
#include "AIBase.h"
#include "ClientPlayer.h"
#include "ActorVision.h"
#include "ActorVehicleAssemble.h"
#include "RiddenComponent.h"
#include "ActorBody.h"

AIBase::~AIBase() 
{
}

bool baseAtkDist(ClientActor *pActor, ClientMob * pMob)
{
	ClientMob *mob = dynamic_cast<ClientMob*>(pMob);
	double dist = 0;
	CollideAABB box;
	pActor->getCollideBox(box);
	if (mob)
	{
		dist = mob->m_Def->AttackDistance*BLOCK_SIZE + mob->m_Def->Width*mob->m_Def->ModelScale / 2 + box.dim.x / 2;
	}
	else
		return false;

	if (pActor->getObjType() == OBJ_TYPE_ROLE)
	{
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(pActor);
		if (player && player->getRidingVehicle())
		{
			ClientActor* attackTarget = pMob->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(player->getRidingVehicle());
			if (attackTarget)
			{
				ActorVehicleAssemble *vehicle = dynamic_cast<ActorVehicleAssemble *>(attackTarget);
				if (vehicle)
				{
					WCoord pos;
					pos = WCoord(pMob->getEyePosition());
					float yaw;
					float pitch;
					yaw = pMob->getLocoMotion()->m_RotateYaw;
					pitch = pMob->getLocoMotion()->m_RotationPitch;
					Rainbow::Vector3f dir;
					PitchYaw2Direction(dir, yaw, pitch);
					MINIW::WorldRay ray;
					ray.m_Origin = pos.toWorldPos();
					ray.m_Dir = dir;
					ray.m_Range = 10 * BLOCK_FSIZE;
					int blockID = 0;
					float t = 0;
					int x, y, z;
					if (vehicle->intersect(ray, t, blockID, x, y, z) && dist >= t)
					{
						return true;
					}
					else
					{
						return false;
					}
				}
			}
		}
		
		if (player)
		{
			auto RidComp = player->getRiddenComponent();
			if (RidComp && RidComp->isRiding())
			{
				auto pRide = RidComp->getRidingActor();
				if (pRide)
				{
					pRide->getCollideBox(box);
					if (mob->m_Def)
						dist = mob->m_Def->AttackDistance*BLOCK_SIZE + mob->m_Def->Width*mob->m_Def->ModelScale * mob->getBody()->getRealScale() / 2 + box.dim.x / 2;
				}
			}
		}
	}
	dist = dist*dist;
	WCoord targetpos = pActor->getLocoMotion()->getPosition();
	if (pMob->getSquareDistToPos(targetpos.x, targetpos.y, targetpos.z) <= dist)
	{
		return true;
	}
	else
	{
		return false;
	}
}

bool baseIsSuitableTarget(ClientActor *target, ClientMob * pMob, bool checkSight)
{
	if (NULL == target || target == pMob)  return false;
	if (target->isInvulnerable(pMob) || target->isDead())  return false;

	ActorLiving *targetLiving = dynamic_cast<ActorLiving *>(target);
	ActorLiving *selfLiving = dynamic_cast<ActorLiving *>(pMob);
	if (targetLiving)
	{
		if (selfLiving->isSameTeam(targetLiving)) return false;

		ClientPlayer *owner = pMob->getTamedOwner();
		if (targetLiving && owner)
		{
			if (owner->isSameTeam(targetLiving))  return false;
		}
	}

	//�ж�HomeDist
	ActorLocoMotion *pLoco = target->getLocoMotion();
	if (!pMob->isInHomeDist(pLoco->m_Position.x, pLoco->m_Position.y, pLoco->m_Position.z))
	{
		return false;
	}

	if (checkSight && !pMob->getVision()->canSeeInAICache(target))
	{
		return false;
	}

	return true;
}