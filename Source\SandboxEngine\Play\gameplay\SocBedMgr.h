#pragma once

#include <vector>
#include <chrono>
#include <string>
#include <OgreWCoord.h>
#include <unordered_map>

#include "world_types.h"

namespace miniw
{
	class bed_data_list;
}

//床数据信息
//踩坑之前这些信息放到Container中,结果地形卸载后拿不到这些信息。而床的功能的超视距的功能
struct SocBedData {
	int owner;													//床的归属者
	int itemid;													//床的物品id
	WCoord pos;													//床的位置
	std::chrono::time_point<std::chrono::system_clock> now;		//记录最后使用的时间戳,方便计算cd
	std::string name;											//睡袋名字
};

class EXPORT_SANDBOXENGINE SocBedMgr
{
public:
	using bed_map_t = std::unordered_map<WCoord, SocBedData*, WCoordHashCoder>;
	using player_bed_t = std::unordered_map<int, std::vector<WCoord>>;

	SocBedMgr();
	~SocBedMgr();
	
	struct SocBedData* GetSocBedDataByPos(const WCoord& pos);

	struct SocBedData* AddBed(const WCoord& pos,int itemid, const std::string& name);

	bool RemoveBed(const WCoord& pos);

	bool SetSocBedDataOwner(const WCoord& pos,int owner);

	bool IsUseBed(const WCoord& pos);

	float GetBedLeftTime(const WCoord& pos);

	bool IsBedMax(int uin);

	bool SetBedName(const WCoord& pos, const std::string& name);

	std::string GetBedName(const WCoord& pos);

	std::string GenerateDefaultBedName(int player_uid);

	const player_bed_t& GetPlayerBed() {
		return m_player_bed;
	};

	void OnLoadDataBase(const void* data, int len);

private:

	bed_map_t m_bed_map;
	player_bed_t m_player_bed;
};