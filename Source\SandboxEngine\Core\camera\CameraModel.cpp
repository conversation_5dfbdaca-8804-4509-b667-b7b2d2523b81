#include "IClientPlayer.h"
#include "BlockScene.h"
#include "CameraModel.h"
#include "world.h"
#include "blocks/BlockMaterialMgr.h"
#include "BaseItemMesh.h"
#include "blocks/special_blockid.h"
#include "DefManagerProxy.h"
#include "IActorBody.h"
#include "PlayManagerInterface.h"
#include "ImportCustomModelMgr.h"
#include "ModelItemMesh.h"
#include "Entity/OgreModelData.h"
#include "Entity/OgreModel.h"
#include "OgreScriptLuaVM.h"
#include "Mesh/LegacyOgreAnimationData.h"
#include "Common/OgreShared.h"
#include "Entity/OgreEntity.h"
#include "IPlayerControl.h"
#include "IClientActor.h"
#include "GameCamera.h"
#include "Math/Quaternionf.h"
#include "Entity/OgreEntity.h"
#include "AssetPipeline/AssetManager.h"
#include "ClientInfoProxy.h"
#include "SandboxCoreDriver.h"

#include "OgreUtils.h"
#include "Mesh/LegacySkinMeshRenderer.h"

#include "BlockMesh.h"
#include "Core/extend/custommodel/CustomModelMgr.h"
#include "WorldManager.h"
#include "AssetPipeline/Prefab/SharedObjectManager.h"
#include "IActorLocoMotion.h"
#include "blocks/container.h"
#include "display/WorldRender.h"
#ifdef ENABLE_PLAYER_CMD_COMMAND
#include "IClientGameManagerInterface.h"
#endif
#include "LegacySequenceMap.h"
#include "LuaInterfaceProxy.h"
#include "InputInfo.h"

using namespace Rainbow;
using namespace MINIW;
using namespace MNSandbox;

#ifdef ENABLE_PLAYER_CMD_COMMAND
bool CameraModel::debug_handPosOffset = false;
#endif
bool CameraModel::IsDebugHandPosOffset()
{
#ifdef ENABLE_PLAYER_CMD_COMMAND
	return debug_handPosOffset;
#endif
	return false;
}
bool CameraModel::debug_lookMyWeaponModel = false;
int CameraModel::ToolModelAnchorid = -1;
static constexpr bool SlideFollowMode = true; //启用滑动跟随模式

CameraModel::CameraModel(int playerindex, int mutatemob, const char* customjson, const char* importmodelkey, std::function<void(bool)> fun) : m_EnbleHandShakeLastFrame(false),
m_HandModel(NULL), m_RenderPitch(0),
m_RenderYaw(0),
m_ToolModel(NULL), m_MoveDirective(NULL),
m_World(NULL),
m_showModel(true),
m_DorsumEntity(NULL),
m_bIsImportModel(false),
m_HomeDirective(NULL),
m_IsShowDirective(false),
m_HandPosOf_begin(Rainbow::Vector3f::zero),
m_HandPosOf_end(Rainbow::Vector3f::zero),
m_HandPosOf_accumTime(0.f),
m_HandPosOf_totalTime(0.f),
m_bIsPrefabHand(false),
m_adsMode(false),
m_lastCameraYaw(0.f),
m_lastCameraPitch(0.f),
m_curSlideHorValue(0.f),
m_curSlideVerValue(0.f),
m_LOrR(0),
m_DOrU(0),
m_CurrentHandAnimId(-1),
m_IsCurrentHandAnimLoop(false),
m_IdleAnimId(-1)
{
	m_HandEntity = nullptr;
	m_IsBlock = false;
	m_BaseOffset = { 15, 0, -10 };
	m_BlockOffset = {5, 0, 20};
	m_PlayerIndex = playerindex;
	m_mutatemob = mutatemob;
	m_customjson = (customjson != NULL) ? std::string(customjson) : std::string("");
	m_importModelKey = (importmodelkey) ? std::string(importmodelkey) : std::string("");
	m_MoveDirective = g_BlockMtlMgr.getModel("particles/movedir.omod");
	m_OnHandModelResLoadedCallback = fun;
	if (m_MoveDirective)
	{
		// todo 
		m_MoveDirective->GetGameObject()->SetLayer(kLayerIndexCustom_FRONT_SCENE);
		//m_MoveDirective->setUserType(1);
		m_MoveDirective->SetInstanceData(Vector4f(1.0f, 1.0f, 0, 0));
		m_MoveDirective->Show(false);
	}
	else
	{
		LOG_WARNING("Can not load <particles/movedir.omod>");
	}


	m_MoveDirViewMode = 0;
	m_ToolModel_left = NULL;
	m_ShakeVector.x = 0;
	m_ShakeVector.y = 0;
	m_ShakeVector.z = 0;

	m_CurrentShakeTime = 0;
	m_EndShakeLerpDuration = 0.8f;
	m_StartShakeLerpDuration = 0.3f;
	m_LerpStartMark = -1;
	m_LerpTimeAccumulate = -1;
	m_ShakePower.x = 2.5f;
	m_ShakePower.y = 1.5f;
	m_ShakePower.z = 0;
	m_OrignalShakeCycle = 0.014f;
	m_ShakeCycle = m_OrignalShakeCycle;

	m_EnbleHandShake = false;
	m_GameCamera = NULL;
	m_ArmPitch = 0;
	m_ArmYaw = 0;
	m_PrePitch = 0;
	m_PreYaw = 0;
	m_HandModelIsLoading = false;
	m_WeaponModelIsLoading = false;

	m_HomeDirective = g_BlockMtlMgr.getModel("particles/movedir2.omod");
	if (m_HomeDirective != NULL)
	{
		// todo_yanxiongjian
		//m_HomeDirective->GetGameObject()->SetLayer(kLayerIndexCustom_FRONT_SCENE);
		/*m_HomeDirective->setUserType(1);*/
		m_HomeDirective->SetInstanceData(Vector4f(1.0f, 1.0f, 0, 0));
		m_HomeDirective->Show(false);
	}
	AsyncLoadHandData();
}

CameraModel::~CameraModel()
{
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_MoveDirective);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_HandModel);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_ToolModel);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_ToolModel_left);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_DorsumEntity);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_HomeDirective);
	m_GameCamera = nullptr;

	UnRegistHandModelResEvent();

	SAFE_DELETE(m_GMOffset);
}

void CameraModel::UnRegistHandModelResEvent()
{
	if (m_HandModelData)
	{
		m_HandModelData->RemoveEvent<CameraModel>(kResLoadedFinished, &CameraModel::OnHandModelResLoaded, this);
		m_HandModelData->RemoveEvent<CameraModel>(kRemoteDownloadFail, &CameraModel::OnHandModelResLoadFaild, this);
	}

	if (m_HandModelAnimData)
	{
		//m_HandModelAnimData->RemoveEvent<CameraModel>(kResAllResLoaded, &CameraModel::OnHandModelResLoaded, this);
		//m_HandModelAnimData->RemoveEvent<CameraModel>(kRemoteDownloadFail, &CameraModel::OnHandModelResLoadFaild, this);
	}
}

void CameraModel::setBobbing(bool b)
{	
	auto iplayer = (dynamic_cast<IClientActor*>(GetIPlayerControl()));
	if (!iplayer)
		return;

	if (m_ToolDef && m_ToolDef->FPSHandWalkSeq > 0)
	{
		int animid = m_IdleAnimId;
		if (b)
		{
			if (iplayer->getRun() && m_ToolDef->FPSHandRunSeq > 0)
			{
				m_BobbingState = 2;
				animid = m_ToolDef->FPSHandRunSeq;
			}
			else
			{
				m_BobbingState = 1;
				animid = m_ToolDef->FPSHandWalkSeq;
			}
		}
		else
		{
			m_BobbingState = 0;
		}
		if (!m_RightButtonDown)
		{
			playHandAnim(animid, 0, 1, -1, 0.1);
		}
		m_EnbleHandShake = false;
	}
	else
	{
		m_BobbingState = 0;
		m_EnbleHandShake = b;
	}
}

void CameraModel::playJumpAnim()
{
	if (m_ToolDef && m_ToolDef->FPSHandJumpSeq > 0 && !m_RightButtonDown)
	{
		playHandAnim(m_ToolDef->FPSHandJumpSeq, 1, 1, -1, 0.1f);
		// m_CanAnimBreak = false;
	}
}


void CameraModel::AsyncLoadHandData()
{
	UnRegistHandModelResEvent();
	m_HandModelData = nullptr;
	m_HandModelAnimData = nullptr;
	m_HandModelAnimData1 = nullptr;
	m_HandModelTex = nullptr;
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_HandModel);
	m_HandModelIsLoading = false;

	SharePtr<RefObject> animData = Model::LoadModelAnimAssetByPathRule("entity/player/fpshand/fpshands/hands");
	//SharePtr<RefObject> animData = Model::LoadModelAnimAssetByPathRule("entity/player/hand");

	if (animData && animData->IsKindOf<Prefab>())
	{
		auto prefab = animData.CastTo<Prefab>();
		SharePtr<SkinAnimContainer> panim = GetShareObjectManager().LoadShareObject(prefab->GetAssetGUID()).CastTo<SkinAnimContainer>();
		m_HandModelAnimData = panim.CastTo<RefObject>();
		m_bIsPrefabHand = true;
	}
	else
	{
		m_HandModelAnimData = animData;
		//另一部分动画数据
		SharePtr<RefObject> animData1 = Model::LoadModelAnimAssetByPathRule("entity/player/hand1");
		if (animData1 && animData1->IsKindOf<AnimationData>())
		{
			m_HandModelAnimData1 = animData1;
		}
	}

	char path[256];
	bool isLoadPlayer12 = false;
	if (m_mutatemob > 0)
	{
		const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(m_mutatemob);
		if (def)
		{
			sprintf(path, "entity/%s/hand", def->Model.c_str());
			//m_HandModel = g_BlockMtlMgr.getModel(path, "entity/player/hand.oanim");

			m_HandModelData = Model::LoadModelAssetByPathRule(path, true);
			//m_HandModelAnimData = GetAssetManager().LoadAssetAsync<Rainbow::AnimationData>("entity/player/hand.oanim");
		}
	}
	else
	{
		if (m_customjson.size() > 0 && m_customjson[0])
		{
			int skin_id = 0;
			int uin = GetIPlayerControl() ? GetIPlayerControl()->GetIUin() : 0;
			SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("RoleSkin_Helper", "GetUseAvtarBodyModel", "is>i", uin, m_customjson.c_str(), &skin_id);
			bool isExhibitionAr = false;
			MINIW::ScriptVM::game()->callFunction("HasArExhibitionAvatar", ">b", &isExhibitionAr);
			if (skin_id > 0 && !isExhibitionAr)
			{
				//如果是老角色转成的皮肤，并且不是商城展示类型，则创建地图内皮肤
				ActorGeniusMgrInterface* geniusMgr = GetActorGeniusMgrInterface();
				if (geniusMgr && geniusMgr->isOldRoleSkin(skin_id))
				{
					int oldRoleModel = geniusMgr->getRoleModelID(skin_id); //新角色皮肤id -> 角色模型id
					//sprintf(path, "entity/player/%d/hand", oldRoleModel);
					sprintf(path, "entity/player/player%.2d/hand", oldRoleModel);
				}
				else
				{
					RoleSkinDef* def = GetDefManagerProxy()->getRoleSkinDef(skin_id);
					if (def)
					{
						sprintf(path, "entity/%d/hand", def->Model);
					}
				}
			}
			else
			{
				sprintf(path, "entity/player/fpshand/fpshands/hands");
				//sprintf(path, "entity/player/player12/hand");
				isLoadPlayer12 = true;
			}

			//m_HandModel = g_BlockMtlMgr.getModel(path, "entity/player/hand.oanim");

			m_HandModelData = Model::LoadModelAssetByPathRule(path, true);
			//m_HandModelAnimData = GetAssetManager().LoadAssetAsync<Rainbow::AnimationData>("entity/player/hand.oanim");
		}
		else if (m_importModelKey.size() > 0 && ImportCustomModelMgr::GetInstancePtr())
		{
			m_HandModel = ImportCustomModelMgr::GetInstancePtr()->getImportHandModel(m_importModelKey.c_str());
			if (m_HandModel)
			{
				m_bIsImportModel = true;
				// todo_yanxiongjian
				//m_HandModel->setIgnoreCull(true);
			}
		}
		else
		{
			int skinid = PlayerIndex2Skin(m_PlayerIndex);
			ActorGeniusMgrInterface* geniusMgr = GetActorGeniusMgrInterface();
			bool isExhibitionAr = false;
			MINIW::ScriptVM::game()->callFunction("HasArExhibitionAvatar", ">b", &isExhibitionAr);
			if (skinid > 0 && geniusMgr && !geniusMgr->isOldRoleSkin(skinid) && !isExhibitionAr)// 由角色转成的皮肤不从皮肤目录索引模型资源，还是用原角色player目录索引角色模型资源
			{
				RoleSkinDef* def = GetDefManagerProxy()->getRoleSkinDef(skinid);
				if (def)
				{
					sprintf(path, "entity/%d/hand", def->Model);
					//m_HandModel = g_BlockMtlMgr.getModel(path, "entity/player/hand.oanim");

					m_HandModelData = Model::LoadModelAssetByPathRule(path, true);
					//m_HandModelAnimData = GetAssetManager().LoadAssetAsync<Rainbow::AnimationData>("entity/player/hand.oanim");

					// todo_yanxiongjian
					int loadparam = RLF_CONVERT_BIT16;
					/*int loadparam = MINIW::ResourceManager::GetInstance().saveMemory()
										? (RLF_CONVERT_BIT16 | RLF_DONT_KEEP_MEMORY_BAK)
										: (RLF_CONVERT_BIT16);*/
					if (skinid >= 34 && skinid <= 36)
					{
						char pathTex[256];
						if (skinid == 34)
							sprintf(pathTex, "entity/%d/male.png", def->Model);
						else
							sprintf(pathTex, "entity/%d/male%d.png", def->Model, skinid - 34);

						m_HandModelTex = GetAssetManager().LoadAssetAsync<Rainbow::Texture2D>(pathTex);

						//m_HandModel->SetTexture("g_DiffuseTex", tex);
					}
				}
			}
			else
			{
				int player2modelIndex = PlayerIndex2Model(m_PlayerIndex);
				if (player2modelIndex > 0)
				{
					sprintf(path, "entity/player/player%.2d/hand", player2modelIndex);
					//m_HandModel = g_BlockMtlMgr.getModel(path, "entity/player/hand.oanim");

					m_HandModelData = Model::LoadModelAssetByPathRule(path, true);
				}
			}
		}
	}


	if (m_HandModelData && !m_HandModelData->IsLoaded())
	{
		m_HandModelIsLoading = true;
		m_HandModelData->AddEvent<CameraModel>(kResLoadedFinished, &CameraModel::OnHandModelResLoaded, this);
		m_HandModelData->AddEvent<CameraModel>(kRemoteDownloadFail, &CameraModel::OnHandModelResLoadFaild, this);
	}

	if (m_HandModelAnimData)
	{
		//m_HandModelAnimData->AddEvent<CameraModel>(kResAllResLoaded, &CameraModel::OnHandModelResLoaded, this);
		//m_HandModelAnimData->AddEvent<CameraModel>(kRemoteDownloadFail, &CameraModel::OnHandModelResLoadFaild, this);
	}

	std::function<void(bool)> callback = m_OnHandModelResLoadedCallback;
	if (!m_HandModelData && !m_HandModel)
	{
		m_OnHandModelResLoadedCallback = nullptr;
		OnHandModelResLoadFaild(nullptr);

		if (callback) {
			callback(false);
		}
	}
	else
	{
		if (m_HandModelData && m_HandModelData->IsLoaded() && m_HandModelAnimData)
		{
			m_OnHandModelResLoadedCallback = nullptr;
			OnHandModelResLoaded(nullptr);

			if (callback) {
				callback(false);
			}
		}
	}
	//缓存Player12的hand
	if (!isLoadPlayer12)
	{
		m_HandModelDataNormal = Model::LoadModelAssetByPathRule("entity/player/player12/hand", false);
		if (m_HandModelDataNormal && m_HandModelDataNormal->IsLoaded())
		{
			if (m_HandModelDataNormal->IsKindOf<ModelData>())
			{
				Rainbow::SharePtr<Rainbow::ModelData> modelData = m_HandModelDataNormal.CastTo<Rainbow::ModelData>();
				if (m_HandModelAnimData && m_HandModelAnimData->IsKindOf<AnimationData>())
				{
					modelData->AddAnimation(m_HandModelAnimData.CastTo<AnimationData>());
				}
				if (m_HandModelAnimData1 && m_HandModelAnimData1->IsKindOf<AnimationData>())
				{
					modelData->AddAnimation(m_HandModelAnimData1.CastTo<AnimationData>());
				}
			}
		}
		else
		{
			m_HandModelDataNormal = nullptr;
		}
	}
}

bool CameraModel::ChangeModelShow(bool normal, int itemid/* = 0*/)
{
	Rainbow::SharePtr<Rainbow::Asset> handModelData = nullptr;
	bool bIsPrefabHand = false;
	const ToolDef* tooldef = m_ToolDef;
	if (tooldef && tooldef->IsModFishRod())
	{
		bIsPrefabHand = true;
		handModelData = Model::LoadModelAssetByPathRule("entity/player/player12/Hand/hand", false);
	}
	else
	{
		if (!m_bIsPrefabHand)
		{
			if (normal == m_isHandModelDataNormal)
				return false;
				
			bIsPrefabHand = false;
			m_isHandModelDataNormal = normal;
			handModelData = normal ? m_HandModelDataNormal : m_HandModelData;
		}
		else
		{
			return true;
		}
	}	

	if (handModelData)
	{
		Rainbow::SharePtr<Rainbow::ModelData> modelData = handModelData.CastTo<Rainbow::ModelData>();

		if (m_World)
		{
			if (m_ToolModel) m_ToolModel->DetachFromScene();
			if (m_ToolModel_left) m_ToolModel_left->DetachFromScene();
			if (m_DorsumEntity) m_DorsumEntity->DetachFromScene();
		}
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_ToolModel);
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_ToolModel_left);
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_DorsumEntity);

		if ((m_bIsPrefabHand == bIsPrefabHand) && (bIsPrefabHand == false))
		{
			Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(m_HandModel);
			legacymodel->Load(modelData);
		}
		else
		{
			if (m_HandModel)
				DESTORY_GAMEOBJECT_BY_COMPOENT(m_HandModel);

			m_HandModel = Model::CreateInstanceFromAsset(modelData.CastTo<Asset>(), false);
			//处理钓鱼的手和鱼竿不在第一人称的渲染层级里面
			if (bIsPrefabHand)
			{
				m_HandModel->SetModelmeshRenderdersToLayer(kLayerIndexCustom_FRONT_SCENE);
			}
			InitHandModelData();
		}

		m_bIsPrefabHand = bIsPrefabHand;

		if (bIsPrefabHand)
		{
			m_HandModel->Show(m_showModel);
			return true;
		}
		else
		{
			Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(m_HandModel);
			auto* animationPlayer = legacymodel->GetModelAnimationPlayer();

			if (animationPlayer)
			{
				animationPlayer->SetCullingMode(IModelAnimationPlayer::kCullingModeAlways);
				animationPlayer->SetEffectDisEnable(false);
			}

			legacymodel->Show(m_showModel);
			return true;
		}
	}
	
	return false;
}

void CameraModel::OnHandModelResLoadFaild(const Rainbow::EventContent* data)
{
	UnRegistHandModelResEvent();
	m_HandModelTex = nullptr;
	m_HandModelData = nullptr;
	m_HandModelAnimData = nullptr;
	m_HandModelAnimData1 = nullptr;
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_HandModel);


	int playerindex = m_PlayerIndex;
	char path[256];
	{
		if (playerindex < -1 || playerindex > 99999)
		{
			LOG_INFO("CameraModel playerindex:%d", playerindex);
			playerindex = 1;
		}
		int skinId = PlayerIndex2Skin(playerindex);
		ActorGeniusMgrInterface* geniusMgr = GetActorGeniusMgrInterface();
		int player2modelIndex = 0;
		if (geniusMgr)// 角色已经从20220907版本开始废弃了，改成了新冒险皮肤，模型还是用角色模型，所以通过皮肤ID找回对应的角色Model 
			player2modelIndex = geniusMgr->getRoleModelID(skinId);

		if (player2modelIndex <= 0)
		{
			LOG_INFO("CameraModel  player2modelIndex:%d", player2modelIndex);
			player2modelIndex = 1;
		}

		sprintf(path, "entity/player/player%.2d/hand", player2modelIndex);
		m_HandModel = GetISandboxActorSubsystem()->LoadActorBodyModel(path, "entity/player/hand.oanim");

		if (m_HandModel == NULL)
		{
			sprintf(path, "entity/player/player%.2d/hand.omod", 1);
			m_HandModel = GetISandboxActorSubsystem()->LoadActorBodyModel(path, "entity/player/hand.oanim");
		}

		InitHandModelData();
		if (m_OnHandModelResLoadedCallback) {
			m_OnHandModelResLoadedCallback(true);
		}
	}
	m_HandModelIsLoading = false;
}

void CameraModel::InitHandModelData()
{
	if (m_HandModel)
	{
		m_HandModel->SetInstanceData(Vector4f(1.0f, 1.0f, 0, 0));
		// todo_yanxiongjian
		/*m_HandModel->clearRenderUsageBits(RU_SHADOWMAP);*/

		m_HandModel->SetModelmeshRenderdersToLayer(kLayerIndexCustom_FRONT_SCENE);

		if (m_HandModelTex)
		{
			m_HandModel->SetTexture("g_DiffuseTex", m_HandModelTex);
		}

		// if (m_World && !m_HandModel->IsInScene())
		// 	m_HandModel->AttachToScene(m_World->getScene());
		if (m_HandEntity && !m_HandEntity->IsInScene() && m_World)
			m_HandEntity->AttachToScene(m_World->getScene());

		ShowInner(m_showModel);

		if (m_HandModel->GetModelAnimationPlayer())
		{
			m_HandModel->GetModelAnimationPlayer()->SetCullingMode(IModelAnimationPlayer::kCullingModeAlways);
			m_HandModel->GetModelAnimationPlayer()->SetEffectDisEnable(false);
		}

		if (m_HandAnimId > -1)
			m_HandModel->PlayAnim(m_HandAnimId);

		//第1人才的手的包围盒有些模型的AB包围盒是错的,导致会一闪一闪不显示,反正手是一直在摄像机前面的,所以可以把包围盒设置最大
		Vector3f minPos = Vector3f(std::numeric_limits<float>::min(), std::numeric_limits<float>::min(), std::numeric_limits<float>::min());
		Vector3f maxPos = Vector3f(std::numeric_limits<float>::max(), std::numeric_limits<float>::max(), std::numeric_limits<float>::max());
		BoxSphereBound bound;
		bound.fromBox(minPos, maxPos);
		auto render = m_HandModel->GetComponent<LegacySkinMeshRenderer>();
		if (render)
		{
			render->SetLocalCustomBounds(bound.getBox());
		}
	}
}

void CameraModel::OnHandModelResLoaded(const Rainbow::EventContent* data)
{
	if (m_HandModelData && m_HandModelData->IsLoaded() && m_HandModelAnimData)
	{
		UnRegistHandModelResEvent();


		Rainbow::Model* pmodel = nullptr;
		if (m_HandModelData->IsKindOf<Prefab>())
		{
			pmodel = Rainbow::Model::CreateInstanceFromAsset(m_HandModelData);
			ModelNew* modelNew = static_cast<ModelNew*>(pmodel);
			if (m_HandModelAnimData->IsKindOf<SkinAnimContainer>())
			{
				modelNew->AddAnimationClips(m_HandModelAnimData.CastTo<SkinAnimContainer>());
			}
			else if (m_HandModelAnimData->IsKindOf<AnimationData>())
			{
				SharePtr<AnimationData> anim = m_HandModelAnimData.CastTo<AnimationData>();
				std::vector<ModelAnimData> anims;
				bool flag = true;
				for (size_t i = 0; i < anims.size(); i++)
				{
					if (anims[i].anim == anim) {
						flag = false;
						break;
					}
				}

				if (flag)
				{
					ModelAnimData animdata;
					animdata.nbonetrack = anim->m_BoneTracks.size();
					animdata.boneids = new short[animdata.nbonetrack];
					animdata.anim = anim;
					animdata.isNeedRemove = false;
					anims.push_back(animdata);

					SharePtr<SkinAnimContainer> conatainer = MakeSharePtr<SkinAnimContainer>();
					conatainer->AddSkeletonAnimationClips(anims);

					for (size_t i = 0; i < anims.size(); i++)
					{
						delete[] anims[i].boneids;
						anims[i].boneids = nullptr;
					}

					if (modelNew)
						modelNew->AddAnimationClips(conatainer);
				}
			}
		}
		else if (m_HandModelData->IsKindOf<ModelData>())
		{
			Rainbow::SharePtr<Rainbow::ModelData> modelData = m_HandModelData.CastTo<Rainbow::ModelData>();
			if (m_HandModelAnimData->IsKindOf<AnimationData>())
			{
				modelData->AddAnimation(m_HandModelAnimData.CastTo<AnimationData>());
				//另一部分动画数据
				if (m_HandModelAnimData1 && m_HandModelAnimData1->IsKindOf<AnimationData>())
				{
					modelData->AddAnimation(m_HandModelAnimData1.CastTo<AnimationData>());
				}
				pmodel = Rainbow::Model::CreateInstanceLegacy(modelData);
			}
		}


		DESTORY_GAMEOBJECT_BY_COMPOENT(m_HandModel);
		m_HandModel = pmodel;
		if (m_HandModel)
		{
			// 创建Entity而不是直接创建Model
        	m_HandEntity = Rainbow::Entity::Create();
			m_HandEntity->Load(m_HandModel);
			m_HandEntity->SetScale(Vector3f(0.5f, 0.5f, 0.5f));
			m_HandEntity->GetGameObject()->SetLayer(kLayerIndexCustom_FRONT_SCENE);
		}

		InitHandModelData();
		if (m_OnHandModelResLoadedCallback) {
			m_OnHandModelResLoadedCallback(true);
		}
	}
	m_HandModelIsLoading = false;
}


void CameraModel::onEnterWorld(World* pworld)
{
	m_World = pworld;
	if (m_World == NULL) return;

	// if (m_HandModel && !m_HandModel->IsInScene())
	// 	m_HandModel->AttachToScene(m_World->getScene());
	if (m_HandEntity && !m_HandEntity->IsInScene())
		m_HandEntity->AttachToScene(m_World->getScene());

	if (m_ToolModel && !m_ToolModel->IsInScene())
	{
		m_ToolModel->AttachToScene(m_World->getScene());
	}
	if (m_ToolModel_left && !m_ToolModel_left->IsInScene())
	{
		m_ToolModel_left->AttachToScene(m_World->getScene());
	}
	if (m_MoveDirective) m_MoveDirective->AttachToScene(m_World->getScene());

	if (pworld->getMapSpecialType() == HOME_GARDEN_WORLD)
	{
		if (m_HomeDirective)
			m_HomeDirective->AttachToScene(m_World->getScene());
	}
	if (m_DorsumEntity)
		m_DorsumEntity->AttachToScene(m_World->getScene());
}

void CameraModel::onLeaveWorld()
{
	if (m_World)
	{
		if (m_HandModel) m_HandModel->DetachFromScene();
		if (m_ToolModel) m_ToolModel->DetachFromScene();
		if (m_ToolModel_left) m_ToolModel_left->DetachFromScene();
		if (m_MoveDirective) m_MoveDirective->DetachFromScene();
		if (m_World->getMapSpecialType() == HOME_GARDEN_WORLD)
		{
			if (m_HomeDirective) m_HomeDirective->DetachFromScene();
		}
		if (m_DorsumEntity)
			m_DorsumEntity->DetachFromScene();
		m_World = NULL;
	}
	HandPosOffsetDebugMode(false);
}

void CameraModel::resetAnim(int seqId)
{
	if (m_HandModel)
	{
		m_HandModel->ResetAnimPlayTrack(seqId);
	}
}

bool CameraModel::hasAnimSeq(int seqId)
{
	return m_HandModel && m_HandModel->HasAnim(seqId);
}

bool CameraModel::hasAnimPlaying(int seqId)
{
	if (!m_HandModel) return false;
	return m_HandModel->HasAnimPlaying(seqId);
}

bool CameraModel::hasAnimPlayEnd(int seqId)
{
	if (!m_HandModel) return false;
	return m_HandModel->HasAnimPlayEnd(seqId);
}

bool CameraModel::playHandAnim(int seqId, int loopMode, float speed, int layer, float crossfade)
{
	if (!m_CanAnimBreak)
	{
		m_NextHandAnimId = seqId;
		m_NextHandAnimLoopMode = loopMode;
		return false;
	}

	if (m_HandAnimId == seqId && loopMode == 0)
		return false;

	LOG_INFO("playHandAnim seqId:%d, loopMode:%d, speed:%f, layer:%d, crossfade:%f", seqId, loopMode, speed, layer, crossfade);
	
	// 如果正在进食，则先播放停止进食动作
	if (m_IsEatAnimPlaying && seqId != 1060015)
	{
		stopEatAnim();
		m_NextHandAnimId = seqId;
		m_NextHandAnimLoopMode = loopMode;
		return false;
	}

	// 记录当前播放的手部动作信息
	m_CurrentHandAnimId = seqId;
	if (loopMode < 0)
	{
		SequenceMap::SeqDesc* smsd = GetSequenceMap().findSequenceDesc(seqId);
		if (smsd)
		{
			loopMode = smsd->loopmode;
			if (smsd->nobreak == 1)
				m_CanAnimBreak = false;
		}
	}
	m_IsCurrentHandAnimLoop = (loopMode == 0); // 0表示循环播放

	if (m_ForceNoCrossfade)
	{
		crossfade = -1;
		m_ForceNoCrossfade = false;
	}

	if (m_GunDef && seqId == m_GunDef->ReloadAnimFps)
	{
		m_CanAnimBreak = false;
		crossfade = -1;
		m_ForceNoCrossfade = true;   // 钉枪的reload跟待机融合也有问题
	}
	if (m_ToolDef && (seqId == m_ToolDef->ToolStartSeq || seqId == m_ToolDef->StartAnimSeq || seqId == m_ToolDef->HandStartSeq))
		m_CanAnimBreak = false;

	
	bool ret = false;
	if (m_HandModel)
		ret = m_HandModel->PlayAnim(seqId, 1.0f, speed, loopMode, layer, crossfade);

	m_HandAnimId = seqId;

	if (m_ToolModel)
	{
		m_ToolModel->playAnim(seqId, loopMode, speed, layer, crossfade);
	}

	return ret;
}

void CameraModel::stopHandAnim(int seqId, bool isreset)
{
	// LOG_INFO("stopHandAnim seqId:%d, isreset:%d", seqId, isreset);
	// 清除当前动作记录
	if (m_CurrentHandAnimId == seqId)
	{
		m_CurrentHandAnimId = -1;
		m_IsCurrentHandAnimLoop = false;
		m_CanAnimBreak = true;
	}

	if (!m_CanAnimBreak)
		return;
	
	m_HandAnimId = -1;
	if (m_HandModel) m_HandModel->StopAnim(seqId, isreset);

	if (m_ToolModel && GetDefManagerProxy()->getGunDef(m_ToolModel->m_ItemID))
	{
		m_ToolModel->stopAnim(seqId);
	}
}
void CameraModel::stopHandAnim(bool isreset)
{
	m_HandAnimId = -1;
	
	// 清除当前动作记录
	m_CanAnimBreak = true;
	m_CurrentHandAnimId = -1;
	m_IsCurrentHandAnimLoop = false;
	
	if (m_HandModel) m_HandModel->StopAnim(isreset);
	if (m_ToolModel && GetDefManagerProxy()->getGunDef(m_ToolModel->m_ItemID))
	{
		m_ToolModel->stopAllAnim();
	}
}

bool CameraModel::SetHandAnimSpeed(int seqId, float speed)
{
	if (!m_HandModel)
		return false;

	return m_HandModel->SetAnimSpeed(seqId, speed);
}

bool CameraModel::SetToolAnimSpeed(int seqId, float speed)
{
	if (!m_ToolModel)
		return false;

	return m_ToolModel->SetAnimSpeed(seqId, speed);
}

bool CameraModel::addAnimFrameEvent(int seqId, int keyFrame, const std::string& eventName, long long objId)
{
	if (m_HandModel && m_HandModel->IsKindOf<ModelLegacy>())
	{
		Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(m_HandModel);
		if (legacymodel != nullptr && legacymodel->GetModelData() && legacymodel->GetAnimPlayer())
		{
			AnimationPlayer* aniPlayer = legacymodel->GetAnimPlayer();
			aniPlayer->SetOwnerObjId(objId);

			SharePtr<ModelData> modelData = legacymodel->GetModelData();
			for (size_t i = 0; i < modelData->GetNumAnim(); i++)
			{
				SharePtr<AnimationData> pAnimData = modelData->getAnimation(i);
				if (pAnimData && pAnimData->hasSequence(seqId) && pAnimData->m_BoneTracks.size() > 0)
				{
					return pAnimData->addFrameEventByKey(seqId, keyFrame * 33, eventName, 1, objId);
				}
			}
		}
	}
	return false;
}

void CameraModel::clearAnimFrameEvent(long long objid)
{
	if (m_HandModel && m_HandModel->IsKindOf<ModelLegacy>())
	{
		Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(m_HandModel);
		if (legacymodel != nullptr && legacymodel->GetModelData())
		{
			SharePtr<ModelData> modelData = legacymodel->GetModelData();
			for (const auto& pAnimData : modelData->m_Anims)
			{
				std::vector<LegacyAnimationFrameEventData>& frameEvents = pAnimData.anim->m_FrameEvents;
				if (objid > 0)
				{
					for (auto it = frameEvents.begin(); it != frameEvents.end();)
					{
						if ((*it).objId == objid)
							it = frameEvents.erase(it);
						else
							++it;
					}
				}
				else
				{
					frameEvents.clear();
				}
			}
		}
	}
}

void CameraModel::clearPendingAnimEvents(int seqId)
{
	if (m_HandModel && m_HandModel->IsKindOf<ModelLegacy>())
	{
		Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(m_HandModel);
		if (legacymodel != nullptr && legacymodel->GetAnimPlayer())
		{
			auto& events = legacymodel->GetAnimPlayer()->m_FrameEvents;
			if (seqId < 0)
			{
				events.clear();
			}
			else
			{
				for (auto it = events.begin(); it != events.end();)
				{
					if ((*it).id == seqId)
						it = events.erase(it);
					else
						++it;
				}
			}
		}
	}
}

void CameraModel::playItemAnim(int anim, int loopMode, float speed, int layer, float crossfade)
{
	// LOG_INFO("playItemAnim seqId:%d, loopMode:%d, speed:%f, layer:%d, crossfade:%f", anim, loopMode, speed, layer, crossfade);
	if (m_ToolModel) m_ToolModel->playAnim(anim, loopMode, speed, layer, crossfade);
	if (m_ToolModel_left) m_ToolModel_left->playAnim(anim, loopMode, speed, layer, crossfade);
}

void CameraModel::stopItemAnim(int anim)
{
	if (m_ToolModel) m_ToolModel->stopAnim(anim);
	if (m_ToolModel_left) m_ToolModel_left->stopAnim(anim);
}

void CameraModel::stopAllItemAnim()
{
	if (m_ToolModel) m_ToolModel->stopAllAnim();
	if (m_ToolModel_left) m_ToolModel_left->stopAllAnim();
}

bool CameraModel::hasToolAnimPlaying(int anim)
{
	if (m_ToolModel == NULL)
		return false;
	return m_ToolModel->hasAnimPlaying(anim);
}

void CameraModel::playItemMotion(const char* motion, bool reset_play, int motion_class, float scale)
{
	if (!isShow())
		return;

	if (m_ToolModel)
		m_ToolModel->playMotion(0,motion, reset_play, motion_class, scale, -1.f, kLayerIndexCustom_FRONT_SCENE);
	if (m_ToolModel_left)
		m_ToolModel_left->playMotion(0,motion, reset_play, motion_class, scale, -1.f, kLayerIndexCustom_FRONT_SCENE);
}

void CameraModel::stopItemMotion(int motion_class)
{
	if (m_ToolModel) m_ToolModel->stopMotion(motion_class);
	if (m_ToolModel_left) m_ToolModel_left->stopMotion(motion_class);
}

void CameraModel::switchItemMode(int index)
{
	if (m_ToolModel)
	{
		bool isUseSkinModel = false;
		if (GetIPlayerControl())
		{
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_SwitchWeaponModel",
				SandboxContext(nullptr)
				.SetData_Number("uin", GetIPlayerControl()->GetIUin())
				.SetData_Number("index", index)
				.SetData_Userdata("BaseItemMesh", "mesh", m_ToolModel)
			);

			isUseSkinModel = (result.IsExecuted() && result.IsSuccessed());
		}
		if (!isUseSkinModel)
		{
			m_ToolModel->switchModel(index);
		}
	}
}

void CameraModel::ShowInner(bool b)
{
	m_showModel = b;
	if (m_ToolModel) m_ToolModel->Show(b);
	if (m_ToolModel_left) m_ToolModel_left->Show(b);
	if (m_DorsumEntity) m_DorsumEntity->Show(b);

	if (!m_HandModel) return;
	if (debug_lookMyWeaponModel)
	{
		m_HandModel->Show(false);
		return;
	}
	if (b && m_MoveDirViewMode == 0)
	{
		if (m_ToolModel)
		{
			const ToolDef* tooldef = m_ToolDef;

			if (m_IsFood || m_GunDef || m_ToolModel->m_ItemID == ITEM_GLOW_STICK || m_ToolModel->m_ItemID == ITEM_GLOW_STICK_USED || m_ToolModel->m_ItemID == ITEM_GLOW_STICK_UNUSED || GetDefManagerProxy()->getCustomGunDef(m_ToolModel->m_ItemID))
			{
				m_ToolModel->Show(true);
				m_HandModel->ShowSkin("hand02", true);
				m_HandModel->Show(true);
			}
			else if (tooldef && (tooldef->IsModFishRod() || tooldef->FPSHandIdleSeq))
			{
				m_ToolModel->Show(true);
				m_HandModel->ShowSkin("hand02", true);
				m_HandModel->Show(true);
			}
			else
			{
				m_ToolModel->Show(true);
				m_HandModel->Show(false);
			}
		}
		else
		{
			m_HandModel->ShowSkin("hand02", false);
			m_HandModel->Show(false);  // TODO 有空手待机后显示
		}
	}
	else
	{
		m_HandModel->Show(false);
	}
}

void CameraModel::show(bool b)
{
	if (m_showModel == b) return;
	ShowInner(b);
}

static float bow_x = 20.0f;
static float bow_z = 0;
static float bow_y = -10.0f;
static float bow_yaw = 70.0f;
static float bow_pitch = 0.0f;
static float bow_roll = 15.0f;
static int arcID = 201;
//static Rainbow::Vector3f item_pos(20.0f, -10.0f, 10.0f);
static Rainbow::Vector3f item_pos(0, 0, 0);

void CameraModel::setCurTool_byGrid(int itemid, const char* iconname, BackPackGrid* grid)
{
	if (m_ToolDef && m_CurToolId == itemid)
		return;
	initToolId(itemid);

	//临时方案，新枪实时换手部模型
	int state = 0;
	if (GetIPlayerControl() && GetIPlayerControl()->IsGunHoldState(GunHoldState::CUSTOMGUN))
	{
		if (ChangeModelShow(true, itemid))
		{
			state = -1;
		}
	}
	else
	{
		if (ChangeModelShow(false, itemid))
		{
			state = 1;
		}
	}

	if (GetIPlayerControl())
	{
		GetIPlayerControl()->PlayGunByState(state);
	}
	setCurTool(itemid, iconname, grid->getUserdataStr().c_str(), grid->hasRuneOrEnchants(), grid->getConstUserDataInt());
}

void CameraModel::initToolId(int itemid)
{
	m_CurToolId = itemid;
	m_ItemDef = GetDefManagerProxy()->getItemDef(itemid);
	m_ToolDef = GetDefManagerProxy()->getOriginalToolDef(itemid);
	m_GunDef = GetDefManagerProxy()->getOrignalGunDef(itemid);
	m_ItemInHandDef = GetDefManagerProxy()->getItemInHandDef(itemid);
	m_IsFood = m_ItemDef && m_ItemDef->UseTarget == ITEM_USE_PRESSFOOD;
	
	m_CanAnimBreak = true;
	m_CurrentHandAnimId = -1;
	m_IsCurrentHandAnimLoop = false;

	if (m_GunDef)
	{
		m_IdleAnimId = m_GunDef->IdleAnimFps;
	}
	else if (m_ToolDef)
	{
		if (m_ToolDef->FPSHandIdleSeq)
		{
			m_IdleAnimId = m_ToolDef->FPSHandIdleSeq;
		}
		else
		{
			m_IdleAnimId = FPS_IDLE;
		}
	}
	else
	{
		if (m_IsFood)
		{
			m_IdleAnimId = 1010001;
		}
		else
		{
			m_IdleAnimId = FPS_IDLE;
		}
	}
}

void CameraModel::setCurTool(int itemid, const char* iconname, const char* userdatastr, bool hasRuneEffect, int userdataInt)
{
	m_WeaponModelIsLoading = true;
	bool isUGCEntity = false;
	if (m_ToolModel)
	{
		if (m_World)
		{
			if (m_ToolModel) m_ToolModel->DetachFromScene();
		}
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_ToolModel);
	}
	if (m_ToolModel_left)
	{
		if (m_World)
		{
			if (m_ToolModel_left) m_ToolModel_left->DetachFromScene();
		}
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_ToolModel_left);
	}
	if (itemid > 0)
	{
		if (m_ToolModel == NULL)
		{
			if (m_ItemDef == NULL) return;
			const ItemDef* def = m_ItemDef;
			float scale = def->WieldScale;
			if (scale == 0) scale = 1.0f;

			if(GetIPlayerControl() == NULL) return;
			if (def->MeshType == CUSTOM_GEN_MESH && def->Icon == "customitem")
			{
				if (CustomModelMgr::GetInstancePtr() && CustomModelMgr::GetInstancePtr()->isBlockCM(def->Model.c_str()))
				{
					scale = 1.0f;
				}
				else
					scale = GetWorldManagerPtr()->m_SurviveGameConfig->custommodelconfig.fpp_scale;
			}
			else if (def->MeshType == FULLY_CUSTOM_GEN_MESH && def->Icon == "fullycustomitem")
				scale = 0.2f;
			else if (def->MeshType == IMPORT_MODEL_GEN_MESH)
				scale = 0.2f;
			else if (def->ID == ITEM_ACTORMODELCRAFT)
				scale = 0.2f;

			if (m_bIsImportModel)
			{
				auto* gunDef = GetDefManagerProxy()->getGunDef(def->ID);
				if (gunDef)
				{
					scale *= 0.6f;
				}
				else
				{
					scale *= 0.8f;
				}

			}

			//m_ToolModel = GetISandboxActorSubsystem()->CreateClientItemModel(itemid, ITEM_MODELDISP_CAMERA, scale);
			if (ITEM_TYPE_VEHICLE == def->Type)
			{
				m_ToolModel = GetISandboxActorSubsystem()->CreateClientItemModel(itemid, ITEM_MODELDISP_CAMERA, 1.0f, 0, NORMAL_MESH, userdatastr);
			}
			else if (IsHookBlockID(itemid) && GetIPlayerControl() && (dynamic_cast<IClientPlayer*>(GetIPlayerControl()))->getHookObj())
			{
				m_ToolModel = GetISandboxActorSubsystem()->CreateClientItemModel(itemid, ITEM_MODELDISP_CAMERA, scale, 1);
			}
			else if (itemid == ITEM_SPRINKLER)
			{
				//������ʣ����� �����userdataInt
				int texIndex = 5;
				MINIW::ScriptVM::game()->callFunction("GetSprinkleModelTextureIndex", "i>i", userdataInt, &texIndex);
				m_ToolModel = GetISandboxActorSubsystem()->CreateClientItemModel(itemid, ITEM_MODELDISP_CAMERA, scale, texIndex);
			}
			else if (itemid == ITEM_UGC_TREE || itemid == ITEM_UGC_RIVER) //ugc特殊道具
			{
				int modelIndex = 0;
				if (GetIPlayerControl())
				{
					modelIndex = (dynamic_cast<IClientPlayer*>(GetIPlayerControl()))->GetTreeItemIndex();
				}
				m_ToolModel = GetISandboxActorSubsystem()->CreateClientItemModel(itemid, ITEM_MODELDISP_CAMERA, 1.0, 0, NORMAL_MESH, userdatastr);
				auto pItemMesh = dynamic_cast<ModelItemMesh*>(m_ToolModel);
				if (pItemMesh)
				{
					Entity* entity = pItemMesh->getEntity();
					if (entity)
					{
						Model* model = entity->GetMainModel();
						if (model)
						{
							model->ShowSkins(false);
							auto mainMesh = model->GetIthModelMeshRenderer(0);
							if (mainMesh) {
								mainMesh->show(true);
							}

							auto theMesh = model->GetIthModelMeshRenderer(modelIndex);
							if (theMesh) {
								char path[512];
								sprintf(path, "itemmods/%d/texture%d.png", itemid, modelIndex);
								theMesh->show(true);
								//Rainbow::Texture* tex = static_cast<Rainbow::Texture*>(ResourceManager::getSingleton().blockLoad(path, RLF_CONVERT_BIT16));
								auto  tex = GetAssetManager().LoadAsset<Rainbow::Texture2D>(path /*, RLF_CONVERT_BIT16 */);
								if (tex) {
									theMesh->SetTexture("g_DiffuseTex", tex);
								}
							}
						}
					}
				}
			}
			else if (itemid == ITEM_COLOR_BRUSH)
			{
				m_ToolModel = GetISandboxActorSubsystem()->CreateClientItemModel(itemid, ITEM_MODELDISP_CAMERA, 1.0, 0, NORMAL_MESH, userdatastr);
				auto pItemMesh = dynamic_cast<ModelItemMesh*>(m_ToolModel);
				if (pItemMesh)
				{
					Entity* entity = pItemMesh->getEntity();
					if (entity)
					{
						Model* model = entity->GetMainModel();
						if (model)
						{
							model->ShowSkins(true);
							auto mainMesh = model->GetIthSkin(1);
							if (mainMesh) {
								mainMesh->show(true);
								char path[512];
								sprintf(path, "itemmods/%d/texture%d.png", itemid, 1);
								auto  tex = GetAssetManager().LoadAsset<Rainbow::Texture2D>(path /*, RLF_CONVERT_BIT16 */);
								if (tex) {
									mainMesh->SetTexture("g_DiffuseTex", tex);
								}

								auto color = dynamic_cast<IClientPlayer*>(GetIPlayerControl())->getSelectedColor();
								Rainbow::ColorQuad cq(color);
								ColorRGBAf rgb(cq.r / 255.0f, cq.g / 255.0f, cq.b / 255.0f, 1.0f);
								mainMesh->SetOverlayColor(rgb);
							}
						}
					}
				}
			}
			else
			{
				ITEM_MODELDISP_TYPE showType = ITEM_MODELDISP_CAMERA;
				if (def->MeshType == ICON_GEN_MESH && !isDoubleWeapon(itemid))
				{
					showType = ITEM_MODELDISP_HAND;
				}
				//���ȴ���Ƥ��ģ��
				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_CreateSkinWeaponModel",
					SandboxContext(nullptr)
					.SetData_Number("itemid", itemid)
					.SetData_Number("uin", GetIPlayerControl() ? GetIPlayerControl()->GetIUin() : 0)
					.SetData_Number("displayType", showType));

				if (result.IsExecSuccessed())
					m_ToolModel = (BaseItemMesh*)result.GetData_Userdata("WeaponSkinModelItemMeshLua", "mesh");

				//û��ʹ��Ƥ����ʹ��ԭƤ��
				if (m_ToolModel == NULL)
				{
					if (isDoubleWeapon(itemid))//双持武器ID
					{
						const CraftingDef* craftDef = GetDefManagerProxy()->findCrafting(itemid);
						if (craftDef && craftDef->MaterialID[0])
							m_ToolModel = GetISandboxActorSubsystem()->CreateClientItemModel(craftDef->MaterialID[0], ITEM_MODELDISP_CAMERA, 1.0, 0, NORMAL_MESH, userdatastr);
						if (craftDef && craftDef->MaterialID[1])//第一人称视角左手武器
						{
							m_ToolModel_left = GetISandboxActorSubsystem()->CreateClientItemModel(craftDef->MaterialID[1], ITEM_MODELDISP_CAMERA, 1.0, 0, NORMAL_MESH, userdatastr);
							m_ToolModel_left->m_ItemID = craftDef->MaterialID[1];
						}

					}
					else
					{
						m_ToolModel = GetISandboxActorSubsystem()->CreateClientItemModel(itemid, showType, 1.0, 0, NORMAL_MESH, userdatastr, kLayerIndexCustom_FRONT_SCENE);  //ClientItem::createItemModel(itemid, showType, 1.0, 0, NORMAL_MESH, userdatastr, kLayerIndexCustom_FRONT_SCENE);
					}

					//m_ToolModel->setModelitemDepthFunc(kDepthFuncAlways);
				}

			}

			if (m_ToolModel == NULL) return;
			isUGCEntity = m_ToolModel->isUGCEntity();
			m_ToolModel->m_ItemID = itemid;
			if (itemid > 0 && m_ToolModel && GetIPlayerControl())
			{
				MNSandbox::SandboxResult result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TriggerGlobalData_PlayerHandEffect",
					MNSandbox::SandboxContext(nullptr).SetData_Number("uin", GetIPlayerControl()->GetIUin()).SetData_Number("view", 2));
			}
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_IsUseSkin",
				SandboxContext(nullptr)
				.SetData_Number("uin", GetIPlayerControl() ? GetIPlayerControl()->GetIUin() : 0)
				.SetData_Number("itemid", def->ID)
			);
			bool isUseWeaponSkin = (result.IsExecuted() && result.IsSuccessed());

			if (IsBowItem(itemid))
			{
				// todo_yanxiongjian
				//m_ToolModel->clearRenderUsageBits(RU_SHADOWMAP);
			}

			if (itemid == ITEM_COLORED_GUN || itemid == ITEM_COLORED_EGG || itemid == ITEM_COLORED_EGG_SMALL)
			{
				if (itemid == ITEM_COLORED_EGG || itemid == ITEM_COLORED_EGG_SMALL)
				{
					MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "ii", 0, 0);
				}
			}
			//创造模式和编辑模式，染色方块可以变色 by：Jeff
			if (GetIPlayerControl() && m_World && (g_WorldMgr->isCreativeMode() || g_WorldMgr->isGameMakerMode()) && IsDyeableBlock(itemid))
			{
				unsigned int color = 0;
				int colordata = 0;
				sscanf(userdatastr, "%d", &colordata);
				MINIW::ScriptVM::game()->callFunction("GetColorFromBlockInfo", "ii>i", itemid, colordata, &color);
				dynamic_cast<IClientPlayer*>(GetIPlayerControl())->setSelectedColor(color);
				MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "ii", 0, 0);
			}
			bool isBlock = false;
			const ItemDef* itemdef = m_ItemDef;
			if (itemdef != NULL && itemdef->Icon.empty() && (itemid < 4096 || IsArcBlock(itemid)))
			{
				isBlock = true;
			}

			m_IsBlock = isBlock;
			if (isBlock)
			{
				m_ToolModel->SetRotation(40, -8, 0);
			}
			else
			{
				m_ToolModel->SetRotation(0, 0, 0);
			}
			if (m_ItemInHandDef)
			{
				auto pos = m_ItemInHandDef->FPSRightItemPosData.Pos;
				m_ToolModel->SetPosition(WorldPos(pos.x, pos.y, pos.z));
				auto rot = m_ItemInHandDef->FPSRightItemPosData.Rot;
				m_ToolModel->SetRotation(rot.x, rot.y, rot.z);
			}

			//m_ToolModel->SetSRTFather(m_HandModel, anchorid);
			if (!isUGCEntity && m_HandModel)
			{
				//主武器
				int mainToolSlot = 101;
				if (m_ItemInHandDef && m_ItemInHandDef->FPSSlotId > 0)
				{
					mainToolSlot = m_ItemInHandDef->FPSSlotId;
				}

				//第1人称的手持模型的包围盒有些模型的AB包围盒是错的,导致会一闪一闪不显示,手持模型是一直在摄像机前面的,所以可以把包围盒设置最大
				if (m_ToolModel && m_ToolModel->IsKindOf<ModelItemMesh>())
				{
					ModelItemMesh* itemmodel = static_cast<ModelItemMesh*>(m_ToolModel);
					if (itemmodel && itemmodel->GetModel())
					{
						Vector3f minPos = Vector3f(std::numeric_limits<float>::min(), std::numeric_limits<float>::min(), std::numeric_limits<float>::min());
						Vector3f maxPos = Vector3f(std::numeric_limits<float>::max(), std::numeric_limits<float>::max(), std::numeric_limits<float>::max());
						BoxSphereBound bound;
						bound.fromBox(minPos, maxPos);
						Rainbow::Model *pModel = itemmodel->GetModel();
						auto render = pModel->GetComponent<LegacySkinMeshRenderer>();
						if (render)
						{
							render->SetLocalCustomBounds(bound.getBox());
						}
					}
				}

				m_HandModel->AttachNodeToBone(m_ToolModel->GetTransform(), mainToolSlot);
				m_ToolModel->SetLayer(kLayerIndexCustom_FRONT_SCENE);

				//副武器
				if (m_ToolModel_left)
				{
					m_HandModel->AttachNodeToBone(m_ToolModel_left->GetTransform(), arcID);
					m_ToolModel_left->SetLayer(kLayerIndexCustom_FRONT_SCENE);
				}
			}
		
			// 三棱柱block
			if (IsTriangularPrismBlock(def->ID))
			{
				int yaw, pitch, roll;
				MINIW::ScriptVM::game()->callFunction("GetBlockTriangularPrismEular", ">iii", &yaw, &pitch, &roll);
				m_ToolModel->SetRotation(float(yaw), float(pitch), float(roll));
			}

			if (def->MeshType == CUSTOM_GEN_MESH)
			{
				m_ToolModel->setMeshModel(true);
				if (def->Icon == "customitem")
				{
					if (CustomModelMgr::GetInstancePtr() && CustomModelMgr::GetInstancePtr()->isBlockCM(def->Model.c_str()))
					{
					}
					else
					{
						m_ToolModel->SetPosition(WorldPos(GetWorldManagerPtr()->m_SurviveGameConfig->custommodelconfig.fpp_x, \
							GetWorldManagerPtr()->m_SurviveGameConfig->custommodelconfig.fpp_y, \
							GetWorldManagerPtr()->m_SurviveGameConfig->custommodelconfig.fpp_z));
					}

				}
				else if (def->Icon == "customegg")
				{
					m_ToolModel->SetScale(0.4f);
				}

			}
			else if (FULLY_CUSTOM_GEN_MESH == def->MeshType && !isUseWeaponSkin)
			{
				m_ToolModel->SetScale(Rainbow::Vector3f(0.25f, 0.25f, 0.25f));
			}
			else if (VEHICLE_GEN_MESH == def->MeshType)
			{
				m_ToolModel->SetScale(Rainbow::Vector3f(0.5f, 0.5f, 0.5f));
				m_ToolModel->SetPosition(Rainbow::Vector3f(-20, 0, -20));
			}

			if (m_bIsImportModel && !isUseWeaponSkin)
			{
				auto* gunDef = GetDefManagerProxy()->getGunDef(def->ID);
				if (gunDef)
				{
					//m_ToolModel->setScale(Rainbow::Vector3f(m_Scale, m_Scale, m_Scale));
					m_ToolModel->SetRotation(5, 15, 10);
					m_ToolModel->SetPosition(WorldPos(70, 0, -50));
				}
				else
				{
					//m_ToolModel->setScale(Rainbow::Vector3f(m_Scale, m_Scale, m_Scale));
					if (isBlock)
					{
						m_ToolModel->SetRotation(47, 15, -8);
					}
					else
					{
						m_ToolModel->SetRotation(2, 15, -8);
					}
					m_ToolModel->SetPosition(WorldPos(100, -140, 0));
				}
			}

			//��԰�����ֳ�ģ��λ������
			bool bFindHomeItemDef = false;
#ifdef MODULE_FUNCTION_ENABLE_HOMELAND
			bFindHomeItemDef = GetDefManagerProxy()->findHomeItemDefFunctionId(itemid, HOMELAND_FUNC_GET_PET);
#endif
			if (bFindHomeItemDef)
			{
				m_ToolModel->SetPosition(WorldPos(0, 50, 0));
			}

			if (m_ToolModel)
			{
				ModelItemMesh* itemmodel = dynamic_cast<ModelItemMesh*>(m_ToolModel);

				if (itemmodel && itemmodel->getEntity() && itemmodel->getEntity()->GetMainModel())
					itemmodel->getEntity()->SetInstanceData(Vector4f(1.0f, 1.0f, 0, 0));
			}
		}

		const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
		if (hasRuneEffect || (def && def->IconEffect > 0))
		{
			if (m_ToolModel) m_ToolModel->setOverlay(0);
		}
		stopWeaponMotion(20000);
		if (def && def->HandEffect[0])
		{
			if (def->HandEffectScale < 0.001f)
			{
				playWeaponMotion(def->HandEffect, true, 20000, 1.0f);
			}
			else
			{
				playWeaponMotion(def->HandEffect, true, 20000, def->HandEffectScale);
			}
		}

		if (m_World && m_World->GetWorldRenderer() && !m_World->IsUGCEditMode())
		{
			m_World->GetWorldRenderer()->SetWeaponItemType(def->MeshType);
		}
	}

	//新枪械(模型)部件组装
	if (isUGCEntity)
	{
		GetISandboxActorSubsystem()->BindHandModelForUGCEntity(this);
	}
	else
	{
		if (m_ToolModel)
		{
			m_ToolModel->SetLayer(kLayerIndexCustom_FRONT_SCENE);
		}
		if (m_ToolModel_left)
		{
			m_ToolModel_left->SetLayer(kLayerIndexCustom_FRONT_SCENE);
		}
		m_WeaponModelIsLoading = false;
	}

	bool b = m_showModel;
	m_showModel = !b;
	show(b);

	if (m_ToolDef && m_ToolDef->StartAnimSeq)
	{
		playHandAnim(m_ToolDef->StartAnimSeq, 1);
		m_CanAnimBreak = false;
	}
}

void CameraModel::setCurDorsum(int itemid)
{
	if (itemid == ITEM_FIRE_ROCKET)	//��ʱֻ��Ҫ����������,������Ʒû��Ҫ
	{
		if (m_DorsumEntity)
		{
			char modelPath[128];
			sprintf(modelPath, "itemmods/%d/body.omod", itemid);

			if (!(m_DorsumEntity->GetResPath() == modelPath))
			{
				if (m_World)
				{
					if (m_DorsumEntity) m_DorsumEntity->DetachFromScene();
				}
				// todo if release m_DorsumEntity ? 
				// OGRE_RELEASE(m_DorsumEntity);
				m_DorsumEntity = NULL;
			}
		}

		if (m_DorsumEntity == NULL)
		{
			int anchorid = 0;
			char modelPath[128];
			sprintf(modelPath, "itemmods/%d/body.omod", itemid);
			m_DorsumEntity = g_BlockMtlMgr.getEntity(modelPath);

			AssertMsg(m_DorsumEntity, "m_DorsumEntity is Null!");

			m_DorsumEntity->SetRotation(0, 0, 0);
			m_DorsumEntity->SetPosition(WorldPos(0, -350, -350));
			m_DorsumEntity->GetMainModel()->SetInstanceData(Vector4f(1.0f, 1.0f, 0, 0));
			m_DorsumEntity->PlayMotion("Chupinazo");
			m_DorsumEntity->SetScale(Rainbow::Vector3f(0.2f, 0.2f, 0.2f));

			if (m_HandModel)
			{
				m_HandModel->AttachNodeToBone(m_DorsumEntity->GetTransform(), anchorid);
			}
			//m_DorsumEntity->SetSRTFather(m_HandModel, anchorid);

			m_DorsumEntity->GetGameObject()->SetLayer(kLayerIndexCustom_FRONT_SCENE);
			if (m_World)
			{
				m_DorsumEntity->AttachToScene(m_World->getScene());
			}
		}
	}
	else
	{
		if (m_DorsumEntity)
		{
			if (m_World)
			{
				m_DorsumEntity->DetachFromScene();
			}
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_DorsumEntity);
			m_DorsumEntity = NULL;
		}
	}
}

void CameraModel::playWeaponMotion(const char* motion, bool reset_play, int motion_class, float scale)
{
	if (m_ToolModel)
	{
		dynamic_cast<BaseItemMesh*>(m_ToolModel)->playMotion(0,motion, reset_play, motion_class, scale);
	}
	if (m_ToolModel_left)
	{
		dynamic_cast<BaseItemMesh*>(m_ToolModel_left)->playMotion(0,motion, reset_play, motion_class, scale);
	}
}

void CameraModel::stopWeaponMotion(int motion_class)
{
	if (m_ToolModel)
	{
		dynamic_cast<BaseItemMesh*>(m_ToolModel)->stopMotion(motion_class);
	}
	if (m_ToolModel_left)
	{
		dynamic_cast<BaseItemMesh*>(m_ToolModel_left)->stopMotion(motion_class);
	}
}

void CameraModel::resetShakeTime()
{
	m_CurrentShakeTime = 0;
}
void CameraModel::update(float dtime, const Rainbow::WorldPos& eyepos, const Rainbow::Quaternionf& cam_rot)
{
	if (m_HandModel == nullptr) return;

	// 检查手部动作状态，如果非循环动作播放结束则播放待机动作
	checkAndPlayIdleAnim();

	// todo_chenyu temp add
	/*{
		m_PrePitch = m_ArmPitch;
		m_PreYaw = m_ArmYaw;

		if (m_GameCamera == NULL) return;
		float deltaPitch = m_GameCamera->m_RotatePitch - m_ArmPitch;
		float deltaYaw = m_GameCamera->m_RotateYaw - m_ArmYaw;
		m_ArmPitch += deltaPitch * 0.5f;
		m_ArmYaw += deltaYaw * 0.5f;
	}*/

	Quaternionf rot = cam_rot;

	Rainbow::Vector3f positionOffset(20.0f, 0, 30.0f);
	//Quaternionf rotationOffset = Quaternionf::identity;
	//// ���������Ԫ���˷���Ҫ����˳��
	//rotationOffset = cam_rot * rotationOffset;
	Quaternionf rotationOffset = cam_rot;
	Rainbow::Vector3f shakeOffset;

	bool isNewGun = (bool)(GetIPlayerControl() && GetIPlayerControl()->GetCustomGunDef());
	float renderPitch = 0.f;
	float renderYaw = 0.f;
	if (isNewGun && m_GameCamera)
	{
		renderPitch = m_GameCamera->getRotatePitch();
		renderYaw = m_GameCamera->getRotateYaw();
	}
	else
	{
		float partialTick = GetClientInfoProxy()->getPartialTick();
		renderPitch = m_PrePitch + (m_ArmPitch - m_PrePitch) * partialTick;
		renderYaw = m_PreYaw + (m_ArmYaw - m_PreYaw) * partialTick;
	}

	Quaternionf quat, riderot;
	quat = AngleEulerToQuaternionf(Vector3f(renderPitch, renderYaw, 0));
	if (m_GameCamera && m_GameCamera->GetPlayer())
	{
		IPlayerControl* playerCtl = m_GameCamera->GetPlayer();

		auto riddencomponent = dynamic_cast<IClientActor*>(playerCtl)->getActorComponent(ComponentType::COMPONENT_RIDDEN);
		if (riddencomponent)
		{
			bool result = riddencomponent->Event2().Emit<Rainbow::Quaternionf&, Rainbow::Quaternionf, IPlayerControl*>("Ridden_BindRot",
				quat, riderot, playerCtl);
			Assert(result);
		}
		/*RiddenComponent* playerRidComp = playerCtl->getRiddenComponent();
		if (playerRidComp && playerRidComp->getRidingActor())
		{
			RiddenComponent* ridingComp = playerRidComp->getRidingActor()->getRiddenComponent();
			if (ridingComp && ridingComp->getRiddenBindRot(playerCtl, riderot))
			{
				quat = riderot * quat;
			}
		}*/
	}

	//位置的额外偏移
	Rainbow::Vector3f eyePosOff{ Rainbow::Vector3f::zero };
#ifdef ENABLE_PLAYER_CMD_COMMAND
	if (debug_handPosOffset)
	{
		eyePosOff = m_eyePosOffDebugMode;
	}
	else
#endif
	{
		UpdateHandPosOffset(dtime, eyePosOff);
	}
	if (SlideFollowMode && isNewGun && m_GameCamera)
	{
		//*** 滑动跟随模式 ***//
		float thresholdValueHor = 15.f;
		float thresholdValueVer = 20.f;
		float slideSpeed = 8.f;
		float slideRange = 3.f;
		ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();
		if (lua_const)
		{
			thresholdValueHor = m_adsMode ? lua_const->handFollow_thresholdValueHor_ads : lua_const->handFollow_thresholdValueHor_hip;
			thresholdValueVer = m_adsMode ? lua_const->handFollow_thresholdValueVer_ads : lua_const->handFollow_thresholdValueVer_hip;
			slideSpeed = m_adsMode ? lua_const->handFollow_slideSpeed_ads : lua_const->handFollow_slideSpeed_hip;
			slideRange = m_adsMode ? lua_const->handFollow_slideRange_ads : lua_const->handFollow_slideRange_hip;
		}
		//方向判断
		int8_t isRotateHorizontal = 0; //1右转，-1左转
		int8_t isRotateVertical = 0; //1上转，-1下转
		float curCameraYaw = m_GameCamera->getRotateYaw();
		float curCameraPitch = m_GameCamera->getRotatePitch();
		float yawDis = Abs(curCameraYaw - m_lastCameraYaw);
		float pitchDis = Abs(curCameraPitch - m_lastCameraPitch);
		if (curCameraYaw > m_lastCameraYaw)
		{
			isRotateHorizontal = yawDis < 180.f ? 1 : -1;
		}
		else if (curCameraYaw < m_lastCameraYaw)
		{
			isRotateHorizontal = yawDis < 180.f ? -1 : 1;
		}
		if (curCameraPitch > m_lastCameraPitch)
		{
			isRotateVertical = -1;
		}
		else if (curCameraPitch < m_lastCameraPitch)
		{
			isRotateVertical = 1;
		}
		m_lastCameraYaw = curCameraYaw;
		m_lastCameraPitch = curCameraPitch;
		//水平惯性
		if (yawDis > 180.f) yawDis = 360.0 - yawDis;
		if (yawDis > thresholdValueHor && isRotateHorizontal != 0 && isRotateHorizontal != m_LOrR)
		{
			m_LOrR = isRotateHorizontal;
		}
		if (m_LOrR != 0)
		{
			if (m_LOrR == 1)
			{
				if (m_curSlideHorValue >= kPI)
					m_LOrR = m_curSlideHorValue = 0;
			}
			else if (m_LOrR == -1)
			{
				if (m_curSlideHorValue <= -kPI)
					m_LOrR = m_curSlideHorValue = 0;
			}
			if (isRotateHorizontal == 0)
			{
				m_curSlideHorValue += dtime * slideSpeed * (float)m_LOrR;
			}
			else
			{
				if (m_LOrR == 1 && m_curSlideHorValue <= kHalfPI)
				{
					m_curSlideHorValue += dtime * slideSpeed;
				}
				else if (m_LOrR == -1 && m_curSlideHorValue >= -kHalfPI)
				{
					m_curSlideHorValue -= dtime * slideSpeed;
				}
			}
			eyePosOff.x -= sin(m_curSlideHorValue) * slideRange;
		}
		//垂直惯性
		if (pitchDis > 180.f) pitchDis = 180.f;
		if (pitchDis > thresholdValueVer && isRotateVertical != 0 && isRotateVertical != m_DOrU)
		{
			m_DOrU = isRotateVertical;
		}
		if (m_DOrU != 0)
		{
			if (m_DOrU == 1)
			{
				if (m_curSlideVerValue >= kPI)
					m_DOrU = m_curSlideVerValue = 0;
			}
			else if (m_DOrU == -1)
			{
				if (m_curSlideVerValue <= -kPI)
					m_DOrU = m_curSlideVerValue = 0;
			}
			if (isRotateVertical == 0)
			{
				m_curSlideVerValue += dtime * slideSpeed * (float)m_DOrU;
			}
			else
			{
				if (m_DOrU == 1 && m_curSlideVerValue <= kHalfPI)
				{
					m_curSlideVerValue += dtime * slideSpeed;
				}
				else if (m_DOrU == -1 && m_curSlideVerValue >= -kHalfPI)
				{
					m_curSlideVerValue -= dtime * slideSpeed;
				}
			}
			eyePosOff.y -= sin(m_curSlideVerValue) * slideRange;
		}
	}
	else
	{
		m_lastCameraYaw = m_lastCameraPitch = 0.f;
		m_curSlideHorValue = m_curSlideVerValue = 0.f;
		m_LOrR = m_DOrU = 0;
	}

	Vector3f offset = eyePosOff + (m_ItemInHandDef ? m_ItemInHandDef->FPSHandPos : m_GMOffset ? *m_GMOffset : (m_IsFood ? Vector3f::zero : m_BaseOffset));
	if (m_IsBlock)
	{
		offset += m_BlockOffset;
	}
	
	Rainbow::Vector3f eyePosOff3 = RotateVectorByQuat(cam_rot, offset);
	//设置旋转
	if (!debug_lookMyWeaponModel && m_GMBind && m_HandEntity)
	{
		if (!SlideFollowMode && isNewGun && !m_adsMode)
		{
			//转角跟随模式
			static float THRESHOLD_VALUE = 0.1f; //触发阈值
			static float SMOOTH_AMOUT = 15.0f; //手臂摆动平滑值
	
			Quaternionf quatHand = m_HandEntity->GetRotation();
			float adis = AngularDistance(quatHand, quat);
			if (adis > THRESHOLD_VALUE)
			{
				quatHand = Slerp(quatHand, quat, clamp01((adis - THRESHOLD_VALUE) / adis));
			}
			quatHand = Slerp(quatHand, quat, clamp01(dtime * SMOOTH_AMOUT));
			m_HandEntity->SetRotation(quatHand);
		}
		else
		{
			quat = AngleEulerToQuaternionf(Vector3f(renderPitch + m_GMPitch, renderYaw + m_GMYaw, 0));
			m_HandEntity->SetRotation(quat);
		}
	}
	if (m_EnbleHandShake && !m_EnbleHandShakeLastFrame)
	{
		m_LerpStartMark = GetTimeSinceStartup();
	}

	if (!m_EnbleHandShake && m_EnbleHandShakeLastFrame)
	{
		m_LerpStartMark = GetTimeSinceStartup();
	}

	//新枪械对手臂摇晃幅度的影响
	float adsModeShakeOffset = 1.0f;
	float adsModeShakeSpeed = 1.0f;
	if (GetIPlayerControl())
	{
		GetIPlayerControl()->GetArmShakeParam(adsModeShakeOffset, adsModeShakeSpeed);
	}
	
	m_CurrentShakeTime += dtime * adsModeShakeSpeed;
	if (m_EnbleHandShake)
	{
		if (GetIPlayerControl() && GetIPlayerControl()->GetPlayerControlSneaking())
		{
			m_ShakeCycle = 2 * m_OrignalShakeCycle;
		}

		else if (GetIPlayerControl() && GetIPlayerControl()->GetPlayerControlRun())
		{
			m_ShakeCycle = 0.75f * m_OrignalShakeCycle;
		}
		else
		{
			m_ShakeCycle = m_OrignalShakeCycle;
		}

		m_ShakeVector.x = m_ShakePower.x * Rainbow::Sin(2 * m_CurrentShakeTime * ONE_PI / m_ShakeCycle);
		m_ShakeVector.y = /*2 * m_ShakePower.y*/ -2 * m_ShakePower.y * Abs(Cos(2 * m_CurrentShakeTime * ONE_PI / m_ShakeCycle));
		m_ShakeVector.z = 0;

		if (m_LerpStartMark >= 0 && GetTimeSinceStartup() - m_LerpStartMark < m_StartShakeLerpDuration)
		{
			m_ShakeVector = Lerp(Rainbow::Vector3f::zero, m_ShakeVector, (GetTimeSinceStartup() - m_LerpStartMark) / m_StartShakeLerpDuration);
		}
		else
		{
			m_LerpStartMark = -1;
		}

		shakeOffset = RotateVectorByQuat(rotationOffset, m_ShakeVector);
		//rotationOffset.rotate(shakeOffset, m_ShakeVector);
		shakeOffset *= adsModeShakeOffset;
		if (m_HandEntity && !debug_lookMyWeaponModel && m_GMBind) m_HandEntity->SetPosition(eyepos + shakeOffset + eyePosOff3);
	}
	else
	{
		if (m_LerpStartMark >= 0 && GetTimeSinceStartup() - m_LerpStartMark < m_EndShakeLerpDuration)
		{
			m_ShakeVector = Lerp(m_ShakeVector, Rainbow::Vector3f::zero, (GetTimeSinceStartup() - m_LerpStartMark) / m_EndShakeLerpDuration);

			shakeOffset = RotateVectorByQuat(rotationOffset, m_ShakeVector);
			//rotationOffset.rotate(shakeOffset, m_ShakeVector);
			shakeOffset *= adsModeShakeOffset;
			if (m_HandEntity && !debug_lookMyWeaponModel && m_GMBind)  m_HandEntity->SetPosition(eyepos + shakeOffset + eyePosOff3);
		}
		else
		{
			if (m_HandEntity && !debug_lookMyWeaponModel && m_GMBind)  m_HandEntity->SetPosition(eyepos + eyePosOff3);
			m_LerpStartMark = -1;
		}
	}
	m_EnbleHandShakeLastFrame = m_EnbleHandShake;

	if (m_HandModel)
	{
		//const Vector3f mm(hscx, hscy, hscz);
		//m_HandModel->SetScale(mm);
		m_HandModel->UpdateTick(TimeToTick(dtime));
	}

	if (m_DorsumEntity)
	{
		m_DorsumEntity->UpdateTick(TimeToTick(dtime));
	}

	if (m_ToolModel)
	{
		/*m_ToolModel->updateWorldCache();*/

		m_ToolModel->UpdateTick(TimeToTick(dtime));

		if (m_World)
		{
			Vector4f lightparam(0, 0, 0, 0);
			if (m_World) m_World->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(eyepos));
			if (m_ToolModel && m_ToolModel->IsKindOf<BlockMesh>())
			{
				BlockMesh* blockmesh = static_cast<BlockMesh*>(m_ToolModel);
				blockmesh->SetItemInstanceData(lightparam);
				//blockmesh->SetUseBlockVertexLight(true);
			}

			if (m_ToolModel && m_ToolModel->IsKindOf<ModelItemMesh>())
			{
				ModelItemMesh* itemmodel = static_cast<ModelItemMesh*>(m_ToolModel);
				if (itemmodel && itemmodel->getEntity() && itemmodel->getEntity()->GetMainModel())
				{
					itemmodel->getEntity()->SetInstanceData(lightparam);
					itemmodel->getEntity()->SetInstanceAmbient(ColourValue::ZERO);
					//itemmodel->getEntity()->GetMainModel()->SetInstanceData(lightparam);
				}
			}
		}
	}
	if (m_ToolModel_left)
	{
		/*m_ToolModel->updateWorldCache();*/

		m_ToolModel_left->UpdateTick(TimeToTick(dtime));

		if (m_World)
		{
			Vector4f lightparam(0, 0, 0, 0);
			if (m_World) m_World->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(eyepos));
			if (m_ToolModel_left && m_ToolModel_left->IsKindOf<BlockMesh>())
			{
				BlockMesh* blockmesh = static_cast<BlockMesh*>(m_ToolModel_left);
				blockmesh->SetItemInstanceData(lightparam);
				//blockmesh->SetUseBlockVertexLight(true);
			}

			if (m_ToolModel_left && m_ToolModel_left->IsKindOf<ModelItemMesh>())
			{
				ModelItemMesh* itemmodel = static_cast<ModelItemMesh*>(m_ToolModel_left);
				if (itemmodel && itemmodel->getEntity() && itemmodel->getEntity()->GetMainModel())
				{
					itemmodel->getEntity()->SetInstanceData(lightparam);
					itemmodel->getEntity()->SetInstanceAmbient(ColourValue::ZERO);
					//itemmodel->getEntity()->GetMainModel()->SetInstanceData(lightparam);
				}
			}
		}
	}

	// 引导提示箭头
	if (m_MoveDirective && m_MoveDirective->IsShow())
	{
		positionOffset = Rainbow::Vector3f(0.f, -50.0f, 300.0f);
		if (m_MoveDirViewMode == 1)
			positionOffset = Rainbow::Vector3f(0.f, 0.0f, 600.0f);

		positionOffset = RotateVectorByQuat(rot, positionOffset);

		WorldPos pos = eyepos + positionOffset;
		m_MoveDirective->SetPosition(pos);

		m_MoveDirective->UpdateTick(TimeToTick(dtime));

		positionOffset = m_MoveTarget.toWorldPos().subtract(pos);
		positionOffset.y = 0;
		positionOffset = MINIW::Normalize(positionOffset);

		// todo check QuaternionToEulerAngle() or QuaternionToEuler()
		Rainbow::Vector3f euler = QuaternionToEulerAngle(rot);

		const Rainbow::Vector3f dest = RotateVectorByQuat(rot, Rainbow::Vector3f(0, 1.0f, 0));
		float v = DotProduct(dest, Rainbow::Vector3f(0, 1.0f, 0));

		// todo check setRotateArc() -> FromToQuaternion()
		Quaternionf quat = FromToQuaternion(Rainbow::Vector3f(0, 0, 1.0f), positionOffset);

		float t = euler.x;
		if (Abs(t) < 20.0f)
		{
			if (t < 0) t = (20.0f + t) * 1.0f;
			else t = -(20.0f - t) * 1.0f;
		}
		else t = 0;

		// todo check setAxisAngleX() -> AxisAngleToQuaternionf()
		const Quaternionf quat2 = AxisAngleToQuaternionf(Vector3f::xAxis, Deg2Rad(t));
		// quat2.setAxisAngleX(t);

		// ���������Ԫ���˷���Ҫ����˳��
		quat = quat * quat2;
		m_MoveDirective->SetRotation(quat);
	}
	//动画事件分发
	if (!m_bIsPrefabHand)
	{
		Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(m_HandModel);
		if (legacymodel != nullptr && legacymodel->GetAnimPlayer())
		{
			if (m_HandModel && legacymodel->GetAnimPlayer())
			{
				AnimationPlayer* pAnimPlayer = legacymodel->GetAnimPlayer();
				for (auto pEvent : pAnimPlayer->m_FrameEvents)
				{
					SandboxContext ctx;
					ctx.SetData_Number("objId", pEvent.objId);
					ctx.SetData_Number("seqId", pEvent.id);
					ctx.SetData_String("eventName", pEvent.event);
					SandboxEventDispatcherManager::GetGlobalInstance().Emit("Animation_FrameKey", ctx);
				}
				pAnimPlayer->m_FrameEvents.clear();
			}
		}
	}
}

void CameraModel::tick()
{
	m_PrePitch = m_ArmPitch;
	m_PreYaw = m_ArmYaw;


	if (m_GameCamera == NULL) return;
	float deltaPitch = m_GameCamera->getRotatePitch() - m_ArmPitch;
	float deltaYaw = m_GameCamera->getRotateYaw() - m_ArmYaw;

	if (GetIPlayerControl() && GetIPlayerControl()->getViewMode() == CameraControlMode::CAMERA_FPS && 
		(dynamic_cast<IClientPlayer*>(GetIPlayerControl()))->getOPWay() == PLAYEROP_WAY_FISHING)
	{
		float pitch, yaw;
		auto lookDir = GetIPlayerControl()->getLookDir();

		auto fishingcomponent = dynamic_cast<IClientActor*>(GetIPlayerControl())->getActorComponent(ComponentType::COMPONENT_FISHING);
		if (fishingcomponent)
		{
			bool result = fishingcomponent->Event2().Emit<Rainbow::Vector3f&>("Fishing_FishingDir", lookDir);
			Assert(result);
		}

		Direction2PitchYaw(&yaw, &pitch, lookDir);

		yaw += 180;	//不知道为什么

		if (yaw < 0) yaw += 360.0f;
		else if (yaw >= 360.0f) yaw -= 360.0f;

		deltaPitch = pitch - m_ArmPitch;
		deltaYaw = yaw - m_ArmYaw;
	}
	m_ArmPitch += deltaPitch * 0.5f;
	m_ArmYaw += deltaYaw * 0.5f;

	if (m_BobbingState > 0)
	{
		auto iplayer = (dynamic_cast<IClientActor*>(GetIPlayerControl()));
		if (iplayer)
		{
			int state = iplayer->getRun() ? 2 : 1;
			if (state != m_BobbingState)
			{
				setBobbing(true);
			}
		}
	}

	/*if (m_ToolModel && GetIPlayerControl())
	{
		Vector4f lightparam(0,0,0,0);
		WCoord center = GetIPlayerControl()->GetPlayerControlPosition();
		center.y += GetIPlayerControl()->getLocoMotion()->m_BoundHeight/2;
		if(m_World) m_World->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(center));
		if(m_PlayerIndex >= 0)
		{
			lightparam.x += 0.2f;
			lightparam.y += 0.2f;
		}
		ModelItemMesh* itemmodel = dynamic_cast<ModelItemMesh*>(m_ToolModel);
		if (itemmodel && itemmodel->getEntity() && itemmodel->getEntity()->GetMainModel())
			itemmodel->getEntity()->GetMainModel()->setInstanceData(lightparam);
	}*/
}

void CameraModel::showMoveDir(bool b)
{
	if (m_MoveDirective) m_MoveDirective->Show(b);
}

void CameraModel::setMoveTarget(const WCoord& pos, int modeview/* =0 */)
{
	m_MoveTarget = pos;
	m_MoveDirViewMode = modeview;
}

bool CameraModel::isShowMoveDir()
{
	if (m_MoveDirective == NULL) return false;
	return m_MoveDirective->IsShow();
}

int CameraModel::getMoveDirModeView()
{
	return m_MoveDirViewMode;
}

float CameraModel::getCurrentShakeTime()
{
	return m_CurrentShakeTime;
}

float CameraModel::getCurrentShakeCycle()
{
	return m_ShakeCycle;
}

void CameraModel::applyBodyColor(unsigned int color)
{
	char texdir[256];

	int skinid = PlayerIndex2Skin(m_PlayerIndex);
	if (skinid > 0)
	{
		RoleSkinDef* def = GetDefManagerProxy()->getRoleSkinDef(skinid);
		if (def)
		{
			sprintf(texdir, "entity/%d", def->Model);

			char path[256];
			int loadparam = RLF_CONVERT_BIT16;
			// todo_yanxiongjian
			//int loadparam = MINIW::ResourceManager::GetInstance().saveMemory() ? (RLF_CONVERT_BIT16 | RLF_DONT_KEEP_MEMORY_BAK) : (RLF_CONVERT_BIT16);
			// forever color >= 0
			if (color >= 0)
			{
				ColourValue colorval;
				colorval.setAsARGB(color);

				sprintf(path, "%s/yanse.png", texdir);

				SharePtr<Rainbow::Texture2D> tex = GetAssetManager().LoadAssetAsync<Texture2D>(path);  //memopt
				if (m_HandModel)
					m_HandModel->SetOverlayMask(tex, &colorval);
			}
			else
			{
				if (m_HandModel)
					m_HandModel->SetOverlayMask();
			}

			sprintf(path, "%s/male.png", texdir);

			//Texture *tex = static_cast<Texture *>(ResourceManager::GetInstance().blockLoad(path, loadparam));  //memopt
			//m_HandModel->setTexture("g_DiffuseTex", tex);
			//if (tex) tex->release();
		}
	}
}

void CameraModel::updateToolModelTexture(int textureIndex /*= 0*/)
{
	if (m_ToolModel)
	{
		auto model = dynamic_cast<ModelItemMesh*>(m_ToolModel);
		if (model)
		{
			model->updateToolModelTexture(textureIndex);
		}
	}
	if (m_ToolModel_left)
	{
		auto model = dynamic_cast<ModelItemMesh*>(m_ToolModel_left);
		if (model)
		{
			model->updateToolModelTexture(textureIndex);
		}
	}
}


bool CameraModel::isShowHomeTaskDir()
{
	if (m_HomeDirective == NULL) return false;
	return m_HomeDirective->IsShow();
}
void CameraModel::showHomeTaskDir(bool b)
{
	if (m_HomeDirective) m_HomeDirective->Show(b);
}
void CameraModel::setHomeTarget(const WCoord& pos)
{
	m_MoveTarget = pos;
}
void CameraModel::updateHomeTask(float dtime, const Rainbow::WorldPos& eyepos, const Rainbow::Quaternionf& cam_rot)
{
	//if (!m_HomeDirective->isShow())
	//{
	//	return;
	//}
	Quaternionf rot = cam_rot;
	Rainbow::Vector3f positionOffset(20.0f, 0, 30.0f);
	//Quaternionf rotationOffset = Quaternionf::identity;
	//// ���������Ԫ���˷���Ҫ����˳��
	//rotationOffset = cam_rot * rotationOffset;
	Quaternionf rotationOffset = cam_rot;
	Rainbow::Vector3f shakeOffset;
	// 引导提示箭头
	if (m_HomeDirective)
	{
		Rainbow::WorldPos homeposition = eyepos;
		positionOffset = Rainbow::Vector3f(0.f, -50.0f, 300.0f);
	   if (CameraControlMode::CAMERA_FPS != GetIPlayerControl()->getViewMode())
		{
			positionOffset = Rainbow::Vector3f(0.0f, 0.0f, 0.0f);
			IActorLocoMotion* playerloc = GetIPlayerControl()->getPlayerControlLocoMotion();
			Rainbow::Vector3f prevdir = Yaw2FowardDir(playerloc->GetPrevRotateYaw());
			Rainbow::Vector3f dir = Yaw2FowardDir(playerloc->GetRotateYaw());

			float t = playerloc->GetTickPosition().m_TickOffsetTime / GAME_TICK_TIME;
			Rainbow::Vector3f vec3 = Lerp(prevdir, dir, t);
			vec3.x *= 220.0f;
			vec3.z *= 220.0f;
			vec3.y = 70.0f;
			positionOffset += vec3;
			homeposition = playerloc->getFramePosition();
			// todo_yanxiongajin
			//m_HomeDirective->setLayer(RL_TERRAIN);
		}
		else
		{
			//m_HomeDirective->GetGameObject()->SetLayer(kLayerIndexCustom_FRONT_SCENE);
			positionOffset = RotateVectorByQuat(rot, positionOffset);
		}
		WorldPos pos = homeposition + positionOffset;
		m_HomeDirective->SetPosition(pos);

		m_HomeDirective->UpdateTick(TimeToTick(dtime));

		positionOffset = m_MoveTarget.toWorldPos().subtract(pos);
		positionOffset.y = 0;
		positionOffset = MINIW::Normalize(positionOffset);

		// todo_yanxiongajin
		const Rainbow::Vector3f euler = QuaternionToEulerAngle(rot);

		Rainbow::Vector3f dest = RotateVectorByQuat(rot, Rainbow::Vector3f(0, 1.0f, 0));

		float v = DotProduct(dest, Rainbow::Vector3f(0, 1.0f, 0));

		Quaternionf quat = FromToQuaternion(Rainbow::Vector3f(0, 0, 1.0f), positionOffset);
		//quat.setRotateArc(Rainbow::Vector3f(0, 0, 1.0f), positionOffset);


		float t = euler.x;
		if (Abs(t) < 20.0f)
		{
			if (t < 0) t = (20.0f + t) * 1.0f;
			else t = -(20.0f - t) * 1.0f;
		}
		else t = 0;

		Quaternionf quat2 = AxisAngleToQuaternionf(Vector3f::xAxis, Deg2Rad(t));
		//quat2.setAxisAngleX(t);

		// ���������Ԫ���˷���Ҫ����˳��
		quat = quat * quat2;
		m_HomeDirective->SetRotation(quat);
	}
}

bool CameraModel::PlayActInHand(int actid, int playmode)
{
	if (m_ToolModel_left)
	{
		ModelItemMesh* mesh = dynamic_cast<ModelItemMesh*>(m_ToolModel_left);
		if (mesh && mesh->getEntity())
		{
			auto def = GetDefManagerProxy()->getTriggerActDef(actid);
			if (def)
			{
				actid = def->ActID;
			}
			if (mesh->getEntity())
			{
				mesh->getEntity()->PlayAnim(actid, playmode);
			}
		}
	}
	if (m_ToolModel)
	{
		ModelItemMesh* mesh = dynamic_cast<ModelItemMesh*>(m_ToolModel);
		if (mesh && mesh->getEntity())
		{
			auto def = GetDefManagerProxy()->getTriggerActDef(actid);
			if (def)
			{
				actid = def->ActID;
			}
			if (mesh->getEntity())
			{
				mesh->getEntity()->PlayAnim(actid, playmode);
			}
			return true;
		}
	}
	return false;
}

void CameraModel::AttachToScene(Rainbow::GameScene* scene)
{
	/*if (this->m_HandModel)
	{
		this->m_HandModel->DetachFromScene();
		this->m_HandModel->AttachToScene(scene);
	}

	if (this->m_ToolModel)
	{
		this->m_ToolModel->DetachFromScene();
		this->m_ToolModel->AttachToScene(scene);
	}*/
}

void CameraModel::StartHandPosOffset(Rainbow::Vector3f offsetTarget, float time)
{
	m_HandPosOf_begin = Rainbow::Vector3f::zero;
	m_HandPosOf_end = offsetTarget;
	m_HandPosOf_accumTime = 0.f;
	m_HandPosOf_totalTime = time < 0.f ? 0.f : time;
}

void CameraModel::StopHandPosOffset(bool immediate)
{
	m_HandPosOf_begin = m_HandPosOf_end;
	m_HandPosOf_end = Rainbow::Vector3f::zero;
	if (immediate)
		m_HandPosOf_accumTime = m_HandPosOf_totalTime;
	else
		m_HandPosOf_accumTime = 0.f;
}

void CameraModel::UpdateHandPosOffset(float dtime, Rainbow::Vector3f& offset)
{
	if (m_HandPosOf_accumTime < m_HandPosOf_totalTime)
	{
		m_HandPosOf_accumTime += dtime;
		offset = Rainbow::Lerp(m_HandPosOf_begin, m_HandPosOf_end, m_HandPosOf_accumTime / m_HandPosOf_totalTime);
	}
	else
	{
		offset = m_HandPosOf_end;
	}
}

void CameraModel::SetHandPos(Rainbow::Vector3f pos)
{
	if (m_ItemInHandDef)
	{
		m_ItemInHandDef->FPSHandPos.x = pos.x;
		m_ItemInHandDef->FPSHandPos.y = pos.y;
		m_ItemInHandDef->FPSHandPos.z = pos.z;
	}
	else
	{
		if (!m_GMOffset) m_GMOffset = new Vector3f();
		*m_GMOffset = pos;
	}
}

void CameraModel::SetHandScale(float scale)
{
	if (m_HandEntity)
		m_HandEntity->SetScale(Rainbow::Vector3f(scale, scale, scale));
}

void CameraModel::SetHandBind(bool b)
{
	m_GMBind = b;
}

void CameraModel::SetGMYawPitch(float yaw, float pitch)
{
	m_GMYaw = yaw;
	m_GMPitch = pitch;
}

void CameraModel::UpdateToolPos()
{
	if (m_ToolModel && m_ItemInHandDef)
	{
		auto pos = m_ItemInHandDef->FPSRightItemPosData.Pos;
		m_ToolModel->SetPosition(WorldPos(pos.x, pos.y, pos.z));
		auto rot = m_ItemInHandDef->FPSRightItemPosData.Rot;
		m_ToolModel->SetRotation(rot.x, rot.y, rot.z);
	}
}

void CameraModel::HandPosOffsetDebugMode(bool b, float offsetUnit)
{
#ifdef ENABLE_PLAYER_CMD_COMMAND
	if (b)
	{
		if (debug_handPosOffset)
		{
			m_eyePosOffUnit = offsetUnit;
			return;
		}
		SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("ClientGameUserInputEvent");
		m_userInputEventCallBack = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("ClientGameUserInputEvent", nullptr,
			[&](SandboxContext context) -> SandboxResult
			{
				if (context.HasKey("InputTag"))
				{
					Rainbow::InputEvent inputEvent = context.GetData_UserObject<Rainbow::InputEvent>("InputTag");
					if (inputEvent.type == Rainbow::InputEvent::kKeyDown)
					{
						auto print = [this]()
						{
							char str_ComboAttack[128];
							sprintf(str_ComboAttack, "X:[%.2f], Y:[%.2f], Z:[%.2f]", m_eyePosOffDebugMode.x, m_eyePosOffDebugMode.y, m_eyePosOffDebugMode.z);
							GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChatToSelf(str_ComboAttack);
							LOG_INFO(str_ComboAttack);
						};
						switch (inputEvent.keycode)
						{
						case Rainbow::SDLK_UP:
							m_eyePosOffDebugMode.y += m_eyePosOffUnit;
							print();
							break;
						case Rainbow::SDLK_DOWN:
							m_eyePosOffDebugMode.y -= m_eyePosOffUnit;
							print();
							break;
						case Rainbow::SDLK_RIGHT:
							m_eyePosOffDebugMode.x += m_eyePosOffUnit;
							print();
							break;
						case Rainbow::SDLK_LEFT:
							m_eyePosOffDebugMode.x -= m_eyePosOffUnit;
							print();
							break;
						case Rainbow::SDLK_HOME:
							m_eyePosOffDebugMode.z += m_eyePosOffUnit;
							print();
							break;
						case Rainbow::SDLK_END:
							m_eyePosOffDebugMode.z -= m_eyePosOffUnit;
							print();
							break;
						default:
							break;
						}
					}
				}
				return SandboxResult();
			}
		);
		m_eyePosOffDebugMode = Rainbow::Vector3f::zero;
		m_eyePosOffUnit = offsetUnit;
		debug_handPosOffset = true;
		if (m_GameCamera) m_GameCamera->setZoomInOut(50);
	}
	else
	{
		if (!debug_handPosOffset)
			return;

		SandboxEventDispatcherManager::GetGlobalInstance().Unsubscribe("ClientGameUserInputEvent", m_userInputEventCallBack);
		debug_handPosOffset = false;
		if (m_GameCamera) m_GameCamera->resetZoom();
	}
#endif
}

// 检查当前非循环动作是否播放结束
bool CameraModel::isCurrentHandAnimFinished() const
{
	// 检查手部模型是否存在且动作是否播放结束
	if (m_HandModel && m_CurrentHandAnimId > 0)
	{
		return !m_HandModel->HasAnimPlaying(m_CurrentHandAnimId);
	}
	
	return true;
}

// 检查并播放待机动作
void CameraModel::checkAndPlayIdleAnim()
{
	// 如果当前没有播放动作，或者当前非循环动作已播放结束
	InputInfo* inputInfo = GetIPlayerControl() ? GetIPlayerControl()->getInputInfo() : nullptr;
	bool rightclick = inputInfo ? inputInfo->rightClick : false;
	if (m_CurrentHandAnimId > 0 && !m_IsCurrentHandAnimLoop && isCurrentHandAnimFinished())
	{
		m_CurrentHandAnimId = -1;
		m_IsCurrentHandAnimLoop = false;
		m_CanAnimBreak = true;

		if (m_NextHandAnimId > 0)
		{
			playHandAnim(m_NextHandAnimId, m_NextHandAnimLoopMode, 1, -1, 0.1f);
			m_NextHandAnimId = -1;
			m_NextHandAnimLoopMode = -1;
		}
		else
		{
			playIdleAnim();
		}
	}
	else
	{
		SetRightButtonDown(rightclick);
	}
	m_RightButtonDown = rightclick;
}

void CameraModel::playIdleAnim()
{
	if (m_RightButtonDown && m_ToolDef && m_ToolDef->HandLoopSeq > 0)
	{
		playHandAnim(m_ToolDef->HandLoopSeq, 0);
	}
	else if (m_BobbingState > 0)
	{
		setBobbing(true);
	}
	// 如果有设置待机动作ID，则播放待机动作
	else if (m_IdleAnimId != -1)
	{
		playHandAnim(m_IdleAnimId, 0); // 循环播放待机动作
	}
}

void CameraModel::playFireAnim()
{
	if (m_RightButtonDown && m_ToolDef && m_ToolDef->HandAtkSeq > 0)
	{
		playHandAnim(m_ToolDef->HandAtkSeq, 1, 1, -1, 0.1f);
		m_CanAnimBreak = false;
		m_ForceNoCrossfade = true;
	}
	else
	{
		if (m_GunDef && m_GunDef->ShootAnimFps > 0)
		{
			playHandAnim(m_GunDef->ShootAnimFps, 1);
			m_CanAnimBreak = false;
			m_ForceNoCrossfade = true;
		}
	}
}

void CameraModel::SetRightButtonDown(bool b)
{
	if (m_RightButtonDown == b)
		return;

	m_RightButtonDown = b;
	if (!m_ToolDef)
		return;

	if (!m_RightButtonDown && m_ToolDef->HandEndSeq > 0)
	{
		playHandAnim(m_ToolDef->HandEndSeq, 1);
	}
	else if (m_RightButtonDown && m_ToolDef->HandStartSeq > 0)
	{
		playHandAnim(m_ToolDef->HandStartSeq, 1);
		if (m_ToolDef->HandLoopSeq > 0)
			playHandAnim(m_ToolDef->HandLoopSeq, 0);
	}
}

void CameraModel::playEatAnim()
{
	if (m_IsFood && !m_IsEatAnimPlaying)
	{
		playHandAnim(1060005, 1);  // 起手动作
		m_CanAnimBreak = false;
		playHandAnim(1060015, 0);  // 进食动作
		m_IsEatAnimPlaying = true;
	}
}

void CameraModel::stopEatAnim()
{
	m_IsEatAnimPlaying = false;
	if (m_IsFood)
	{
		playHandAnim(1060025, 1);
		m_CanAnimBreak = false;
	}
}
