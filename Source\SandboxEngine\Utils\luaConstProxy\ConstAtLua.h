
#ifndef __CONST_AT_LUA__HH__
#define __CONST_AT_LUA__HH__ 1
#include <array>
#include "OgreShared.h"
#include "json/jsonxx.h"
//tolua_begin
struct ItemDataConst
{
	int ItemID;
	int Num;
	int Probability;
	ItemDataConst(int id, int num, int prob) :ItemID(id), Num(num), Probability(prob)
	{

	}
};

//方块和生物的穿透率
struct UGCCFG
{
	std::unordered_map<int, float> basic;//basic type的方块 key是MineTool
	std::unordered_map<std::string, float> map;
	UGCCFG()
	{
	}

	void setMineTool(int mineTool, float value)
	{
		basic[mineTool] = value;
	}

	void setKV(std::string key, float value)
	{
		map[key] = value;
	}

	float get(std::string key, float defaultValue)
	{
		if (map.find(key) != map.end())
			return map[key];

		return defaultValue;
	}
};

struct UnderGroundPalaceTreasure
{
	int maxTypeCount;
	int perMinCount;
	int perMaxCount;
	int probability;
	std::vector<int> typeIdList;

	void addTypeId(int id)
	{
		typeIdList.push_back(id);
	}
};

struct ComputerOrderData //order映射itemId
{
	int itemId;
	std::string order;
	std::string prefabId;
	int num;
	ComputerOrderData()
	{
		itemId = 0;
		order = "";
		prefabId = "";
		num = 0;
	}
};

struct ConstAtLua //该数据会从lua中设置 
{
    //{{{
    float hpmax; //最大血量
	float hpoverflow;//溢出部分血量
    float strengthmax; //最大体力
	float strengthoverflow;//溢出部分体力值
	float thirstmax; //最大体力
	float thirsthoverflow;//溢出部分体力值
    float foodlevel;//出事饥饿值

    int food_beilv;//食物的倍率

    int   kongshou_shanghai_beilv;//空手伤害倍率
    int   cactus_shanghai_beilv;//仙人掌伤害倍率
	int   cactus_force;//仙人掌弹开系数
    int   yanjiang_shanghai_beilv;//岩浆伤害倍率
    int   zhixi_shanghai_beilv;//窒息伤害倍率, 水下生物在空气中窒息
    int   nishui_shanghai_beilv;//溺水伤害倍率 ATTACK_DROWN
    int   xukong_shanghai_beilv;//虚空伤害倍率, 跌落到地图以外?
    int   falling_shanghai_beilv;// 跌落伤害倍率
    int   default_shanghai_beilv;//默认伤害倍率

	float default_xieliang_huifu_beilv_old;//默认血量恢复倍率（旧版饥饿机制）
	float default_xieliang_huifu_beilv;//默认血量恢复倍率
    float default_tili_huifu_beilv;//默认体力恢复倍率

    float max_strength_for_exhaustion;//疲劳时体力的最大值

	int architectural_repaired_value;//建筑方块每次修复恢复值

	int waterblock_bad_effect_probability; //喝生水方块出现副作用的概率 100计算
	int waterblock_bad_effect_value; //喝生水方块出现副作用扣除饥饿值
    /**
    @deprecated 
    */
    float max_percentage_of_strength_for_exhaustion;//疲劳时体力的最大百分比
    
    float strength_consumption_of_attack;   //攻击时体力消耗值
    float strength_consumption_of_running_jump;  //疾跑跳跃体力消耗值
    float strength_consumption_of_double_jump;   //二段跳体力消耗值
	float strength_consumption_of_charging_per_second;	//蓄力每秒消耗体力值
	float strength_consumption_of_swimming_per_second;	//游泳每秒消耗体力值
	float strength_consumption_of_digging_per_second;	//挖方块每秒消耗体力值
	float strength_consumption_of_running_per_second;//奔跑每秒消耗体力值
	float strength_min_overdraw; //大于该值则脱离体力透支buff
	float strength_max_overdraw; //小于等于该值获得体力透支buff
	float strength_consume_walk; //步行所耗体力

	float strength_consumption_of_digging_block_min; //挖方块每块最小消耗值,可小数
	float strength_consume_fishing; //钓鱼时每秒消耗体力,可小数
	float reduce_strength_per_hp_recover; //恢复血量时每单位生命消耗体力,可小数
	float actor_revive_in_place_strength_min; //原地复活最小体力值,百分比

	float thirst_consumption_of_attack;   //攻击时口渴消耗值
	float thirst_consumption_of_running_jump;  //疾跑跳跃口渴消耗值
	float thirst_consumption_of_double_jump;   //二段跳口渴消耗值
	float thirst_consumption_of_charging_per_second;	//蓄力每秒消耗口渴值
	float thirst_consumption_of_swimming_per_second;	//游泳每秒消耗口渴值
	float thirst_consumption_of_digging_per_second;	//挖方块每秒消耗口渴值
	float thirst_consumption_of_running_per_second;//奔跑每秒消耗口渴值
	float thirst_min_overdraw; //大于该值则脱离口渴透支buff
	float thirst_max_overdraw; //小于等于该值获得口渴透支buff
	float thirst_consume_walk; //步行所耗口渴
	float thirst_consumption_of_digging_block_min; //挖方块每块最小消耗值,可小数
	float thirst_consume_fishing; //钓鱼时每秒消耗体力,可小数
	float reduce_thirst_per_hp_recover; //恢复血量时每单位生命消耗体力,可小数
	float actor_revive_in_place_thirst_min; //原地复活最小口渴值,百分比

	int revive_in_place_consume_buff_duration;// 原地复活星星消耗增加BUFF持续时间，单位秒
	int	revive_in_place_consume_buff_num_stage1; //原地复活星星消耗增加BUFF第一段分割次数
	int	revive_in_place_consume_buff_num_stage2;// 原地复活星星消耗增加BUFF第二段分割次数
	int	revive_in_place_consume_buff_star_extra1;// 原地复活星星消耗增加BUFF额外增加星星数量，小于5次
	int	revive_in_place_consume_buff_star_extra2;// 原地复活星星消耗增加BUFF额外增加星星数量，大于等于5次
	int	revive_in_place_consume_buff_duration_extra1; //原地复活星星消耗增加BUFF额外增加1持续时间，单位秒，小于5次
	int	revive_in_place_consume_buff_duration_extra2; //原地复活星星消耗增加BUFF额外增加1持续时间，单位秒，大于等于5次

	bool be_forbidden_to_run_with_exhaustion;//疲劳时是否禁止奔跑

	bool check_strength_enough;//体力消耗操作是否判断体力充足

    //TODO 其他常量 
	float houtui_yidong_beilv;  //后退的移动速度倍率
	float sneaking_yidong_beilv;  //潜行的移动速度倍率
	float chongci_yidong_beilv;  //冲刺的移动速度倍率
	float swimming_yidong_beilv; //游泳的移动速度倍率
	float swimming_honey_yidong_beilv; //在蜂蜜中游泳的速度倍率
	float xuli_yidong_beilv;     //蓄力的移动速度倍率

	float chongci_phys_yidong_beilv; // 物理冲刺的移动速度倍率
	
	/*
	@deprecated
	*/
	int tili_action_walk;        //走路消耗的体力
	int tili_action_sprint;      //快跑
	int tili_action_swim;        //游泳
	int tili_action_jump;        //跳跃
	int tili_action_sprintjump;  //快跑跳跃
	int tili_action_destroyblock;//破坏方块
	int tili_action_attack;      //攻击
	int tili_action_hurt;        //受伤
	int tili_action_food2hp;     //吃饱后回血

	float kaijing_sensitivity_xishu;//开镜下，鼠标灵敏度系数
	float kaijing_yidong_xishu;		//开镜下，玩家移动速度系数

	float planet_gravity_beilv;    //外星的重力倍率
	float planet_oxygenuse_beilv;  //外星氧气消耗倍率
	float planet_oxygenhurt_beilv; //外星缺氧中毒伤害概率
	int   planet_safearea_radius;  //安全区半径
	float planet_tili_beilv;       //外星饥饿度消耗倍率
	float planet_daynight_beilv;   //外星日夜变化倍率
	int planet_lightatten_beilv;   //外星光线削减倍率（只能整数倍）
	int planet_cloud_genperiod;    //1:最快， n: 每n个周期产生一个云
	int planet_cloud_delperiod;    //1:最快， n: 每n个周期清除一个云
	int planet_cloud_crackperiod;  //1:最快， n: 每n个周期破碎一点
	int planet_totem_activeage;    //图腾核心激活的时间： 秒, 0表示无限时间
	int planet_totem_pollutionradius;	//图腾核心召唤BOSS, 污染方块最大半径
	int planet_totem_pollutionspeed;	//图腾核心召唤BOSS, 方块污染速度, 每秒多少格
	float fall_hurt_ext;
	float fall_hurt_ext2;	
	float effect_maxscale;			//触发器特效, 最大放大倍数

	int actor_max_extremisval;		//生物最大濒死值
	int	actor_max_hugger;			//生物最大饥饿值
	int	consume_food_by_hugger;		//饥饿消耗的饱食度
	int	actor_init_favor;			//生物初始好感值
	int	actor_max_favor;			//生物最大好感值
	int decay_extremis;				//每秒衰减的濒死值

    int actor_bound_height;         //碰撞盒高度
    int actor_bound_width;          //碰撞盒宽度
	
	int feedtrough_1_consume_count;//1级容量饲料槽可供消耗次数
	int feedtrough_2_consume_count;//2级容量饲料槽可供消耗次数 
	int feedtrough_3_consume_count;//3级容量饲料槽可供消耗次数

	int number_of_sprinklers;		//花洒浇灌次数 code-by:liwentao

	int weapon_skillpoint_killed;	//熟练度新增点数_击杀数
	int weapon_skillpoint_cdskill;	//熟练度新增点数_武器蓄力
	int weapon_skillpoint_cuttree;	//熟练度新增点数_砍树(方块)
	int weapon_skillpoint_digblock; //熟练度新增点数_挖坑(方块)
	int weapon_skillpoint_plantland;//熟练度新增点数_耕地(方块)

	int check_new_hp_rule;			//开启新的血量体力规则 0:冒险和编辑都启用 1:只冒险 2:只编辑 3:都可以

	int actor_perseverance_max;
	int actor_armor_max;
	int actor_revive_hp;
	int actor_revive_strength;


    //}}}

	/* codeby:zhangyusong  冒险0623*/
	float sand_duststorm_power; //沙尘暴推力大小
	float sand_duststorm_power_up; //强沙尘暴推力大小
	float sand_duststorm_speed; //沙尘暴移动速度  每tick多少个方块
	int sand_duststorm_idle; //沙尘暴闲时
	int sand_duststorm_up_start; //沙尘暴天气前奏——》强沙尘暴天气来临 tick
	int sand_duststorm_up_end; // 强沙尘暴天气来临——》强沙尘暴结束  多少tick
	int sand_duststorm_end; //强沙尘暴结束——》沙尘暴天气结束  tick

	int sandman_core_hp;					    //沙人核心血量
	int sandman_absorbsand_max;					    //沙人吸收上限
	float sandman_absorbsand_curehp;					    //沙人吸收回复血量
	float sandman_absorbsand_scale;					    //沙人吸收体型增大
	int sandman_fakedeath_tick;					    //沙人假死时间 Tick
	int sandman_sonicboom_tick;					    //沙人受音爆罐子影响时间 Tick
	
	int playerExploreFindBiome;     //探索的地形
	int playerExploreRange; //探寻的trunk数大小
	int desertTradeCaravanSaveTime; //商队存在的时间单位 h
	int desertTradeCaravanSpawnMinx; //商队刷在玩家面前最小x
	int desertTradeCaravanSpawnMaxx; //商队刷在玩家面前最大x
	int desertTradeCaravanSpawnMinz; //商队刷在玩家面前最小z
	int desertTradeCaravanSpawnMaxz; //商队刷在玩家面前最大z
	int desertVillageSpecialBusinessmanSpawnProbab;  //村庄里特殊沙漠商人生成几率
	int desertVillageNormalBusinessmanSpawnProbab; //村庄里普通商人生成3个几率, 反之生成2个
	int desertVillagerWomanSpawnFourProbab;  //村庄里女村民生成4个几率,反之生成3个
	int desertVillagerManSpawnFourProbab;    //村庄里男村民生成4个几率,反之生成3个
	int desertVillagerChildSpawnFourProbab;    //村庄里小孩生成3个几率,反之生成2个
	bool needOpenExploreTerrain;              //是否要开启地形勘测功能
	bool needGenVillage;					//是否要生成村庄
	int  terrainExploreCool;				 //探索冷却时间
	int inSameOasisRange;					//在绿洲的某点和另一点相距多少范围是在一个绿洲内
	int maxWarningTick;					    //商人警告间隔
	float desertReplyHp;						//商人在帐篷每tick回血量
	float cameraTpsBackPlayerRotationLimitAngle; //摄像动作视角模式角色旋转角度限制
	int plutonicRockGenProab;		//沉积岩飘雪概率
	int citySizeX;
	int citySizeY;
	int cityBuildNum;
	int endOfPOD;
	// 在LuaInterfaceProxy 中会对ConstAtLua实例memset，把容器类型的成员防在下面，用 endOfPOD 隔开

		// 海洋---暴风雨天气
	float tempest_power; //暴风雨推力大小
	float tempest_power_up; //强暴风雨推力大小
	int tempest_idle; //暴风雨闲时
	int tempest_up_start; //暴风雨天气前奏——》强暴风雨天气来临 tick
	int tempest_up_end; // 强暴风雨天气来临——》强暴风雨结束  多少tick
	int tempest_end; //强暴风雨结束——》暴风雨天气结束  tick
	int rainbow_probability;//彩虹出现概率 0~100
	int tempest_probability;//暴风雨出现概率，后续累计加5
	int tempest_add_tick;//50概率未出现累计时间后在概率
	int rainbow_tick;//彩虹出现时间

	int water_pressure_coefficient; //水压系数
	float oxygen_consume_coefficient; //氧气消耗系数
	float power_consume_coefficient; //体力消耗系数
	int block_hardness_value; //小于该方块硬度得方块，在水压下会挤碎
	int block_pressure_value; //满足3个面受到液体压力大于等于该值，进入挤碎
	int block_scan_liquid_range; //方块自身水下扫描
	int block_scan_all_range; //玩家根据移动扫描得方块区
	int bubble_move_max_distance;//海马喷射气泡最远距离，超过此距离自毁
	int crab_create_random_num;//打破碎石块时生成螃蟹的概率(n分之1)
	int clamp_click_max;//被螃蟹钳击时挣脱所需点击次数
	int fishingVillageExploreRange; //渔村探寻的trunk数大小
	int scallops_in_water_tick; // 巨型扇贝在水中的状态判断的时间tick
	int scallops_around_fire_tick; // 巨型扇贝在点燃的篝火或火焰方块的状态判断tick
	int scallops_after_dispear_tick; //巨型扇贝在螃蟹消失，以及珍珠被拿走后的时间tick
	int scallops_born_pearl; // 巨型扇贝张开产生珍珠的几率(1-10范围, 小于等于scallops_born_pearl，代表十分之scallops_born_pearl)
	int scallops_born_crap;  // 巨型扇贝张开产生螃蟹的几率(1-10范围, 小于等于scallops_born_crap，代表十分之scallops_born_pearl)
	int HotSpringGenRate; //热泉生成概率
	int HotSpringGenDis; //热泉生成间隔距离

	int GenMossRate;//苔藓生成概率 0~100
	float lightningChainDamageDecPercent;	// 闪电链伤害衰减
	float lightningChainEffectRawLength;	// 闪电链特效原始长度
	float lightningWeaponEffectScale;		// 武器上的闪电特效缩放

	int chargeFishingMinDis;	// 钓鱼蓄力最小距离
	int chargeFishingMaxDis;	// 钓鱼蓄力最大距离
	int chargeFishingTickStep;	// 钓鱼蓄力每帧移动距离
	int chargeFishingWaitTime;	// 钓鱼蓄力到达最近最远后，停留的时间，毫秒
	int fishingMinWaterDeep;	// 钓鱼最小水深
	int fishingMaxDownOffset;	// 钓鱼下方最大距离
	int fishingStartWaitTicks;	// 钓鱼开始到第一次上钩tick
	int fishingMiddleWaitTicks;	// 钓鱼两次咬饵中间等待tick
	int fishingFakeBaltTicks;	// 钓鱼假咬饵维持tick
	int fishingFakeBaltSinkDis;	// 钓鱼假咬饵下沉距离
	int fishingRealBaltSinkDis;	// 钓鱼真咬饵下沉距离
	int fishingRealPickWaitTicks;	// 钓鱼真咬饵时，提竿到拉起鱼的等待时间

	int smallPirateShip_PirateNum; //大中小海盗船死亡时生成的海盗数
	int middlePirateShip_PirateNum;
	int largePirateShip_PirateNum;
	int spawnPointRangeNum;		//出生点位的搜索范围，以chunk为单位 
	std::vector<ItemDataConst> PirateChest_Treasure;		//海盗船沉没宝箱


	float pirateShip_ChangeModel1; //海盗船第一次替换模型血量（%）
	float pirateShip_ChangeModel2; //海盗船第二次替换模型血量（%）

	float pirateShip_HpHealing; //海盗船恢复血量（%）
	int pirateShip_HpHealingCount; //海盗船恢复血量Tick

	int	fishingJellyLightningTick;		// 水母闪电单段传递tick
	float fishingJellyLightningDamage;	// 钓鱼钓到水母电击伤害

	int smallTorchMaxFireTick;	// 小火炬燃烧时间，tick数

	int coralEcosyRangXMax; // 珊瑚生态x方向最大范围
	int	coralEcosyRangXMin; // 珊瑚生态x方向最小范围
	int coralEcosyRangYMax; // 珊瑚生态y方向最大范围
	int	coralEcosyRangYmin; // 珊瑚生态y方向最大范围
	int	coralEcosyRangZMax; // 珊瑚生态z方向最大范围
	int	coralEcosyRangZMin; // 珊瑚生态z方向最大范围
	int coralEcosyBubbleNumMax; // 珊瑚生态气泡珊瑚最大数量
	int	coralEcosyBubbleNumMin; // 珊瑚生态气泡珊瑚最小数量
	int	coralEcosyWaterWeedPer; // 珊瑚生态水草生成概率

	int coralReefRangXMax;	// 珊瑚周围礁石生成x方向最大范围
	int	coralReefRangXMin;	// 珊瑚周围礁石生成x方向最小范围
	int coralReefRangYMax;	// 珊瑚周围礁石生成y方向最大范围
	int	coralReefRangYmin;	// 珊瑚周围礁石生成y方向最大范围
	int	coralReefRangZMax;	// 珊瑚周围礁石生成z方向最大范围
	int	coralReefRangZMin;	// 珊瑚周围礁石生成z方向最大范围
	int coralReefPer;		// 珊瑚周围礁石生成礁石生成概率

	int coralGrowRate;		// 珊瑚生长速率

	int treasureProtectorProb;		// 沉船群 海灵守卫船中生成概率 1-100
	int treasureProtectorNumber;	// 沉船群 海灵守卫数量

	int sleeep_check_mob_distance; //睡觉检查周围生物
	int oxygen_pack_full_bullet_num; //压缩气罐满 1048 对应多少子弹
	int air_ball_atk_value_param; //空气球根据水压计算攻击力的公式系数  atk = water_power*air_ball_atk_value_param
	int air_ball_range_value_param; //空气球根据水压计算伤害范围的公式系数  range = water_power*air_ball_range_value_param
	int air_ball_max_range_value_param; ////空气球最大爆炸范围
	int air_ball_land_range_value_param; //空气球陆地上的范围值
	int air_ball_land_montion_param; //空气球在陆地上产生的推力值


	float livingWaterJumpBaseSpeed; // 生物在水中上游的基础速度
	float livingWaterSneakAddSpeed; //生物在水中潜行键下降的速度加成
	int desertIsLandTreasureBox_probability;   //荒岛宝箱生成概率.
	int islandBuildCoralIsLandTreasureBox_probablity; //珊瑚岛宝箱生成概率
	int islandBuildCoralIsLandPirateShop_probablity; //珊瑚岛海盗商人生成概率
	int islandBuildCoralIsLandShop_probablity; //珊瑚岛海岛商人生成概率
	int inSameIslandRange; //相距多少范围在一个岛上
	int hotZoneJetSpeed;
	int interact_poseidon_status_time;

	int deductHungerTick;//渔村村民掉血时间
	
	int botFollowPlayerDist; //机器人跟随距离

	bool forceGuideOpen; //开启强制新手引导
	int forceGuideMoveTime; //移动检测时间（以秒为单位）

	int spawnWaterMobPercent;	// 水生物生成概率
	int spawnFlyMobPercent;		// 飞行生物生成概率

	int weather_render_range;
	int m_ConstMilaTempAtten;
	int m_ConstMilaTempAdd;
	int m_ConstMengyanTempAtten;
	int m_ConstMengyanTempAdd;
	int m_ConstPingtanTempAtten;
	int m_ConstPingtanTempAdd;
	float m_ConstPlantTempChangeVal;
	float m_ConstTickTempChangeVal;
	float m_ConstBaseTempChangeVal;
	float m_ConstTickTempChangeRate;
	float m_ConstTempBurn;
	float m_ConstTempTopHeat;
	float m_ConstTempHeat;
	float m_ConstTempIce;
	float m_ConstTempTopIce;
	float m_ConstTempFreeze;
	float m_ConstWeatherRain;
	float m_ConstWeatherSnow;
	float m_ConstWeatherTempest;
	float m_ConstWeatherTempestUp;
	float m_ConstWeatherBlizzard;
	float m_ConstWeatherBlizzardUp;
	float m_ConstWeatherThunder;

	float blizzard_power;
	float blizzard_power_up;
	float blizzard_speed ;
	int blizzard_idle ;
	int blizzard_up_start;
	int blizzard_up_end;
	int blizzard_end;

	int iceCrystalShroomGrowRate;		// 冰晶喷菇生长速率
	int iceCrystalShroomGrowCrycle;		// 冰晶喷菇生长周期（tick数）
	int iceCrystalFernGrowRate;			// 冰晶蕨生长速率
	int iceCrystalFernGrowCrycle;		// 冰晶蕨生长周期（tick数）

	int largeLarchTallMax;				// 大落叶松整体高度最大值
	int largeLarchTallMin;				// 大落叶松整体高度最小值
	int largeLarchRadiusMax;			// 大落叶松最大半径
	int largeLarchFruitMax;				// 大落叶松果实最大个数
	int largeLarchFruitMin;				// 大落叶松果实最小个数
	int smallLarchTallMax;				// 小落叶松整体高度最大值
	int smallLarchTallMin;				// 小落叶松整体高度最小值
	int smallLarchRadiusMax;			// 小落叶松最大半径
	int smallLarchFruitMax;				// 小落叶松果实最大个数
	int smallLarchFruitMin;				// 小落叶松果实最小个数
	int smallLarchGrowPercent;			// 小落叶松生成概率

	int pushSnowBall_Large_Size; //雪球最大尺寸
	int pushSnowBall_Middle_Size; //雪球中到雪球大阈值
	int pushSnowBall_Small_Size; //雪球小到雪球中阈值
	float   pushSnowBall_Large_SpeedUp; //推雪球加速度变化--尺寸大
	float	pushSnowBall_Middle_SpeedUp; //推雪球加速度变化--尺寸中
	float	pushSnowBall_Small_SpeedUp; //推雪球加速度变化--尺寸小
	float	pushSnowBall_Large_Speed; //推雪球最大速度与加速度--尺寸大
	float	pushSnowBall_Middle_Speed; //推雪球最大速度与加速度--尺寸中
	float	pushSnowBall_Small_Speed; //推雪球最大速度与加速度--尺寸小
	float	pushSnowBall_Large_Damage; //推雪球撞击伤害--尺寸大
	float	pushSnowBall_Middle_Damage; //推雪球撞击伤害--尺寸中
	float	pushSnowBall_Small_Damage; //推雪球撞击伤害--尺寸小
	float	pushSnowBall_Large_Knockback; //推雪球撞击击退速度--尺寸大
	float	pushSnowBall_Middle_Knockback; //推雪球撞击击退速度--尺寸中
	float	pushSnowBall_Small_Knockback; //推雪球撞击击退速度--尺寸小
	int	pushSnowBall_Size_Change; //推雪球压到积雪或融化时尺寸变化
	int	pushSnowBall_Make_Size; //生成雪球的初始大小
	float	pushSnowBall_Hit_TemperatureChange; //雪球击中生物后温度变化

	float K_BUILD_BLOCK_EROSION;//建筑方块的腐蚀

	float K_TIPS_SHOWDURATION;
	float K_HP_LOW_TIPS;
	float K_FOOD_LOW_TIPS;
	float K_FOOD_HIGH_TIPS;
	float K_THIRST_LOW_TIPS;
	float K_TEMPERATURE_LOW_TIPS;
	float K_TEMPERATURE_TOO_LOW_TIPS;
	float K_TEMPERATURE_HIGH_TIPS;
	float K_TEMPERATURE_TOO_HIGH_TIPS;
	float K_RADIATION_HIGH_TIPS;
	float K_RADIATION_TOO_HIGH_TIPS;

	bool K_PRINT_ATTR_LOG;
	int K_MAX_MOB_COUNT;

	std::vector<std::array<int,3>> temperatureBuffConfig; //温度系统BUFF配置

	std::vector<int> redSoilPit;		//红土坑
	std::vector<int> redSoilLand;		//红土地
	std::vector<int> jaggedfernSkip;		//刺球蕨扎人伤害忽略生物ID

	std::vector<ItemDataConst> caravan_camel_leftpackinit;		//商队骆驼背包初始物品
	std::vector<ItemDataConst> caravan_camel_rightpackinit;		//商队骆驼背包初始物品
	std::vector<UnderGroundPalaceTreasure*> castlesTreasure;
	std::vector<UnderGroundPalaceTreasure*> chamberTreasure;
	std::vector<UnderGroundPalaceTreasure*> islandTreasure; // 岛屿宝箱
	std::vector<UnderGroundPalaceTreasure*> shipWreckTreasure; // 沉船群宝箱

	std::vector<int> coralEcosyPers;
	std::vector<int> sandCoverId;		//可以被沙子埋的方块ID

	float waterJumpDistance; //水中跳跃起来的距离
	float iceGroundGlissadeSpeed; //冰面滑行速度
	float growth_rate_min; //植物生长最低速率(%)
	int heat_logic_prop; //方块温度逻辑执行概率(%)
	std::vector<std::array<int, 3>> sleepConfig; //温度系统BUFF配置
	int manualEmitterInterval; //手持发射器间隔
	int hp_recover_per_unit_of_time;// --单位时间的生命恢复量
	int hp_recover_unit_time;//--生命恢复的单位时间
	int min_strength_for_hp_recover;//--可恢复生命时，体力的最小值
	int vortex_bigger_strain; //大漩涡的拉力
	int vortex_smaller_strain; //小漩涡的拉力
	int player_actionattrtip; //玩家权限移动等权限提示文本间隔

	float doubleWeaponDurPer; //--双持武器融合耐久度比例
	int fusionCageValPer; //--融合台创造晶体比例
	int fusionCageTime; //--融合台每个晶体的融合时间 tick
	float doubleWeaponNeedValPer; //--双持武器消耗创造晶体比例

	int magicResistCoefficient;		// 魔法抗性系数
	bool isOpenNewHpdecCalculate;	// 是否开启战斗系统新伤害计算
	int dampingControlA;			// 范围伤害控制参数1
	int dampingControlB;			// 范围伤害控制参数2

	jsonxx::Object vacantConfig; // 虚空生物相关数据

	int aiatk_frist_pre_tick;	//aiatk 第一次攻击准备时间
	int aiatk_interval_tick;	//aiatk 攻击总间隔时间
	float emitter_speedRate;    //发射器速度倍率

	int DungeonsDist;//刷怪房间隔
	std::list<int> DungeonsChest; // 刷怪房宝箱配置
	std::map<std::string, std::map<std::string, float> > Dungeons; //刷怪房不同层级不同房间刷新概率

	float sensitivity_coef_pc; // 镜头移动系数pc端
	float sensitivity_coef_mobile; // 镜头移动系数移动端
	UGCCFG ugcCfg;
	std::vector<ComputerOrderData*> ComputerOrderItemAddList;

	int SummonerFirstRoundCD; //  600tick    1~2波间隔
	int	SummonerSecondRoundCD; // 1800tick  2~3波间隔
	int	SummonerMainCD; //24000 tick - 激活倒计时：刷出BOSS后显示CD，总时间20分钟

	//扩展背包数量配置
	int level1ExtBackPack;
	int level2ExtBackPack;
	int level3ExtBackPack;

	//FPS手臂的滑动跟随模式参数
	float handFollow_thresholdValueHor_hip;
	float handFollow_thresholdValueVer_hip;
	float handFollow_slideSpeed_hip;
	float handFollow_slideRange_hip;
	float handFollow_thresholdValueHor_ads;
	float handFollow_thresholdValueVer_ads;
	float handFollow_slideSpeed_ads;
	float handFollow_slideRange_ads;
	float fishAnimYaw;
	float fishAnimPitch;
	float fishAnimRoll;


	// Soc地图生成参数
	int SocLargeMapSizeMin;      // 按chunk数量计算，313*313 ~= 5000x5000
	int SocMediumMapSizeMin;
	int SocSmallMapSizeMin;

	// Soc大地图小地形配置 - 平原
	int SocLargeMapSmallTerrain_plains_forest;
	int SocLargeMapSmallTerrain_plains_foresthill;
	int SocLargeMapSmallTerrain_plains_lake;
	int SocLargeMapSmallTerrain_plains_canyon;

	// Soc大地图小地形配置 - 沙漠
	int SocLargeMapSmallTerrain_deserts_deserthill;
	int SocLargeMapSmallTerrain_deserts_desertoasis;

	// Soc大地图小地形配置 - 冰原
	int SocLargeMapSmallTerrain_iceplains_icemountains;
	int SocLargeMapSmallTerrain_iceplains_frizzelake;

	// Soc中等地图小地形配置 - 平原
	int SocMediumMapSmallTerrain_plains_forest;
	int SocMediumMapSmallTerrain_plains_foresthill;
	int SocMediumMapSmallTerrain_plains_lake;
	int SocMediumMapSmallTerrain_plains_canyon;

	// Soc中等地图小地形配置 - 沙漠
	int SocMediumMapSmallTerrain_deserts_deserthill;
	int SocMediumMapSmallTerrain_deserts_desertoasis;

	// Soc中等地图小地形配置 - 冰原
	int SocMediumMapSmallTerrain_iceplains_icemountains;
	int SocMediumMapSmallTerrain_iceplains_frizzelake;

	// Soc小地图小地形配置 - 平原
	int SocSmallMapSmallTerrain_plains_forest;
	int SocSmallMapSmallTerrain_plains_foresthill;
	int SocSmallMapSmallTerrain_plains_lake;
	int SocSmallMapSmallTerrain_plains_canyon;

	// Soc小地图小地形配置 - 沙漠
	int SocSmallMapSmallTerrain_deserts_deserthill;
	int SocSmallMapSmallTerrain_deserts_desertoasis;

	// Soc小地图小地形配置 - 冰原
	int SocSmallMapSmallTerrain_iceplains_icemountains;
	int SocSmallMapSmallTerrain_iceplains_frizzelake;


	UnderGroundPalaceTreasure* createUGPTreasure()
	{
		return ENG_NEW_LABEL(UnderGroundPalaceTreasure,kMemGame);
	}
	void addIslandTreasure(UnderGroundPalaceTreasure* data)
	{
		islandTreasure.push_back(data);
		sort(islandTreasure.begin(), islandTreasure.end(), [&](const UnderGroundPalaceTreasure* a, const UnderGroundPalaceTreasure* b)->bool {
			return (a->probability < b->probability);
		});
	}
	void addCastlesTreasure(UnderGroundPalaceTreasure* data)
	{
		castlesTreasure.push_back(data);
		sort(castlesTreasure.begin(), castlesTreasure.end(), [&](const UnderGroundPalaceTreasure* a, const UnderGroundPalaceTreasure* b)->bool {
			return (a->probability < b->probability);
			});
	}
	void addChamberTreasure(UnderGroundPalaceTreasure* data)
	{
		chamberTreasure.push_back(data);
		sort(chamberTreasure.begin(), chamberTreasure.end(), [&](const UnderGroundPalaceTreasure* a, const UnderGroundPalaceTreasure* b)->bool {
			return (a->probability < b->probability);
			});
	}

	void addShipWreckTreasure(UnderGroundPalaceTreasure* data)
	{
		shipWreckTreasure.push_back(data);
		sort(shipWreckTreasure.begin(), shipWreckTreasure.end(), [&](const UnderGroundPalaceTreasure* a, const UnderGroundPalaceTreasure* b)->bool {
			return (a->probability < b->probability);
			});
	}

	void addCaravanCamelPack(bool left, int ItemID, int num, int probability)
	{
		if (left)
		{
			caravan_camel_leftpackinit.push_back(ItemDataConst(ItemID, num, probability));
		}
		else
		{
			caravan_camel_rightpackinit.push_back(ItemDataConst(ItemID, num, probability));
		}

	}
	void addRedSoilPit(int id)
	{
		redSoilPit.push_back(id);
	}
	void addRedSoilLand(int id)
	{
		redSoilLand.push_back(id);
	}
	void addJaggedfernSkip(int id)
	{
		jaggedfernSkip.push_back(id);
	}
	void addCoralEcosyPers(int per)
	{
		coralEcosyPers.push_back(per);
	}
	void addPirateChestTreasure(int ItemID, int num, int probability)
	{
		PirateChest_Treasure.push_back(ItemDataConst(ItemID, num, probability));
	}
	void addSandCoverId(int id)
	{
		sandCoverId.push_back(id);
	}

	void addTemperatureBuffConfig(int permanentBuff,int triggerBuff,int alpha)
	{
		std::array<int,3> arr = { permanentBuff ,triggerBuff ,alpha };
		temperatureBuffConfig.push_back(arr);
	}
	void addSleepConfig(int start, int end, int precent)
	{
		std::array<int, 3> arr = { start ,end ,precent };
		sleepConfig.push_back(arr);
	}

	void addVacantConfig(std::string vacantConfigStr)
	{
		vacantConfig.parse(vacantConfigStr);
	}

	void addDungeonsChest(int id)
	{
		DungeonsChest.push_back(id);
	}

	void addDungeons(const std::string & level, const std::string& roomtype, float p)
	{
		if (Dungeons.find(level) == Dungeons.end())
		{
			std::map<std::string, float> roomp;
			Dungeons[level] = roomp;
		}
		Dungeons[level][roomtype] = p;
	}

	void addSensitivityCoefPc(float fSensitivity_coef_pc)
	{
		sensitivity_coef_pc = fSensitivity_coef_pc;
	}
	
	void addSensitivityCoefMobile(float faddSensitivityCoefMobile)
	{
		sensitivity_coef_mobile = faddSensitivityCoefMobile;
	}

	void addComputerOrderItemAddList(int Id, const std::string& order, const std::string& prefabId,int num)
	{
		ComputerOrderData* def = ENG_NEW_LABEL(ComputerOrderData, kMemConfigDef);
		def->itemId = Id;
		def->order = order;
		def->prefabId = prefabId;
		def->num = num;
		ComputerOrderItemAddList.push_back(def);
	}
	void clearAllComputerOrderItemAddList()
	{
		for (int i = 0; i < ComputerOrderItemAddList.size(); i++)
		{
			ENG_DELETE_LABEL(ComputerOrderItemAddList[i], kMemConfigDef);
		}
		ComputerOrderItemAddList.clear();
	}

	~ConstAtLua()
	{
		for (auto item : castlesTreasure)
		{
			ENG_DELETE_LABEL(item, kMemGame);
		}
		castlesTreasure.clear();
		for (auto item : chamberTreasure)
		{
			ENG_DELETE_LABEL(item, kMemGame);
		}
		chamberTreasure.clear();
		for (auto item : islandTreasure)
		{
			OGRE_DELETE(item);
		}
		islandTreasure.clear();

		for (auto item : shipWreckTreasure)
		{
			ENG_DELETE_LABEL(item, kMemGame);
		}
		shipWreckTreasure.clear();

		caravan_camel_leftpackinit.clear();
		caravan_camel_rightpackinit.clear();
		redSoilPit.clear();
		redSoilLand.clear();
		PirateChest_Treasure.clear();
		jaggedfernSkip.clear();
		coralEcosyPers.clear();
		sandCoverId.clear();
		temperatureBuffConfig.clear();
		sleepConfig.clear();
		vacantConfig.reset();
		DungeonsChest.clear();
		Dungeons.clear();
		clearAllComputerOrderItemAddList();
	}
};

//struct SocOneOnOne
//{
//	int source_id;
//	int target_id;
//};
//
//struct SocOneSourceManyTarget
//{
//	int source_id;
//	std::vector<int> target_id;
//};
//
//struct SocOneTargetManySource
//{
//	int target_id;
//	std::vector<int> source_id;
//};
//
//struct SocOneToManyRange
//{
//	int source_id;
//	std::vector<int> target_id;
//	int rangex;
//	int rangey;
//	int rangez;
//};

struct SocSourceData
{
	int _source_id;
	int _rangex;
	int _rangey;
	int _rangez;
};

struct SocRandNpcItemData
{
	int _id;
	int _probability;
};

struct SocRandNpcData
{
	int _totalProbability;
	std::vector<SocRandNpcItemData> _vRandNpcItemList;
};

struct SocConstAtLua
{
	int soctestvalue;


	int endOfPOD;

	std::vector<int> vLinkBlockIdList;

	//std::vector<SocOneOnOne> vOneOnOneLinkList;
	//std::vector<SocOneSourceManyTarget> vOneSourceToManyTargetLinkList;
	//std::vector<SocOneTargetManySource> vOneTargetToManySourceLinkList;
	//std::vector<SocOneToManyRange> vOneToManyRangeList;

	std::vector<int> vTargetIdList;
	std::vector<SocSourceData> vSourceList;
	std::unordered_map<int, SocRandNpcData> vRandNpcList;

	void addSourceData(int source_id, int rangex, int rangey, int rangez)
	{
		SocSourceData obj;
		obj._source_id = source_id;
		obj._rangex = rangex;
		obj._rangey = rangey;
		obj._rangez = rangez;
		vSourceList.push_back(obj);
	}

	void addRandNpc(int blockid, int npcid, int probability)
	{
		auto iter = vRandNpcList.find(blockid);
		if (iter == vRandNpcList.end())
		{
			iter = vRandNpcList.insert(std::make_pair(blockid, SocRandNpcData{ 0, {} })).first;
		}

		iter->second._vRandNpcItemList.push_back(SocRandNpcItemData{ npcid, probability });
		iter->second._totalProbability += probability;
	}

/* 留下作为数据传导格式参考
	void addOneOnOneLink(int source_id, int target_id)
	{
		SocOneOnOne obj;
		obj.source_id = source_id;
		obj.target_id = target_id;
		vOneOnOneLinkList.push_back(obj);
	}

	void addOneSourceToManyTarget(int source_id)
	{
		SocOneSourceManyTarget obj;
		obj.source_id = source_id;
		obj.target_id.clear();
		vOneSourceToManyTargetLinkList.push_back(obj);
	}

	void addTargetIdToLastOneSourceToMany(int target_id)
	{
		if (!vOneSourceToManyTargetLinkList.empty())
		{
			vOneSourceToManyTargetLinkList.back().target_id.push_back(target_id);
		}
	}

	void addOneTargetToManySource(int target_id)
	{
		SocOneTargetManySource obj;
		obj.target_id = target_id;
		obj.source_id.clear();
		vOneTargetToManySourceLinkList.push_back(obj);
	}

	void addSourceIdToLastOneTargetToMany(int source_id)
	{
		if (!vOneTargetToManySourceLinkList.empty())
		{
			vOneTargetToManySourceLinkList.back().source_id.push_back(source_id);
		}
	}

	void addOneToManyRange(int source_id, int rangex, int rangey, int rangez)
	{
		SocOneToManyRange obj;
		obj.source_id = source_id;
		obj.rangex = rangex;
		obj.rangey = rangey;
		obj.rangez = rangez;
		obj.target_id.clear();
		vOneToManyRangeList.push_back(obj);
	}

	void addTargetIdToLastOneToMany(int target_id)
	{
		if (!vOneToManyRangeList.empty())
		{
			vOneToManyRangeList.back().target_id.push_back(target_id);
		}
	}
	*/
}; 

//tolua_end
#endif

