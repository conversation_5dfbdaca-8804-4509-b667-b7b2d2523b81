
#ifndef __AIFLYPANIC_H__
#define __AIFLYPANIC_H__

#include <OgreWCoord.h>

#include "AIBase.h"


class LivingLocoMotion;
//恐慌
class AIFlyPanic :public AIBase //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	AIFlyPanic(ClientMob *pActor, float speed, int dis, float maxhp);
	~AIFlyPanic() {}
	virtual bool willRun();
	virtual bool continueRun();
	virtual void start();
	virtual void reset();

	virtual bool canInterruptInteract() { return true; }
	virtual bool canInterruptedByInteract() { return false; }
	virtual AI_MOTION_TYPE getMotionType() { return INJURED_PANIC; }
	//tolua_end
private:
	float m_PanicSpeed;			// ����ٶ�
	float m_PanicHPPer;			// �������Ѫ���ٷֱ� 
	int m_MaxDistance;			// ��ܾ��루����������룬��ܽ�����
	LivingLocoMotion* m_pLivingLocomotion;
	WCoord m_StartPos;			// ��ʼ���λ��
}; //tolua_exports

#endif
