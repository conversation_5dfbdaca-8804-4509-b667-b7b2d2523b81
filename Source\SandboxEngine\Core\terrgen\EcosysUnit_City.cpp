#include "EcosysUnit_City.h"
#include "WorldManager.h"
#include "LuaInterfaceProxy.h"
#include "EcosysBigBuildBuilder.h"
#include "EcosysBuildHelp.h"
#include "GameStatic.h"
#include "chunk.h"
#include "LuaInterfaceProxy.h"
#include "ClientInfoProxy.h"
#include "SandBoxManager.h"
#include "Mgr/BuildManager/BuildFillParamter.h"
#include "IClientPlayer.h"
#include "Mgr/CityManager/CityData.h"
#include "ChunkGenerator.h"
#include "EcosysUnit_RoadBuild.h"
#include "IPlayerControl.h"

EcosysUnitCityBuild::EcosysUnitCityBuild()
{

}

EcosysUnitCityBuild::~EcosysUnitCityBuild()
{

}

bool EcosysUnitCityBuild::addToWorld(WorldProxy* pworld, ChunkRandGen& randgen, const WCoord& blockpos)
{
	return false;
}

/**
 * 检查区块是否平整并返回合适的高度
 * @param chunk 待检查的区块指针
 * @param outHeight [out] 如果区块平整,输出区块最低点高度
 * @param maxHeightDiff [可选] 允许的最大高度差,默认为2
 * @return true:区块平整 false:区块不平整或参数无效
 */
static bool getRealChunkHeight(Chunk* chunk, int& outHeight, int maxHeightDiff = 2) 
{
	if (!chunk || CHUNK_BLOCK_X <= 0 || CHUNK_BLOCK_Z <= 0) return false;
	
	// 在chunk中均匀取16个采样点
	const int SAMPLE_COUNT = 4; // 每行/列采样4个点
	const int STEP = CHUNK_BLOCK_X / SAMPLE_COUNT;
	
	int minHeight = INT_MAX;
	int maxHeight = INT_MIN;
	
	// 遍历采样点获取高度
	for (int x = 0; x < CHUNK_BLOCK_X; x += STEP) {
		for (int z = 0; z < CHUNK_BLOCK_Z; z += STEP) {
			int height = EcosysBuildHelp::getRealHeightChunk(chunk, x, z);
			minHeight = std::min(minHeight, height);
			maxHeight = std::max(maxHeight, height);
			
			// 如果高度差已经超过允许值，可以提前返回false
			if (maxHeight - minHeight > maxHeightDiff) {
				return false;
			}
		}
	}
	
	// 额外检查边界点，确保边界也是平整的
	const int lastX = CHUNK_BLOCK_X - 1;
	const int lastZ = CHUNK_BLOCK_Z - 1;
	int borderPoints[][2] = {
		{0, 0}, {0, lastZ}, {lastX, 0}, {lastX, lastZ},
		{lastX/2, 0}, {lastX/2, lastZ}, {0, lastZ/2}, {lastX, lastZ/2}
	};
	
	for (const auto& point : borderPoints) {
		int height = EcosysBuildHelp::getRealHeightChunk(chunk, point[0], point[1]);
		minHeight = std::min(minHeight, height);
		maxHeight = std::max(maxHeight, height);
		
		if (maxHeight - minHeight > maxHeightDiff) {
			return false;
		}
	}
	
	// 如果所有采样点的高度差都在允许范围内，返回最低点的高度
	outHeight = minHeight;
	return true;
}

void EcosysUnitCityBuild::onWorldLoaded(World* world)
{
	if (!m_CalculatCity)
	{
		m_CalculatCity = true;
		world->intermingleRespawnList();
		//world->setSOCCreateMap(true);
		ProcessCityGen(world);
		world->getCityMgr()->onCityGenEnd();

		//GetEcosysUnitRoadBuild().onWorldLoaded(world);
		//GenSmallBuilding(world);
	}
}

void EcosysUnitCityBuild::onFileLoad(World* world)
{
	const auto& cityData = world->getCityMgr()->GetAllCityData();
	if (cityData.size() > 5)
		m_CalculatCity = true;
}

// 定义边缘安全距离和城市间最小距离
const int EDGE_MARGIN = 30;  // 距离地图边缘至少25个区块
const int MIN_CITY_DISTANCE = 15;  // 城市之间至少间隔5个区块

bool EcosysUnitCityBuild::addToWorld(World* world)
{
	//m_Chunk = index;
	if (world == nullptr) 
	{
		return false;
	}
	// 遍历地图范围
	// auto provider = world->getChunkProvider();
	// int startX = provider->getStartChunkX();
	// int endX = provider->getEndChunkX();
	// int startZ = provider->getStartChunkZ();
	// int endZ = provider->getEndChunkZ();
	// ChunkIndex startci, endci;
	// provider->GetPlaneRange(world, startci, endci);

	// for (int x = startci.x + 2; x <= endci.x - 5; x++)
	// {
	// 	for (int z = startci.z + 2; z <= endci.x - 5; z++)
	// 	{
	// 		addToWorld(world, ChunkIndex(x, z));
	// 	}
	// }
	// ProcessCityGen(world);
	// world->getCityMgr()->onCityGenEnd();
	return true;
}



bool EcosysUnitCityBuild::addToWorld(World* world, ChunkIndex index, bool force)
{
	//这是在dofunction里调用的,chunk一定是加载了的
	// Chunk* curChunk = world->getChunk(index);
	// if (curChunk == nullptr)
	// {
	// 	return false;
	// }
	// if (!world->getCityMgr()) return false;

	// if (curChunk->m_chunkSpecialBiome.empty())
	// {
	// 	// return addSingleBuildToWorld(world, index, force);
	// }
	// else
	// {
	// 	// if (curChunk->getBiomeID(7, 7) != BIOME_DESERT_CITY) return false;
	// 	if (!GetCityConfigInterface()) return false;

	// 	for (auto& p : curChunk->m_chunkSpecialBiome)
	// 	{
	// 		m_CandidateCitys.push_back({index, p});
	// 	}
	// 	return true;
	// }
	return false;
}

/**
 * 检查城市地形是否平整且合适
 * @param world 世界指针
 * @param cityData 城市位置和大小数据
 * @param outBaseHeight [出参] 如果地形合适，输出基准高度
 * @return true:地形合适 false:地形不合适
 */
bool EcosysUnitCityBuild::checkCityTerrainSuitability(World* world, const ChunkSpecialBiomeData& p, int& outBaseHeight)
{
	bool foundSuitableHeight = false;
	// const auto pcityData = GetCityConfigInterface()->getCityDataByIndex(
	// 	GetCityConfigInterface()->getCityIndex(p.originBomeId, p.configId));
	// if (!pcityData) return false;

	// // 确保城市范围内的所有区块都已加载
	// std::vector<Chunk*> cityChunks;
	// for (int x = p.leftDown.x; x < p.leftDown.x + pcityData->m_citySize.x(); x++) {
	// 	for (int z = p.leftDown.z; z < p.leftDown.z + pcityData->m_citySize.y(); z++) {
	// 		Chunk* chunk = world->getChunk(ChunkIndex(x, z));
	// 		if (!chunk) continue;
	// 		cityChunks.push_back(chunk);
	// 	}
	// }

	// // 检查所有区块的平整度，找到合适的基准高度
	// int baseHeight = 0;
	// int minHeight = 100000;

	// // 先检查中心区块
	// int centerX = p.leftDown.x + pcityData->m_citySize.x() / 2;
	// int centerZ = p.leftDown.z + pcityData->m_citySize.y() / 2;
	// Chunk* centerChunk = world->getChunk(ChunkIndex(centerX, centerZ));
	// if (centerChunk && getRealChunkHeight(centerChunk, baseHeight)) {
	// 	// 以中心区块的高度作为基准，检查其他区块
	// 	bool allChunksFlat = true;
	// 	for (Chunk* chunk : cityChunks) {
	// 		int chunkHeight;
	// 		if (!getRealChunkHeight(chunk, chunkHeight, 10)) {
	// 			allChunksFlat = false;
	// 			GEN_LOG_INFO("CityCheck Chunk is not flat! height:%d", chunkHeight);
	// 			break;
	// 		}
	// 		minHeight = std::min(minHeight, chunkHeight);
	// 		// 检查与基准高度的差异
	// 		if (abs(chunkHeight - baseHeight) > 10) {
	// 			allChunksFlat = false;
	// 			GEN_LOG_INFO("CityCheck Chunk diff center height ! baseHeight:%d chunkHeight:%d", baseHeight, chunkHeight);
	// 			break;
	// 		}
	// 	}

	// 	if (allChunksFlat) {
	// 		foundSuitableHeight = true;
	// 	}
	// }

	// if (foundSuitableHeight) {
	// 	outBaseHeight = baseHeight;
	// } else {
	// 	GEN_LOG_INFO("CityCheck Chunk is not suit!");
	// }
	
	return foundSuitableHeight;
}

bool EcosysUnitCityBuild::ProcessCityGen(World* world)
{
	// 获取地图边界
	auto provider = world->getChunkProvider();
	ChunkIndex startci, endci;
	provider->GetPlaneRange(world, startci, endci);
	int edgeSidex = endci.x - startci.x;
	int edgeSidez = endci.z - startci.z;
	edgeSidex = std::min(edgeSidex, EDGE_MARGIN);
	edgeSidez = std::min(edgeSidez, EDGE_MARGIN);
	int startX = startci.x + edgeSidex;
	int endX = endci.x - edgeSidex;
	int startZ = startci.z + edgeSidez;
	int endZ = endci.z - edgeSidez;

	auto mapsx = provider->getStartChunkX();
	auto mapsz = provider->getStartChunkZ();
	auto mapex = provider->getEndChunkX();
	auto mapez = provider->getEndChunkZ();
	int realChunkX = mapex - mapsx + 1;
	int realChunkZ = mapez - mapsz + 1;

	ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();
	int mapSizeType = MAP_ERROR_SIZE;
	if (realChunkX >= lua_const->SocLargeMapSizeMin && realChunkZ >= lua_const->SocLargeMapSizeMin)
	{
		mapSizeType = MAP_BIG_SIZE;
	}
	else if (realChunkX < lua_const->SocLargeMapSizeMin && realChunkZ < lua_const->SocLargeMapSizeMin
		&& realChunkX >= lua_const->SocMediumMapSizeMin && realChunkZ >= lua_const->SocMediumMapSizeMin)
	{
		mapSizeType = MAP_MIDDLE_SIZE;
	}
	else if (realChunkX >= lua_const->SocSmallMapSizeMin && realChunkZ >= lua_const->SocSmallMapSizeMin)
	{
		mapSizeType = MAP_SMALL_SIZE;
	}

	const CityBuildConfig::CityDataConfig* cityData = GetCityConfigInterface()->getCityDataByIndex(0);
	if (!cityData)
	{
		GEN_LOG_INFO("ProcessCityGen cityData is nullptr");
		return false;
	}

	std::vector<std::string> availableBuilds = cityData->m_SingleBuilds;

	int maxCityCount = GetCityConfigInterface()->GetMaxCityCount();

	int genCityNum = 0;
	for (const auto& singleBuild : availableBuilds)
	{
		auto conf = GetCityConfigInterface()->getSingleBuildDataByName(singleBuild);
		if (!conf) continue;

		Rainbow::Vector2i range = conf->m_range;
		int rangeMax = std::max(range.x(), range.y());
		int buildEndx = endX - rangeMax;
		int buildEndz = endZ - rangeMax;
		int maxCount = mapSizeType == MAP_ERROR_SIZE ? conf->m_maxCount : mapSizeType == MAP_BIG_SIZE ? conf->m_largeMaxCount : mapSizeType == MAP_MIDDLE_SIZE ? conf->m_mediumMaxCount : conf->m_smallMaxCount;
		std::vector<std::vector<ChunkIndex>> buildZoneList;
		buildZoneList.push_back({ ChunkIndex(startX, startZ) ,ChunkIndex(buildEndx, buildEndz) });

		Rainbow::Vector3i bsize = conf->m_size;
		Rainbow::Vector2i realBRange(bsize.x(), bsize.z());
		//realBRange.x() = conf->m_size.x();
		//realBRange.y() = conf->m_size.z();
		bool isSeaBuildCity = false;
		if (conf->m_vPlaceBiomeList.size() > 0 && conf->m_vPlaceBiomeList[0] != -1)
		{
			if (conf->m_vPlaceBiomeList.size() == 2 && conf->m_vPlaceBiomeList[0] == BIOME_BEACH && conf->m_vPlaceBiomeList[1] == BIOME_OCEAN)
			{//小码头
				//isSeaBuildCity = true;//现需求是每边一个而海建筑只有这个。所以不需要判断是否和其他建筑重叠
				//biomeParamMap.clear();

				int coastalLeftFarthest = provider->getSideCoastal(0, 0);
				int coastalLeftClosest = provider->getSideCoastal(0, 1);

				int coastalRightFarthest = provider->getSideCoastal(1, 0);
				int coastalRightClosest = provider->getSideCoastal(1, 1);

				int coastalDownFarthest = provider->getSideCoastal(2, 0);
				int coastalDownClosest = provider->getSideCoastal(2, 1);

				int coastalUpFarthest = provider->getSideCoastal(3, 0);
				int coastalUpClosest = provider->getSideCoastal(3, 1);


				int coastalLeft = provider->getSideCoastalAverage(0);
				int coastalRight = provider->getSideCoastalAverage(1);
				int coastalDown = provider->getSideCoastalAverage(2);
				int coastalUp = provider->getSideCoastalAverage(3);

				buildZoneList.clear();
				int sideLong = std::max(range.x(), range.y())/* / 2 * 3*/;
				int left = coastalLeftFarthest - sideLong / 2;
				left = left >= (mapsx + g_nMapSafeNoEnterRange) ? left : (mapsx + g_nMapSafeNoEnterRange);
				int right = coastalRightFarthest + sideLong / 2;
				right = right <= (mapex - g_nMapSafeNoEnterRange) ? right : (mapex - g_nMapSafeNoEnterRange);
				int down = coastalDownFarthest - sideLong / 2;
				down = down >= (mapsz + g_nMapSafeNoEnterRange) ? down : (mapsz + g_nMapSafeNoEnterRange);
				int up = coastalUpFarthest + sideLong / 2;
				up = up <= (mapez - g_nMapSafeNoEnterRange) ? up : (mapez - g_nMapSafeNoEnterRange);

				//必须只有四个方向.vector中的idx是建筑物的朝向
				buildZoneList.push_back({ ChunkIndex(left, coastalDown) , ChunkIndex(coastalLeftClosest + sideLong / 2, coastalUp) });//left，地图西部生成区域
				buildZoneList.push_back({ ChunkIndex(coastalRightClosest - sideLong / 2, coastalDown) ,ChunkIndex(right, coastalUp) });//right，地图东部生成区域
				buildZoneList.push_back({ ChunkIndex(coastalLeft, down) ,ChunkIndex(coastalRight, coastalDownClosest + sideLong / 2) });//down，地图南部生成区域
				buildZoneList.push_back({ ChunkIndex(coastalLeft, coastalUpClosest - sideLong / 2) ,ChunkIndex(coastalRight, up) });//up,地图北部生成区域

				LOG_INFO_BUILD("#####Create Map record xiaogangkou, mapStart x = %d, z = %d, mapEnd x = %d, z = %d startci x = %d, z = %d;  endci x = %d, z = %d; width = %d, long = %d, sidelong = %d"
					, mapsx, mapsz, mapex, mapez, coastalLeft, coastalDown, coastalRight, coastalUp, range.x(), range.y(), sideLong);
				LOG_INFO_BUILD("#####Create Map record xiaogangkou, left = %d, right = %d, down = %d, up = %d" , left, right, down, up);
				LOG_INFO_BUILD("#####Create Map record xiaogangkou, leftfarthest = %d, leftclosest = %d, rightfarthest = %d, rightclosest = %d, downfarthest = %d, downclosest = %d, upfarthest = %d, upclosest = %d" 
				, coastalLeftFarthest, coastalLeftClosest, coastalRightFarthest, coastalRightClosest, coastalDownFarthest, coastalDownClosest, coastalUpFarthest, coastalUpClosest);

				LOG_INFO_BUILD("#####Create Map record xiaogangkou, left ChunkIndex start x = %d, z = %d, end x = %d, z = %d", buildZoneList[0][0].x, buildZoneList[0][0].z, buildZoneList[0][1].x, buildZoneList[0][1].z);
				LOG_INFO_BUILD("#####Create Map record xiaogangkou, right ChunkIndex start x = %d, z = %d, end x = %d, z = %d", buildZoneList[1][0].x, buildZoneList[1][0].z, buildZoneList[1][1].x, buildZoneList[1][1].z);
				LOG_INFO_BUILD("#####Create Map record xiaogangkou, down ChunkIndex start x = %d, z = %d, end x = %d, z = %d", buildZoneList[2][0].x, buildZoneList[2][0].z, buildZoneList[2][1].x, buildZoneList[2][1].z);
				LOG_INFO_BUILD("#####Create Map record xiaogangkou, up ChunkIndex start x = %d, z = %d, end x = %d, z = %d", buildZoneList[3][0].x, buildZoneList[3][0].z, buildZoneList[3][1].x, buildZoneList[3][1].z);
			}
		}
		int randStartX = startX;
		int randStartZ = startZ;
		int randEndX = buildEndx;
		int randEndZ = buildEndz;

		int successCount = 0;
		int createCount = 0;
		while(createCount < maxCount)
		{
			int buildDir = -1;
			bool isRandDir = false;
			if (isValidDir(conf->m_dir))
			{
				buildDir = conf->m_dir;
			}
			else
			{
				isRandDir = true;
				buildDir = m_randGen.get(DIR_NEG_X, DIR_POS_Z);
			}
			int maxTryCount = 200;
			if (buildZoneList.size() == 1)
			{
				randStartX = buildZoneList[0][0].x;
				randStartZ = buildZoneList[0][0].z;
				randEndX = buildZoneList[0][1].x;
				randEndZ = buildZoneList[0][1].z;
			}
			else
			{
				if (isRandDir) buildDir = createCount % 4;// rand() % biomeParamMap.size();
				auto list = buildZoneList[buildDir];
				randStartX = list[0].x;
				randStartZ = list[0].z;
				randEndX = list[1].x;
				randEndZ = list[1].z;
			}
			Rainbow::Vector2i realCRange = range;
			if (buildDir == DIR_NEG_X || buildDir == DIR_POS_X)
			{
				realCRange.x() = range.y();
				realCRange.y() = range.x();
				realBRange.x() = bsize.z();
				realBRange.y() = bsize.x();
			}

			auto createCity = [&](ChunkIndex chunkIndex)
			{
				if (world->getCityMgr()->checkPosCanPlaceCity(chunkIndex, realCRange.x(), realCRange.y(), conf->m_vPlaceBiomeList, !isSeaBuildCity, buildDir))
				{
					if (addSingleBuildToWorld(world, chunkIndex, singleBuild, true, true, buildDir))
					{
						world->syncLoadChunk(chunkIndex, GetIPlayerControl() ? GetIPlayerControl()->GetChunkViewerForCtrl() : nullptr);
						Chunk* centerChunk = world->getChunk(chunkIndex);
						int baseHeight = 0;
						if (centerChunk)
							getRealChunkHeight(centerChunk, baseHeight);

						LOG_INFO_BUILD("#####Create Map record addNewCity idx = %d buildname:%s pos x:%d z:%d baseHeight:%d ", genCityNum, singleBuild.c_str(), chunkIndex.x, chunkIndex.z, baseHeight);
						world->getCityMgr()->addNewCity(::CityData(chunkIndex, baseHeight, 1, realCRange, genCityNum, realBRange));
						genCityNum++;
						return true;
					}
				}
				return false;
			};

			bool isCreate = false;
			while (maxTryCount > 0)
			{
				ChunkIndex chunkIndex = ChunkIndex(rand() % (randEndX - randStartX + 1) + randStartX, rand() % (randEndZ - randStartZ + 1) + randStartZ);
				if (createCity(chunkIndex))
				{
					isCreate = true;
					break;
				}
				maxTryCount--;
			}
			if (!isCreate)
			{
				for (int x = randStartX; x <= randEndX; x++)
				{
					for (int z = randStartZ; z <= randEndZ; z++)
					{
						if (createCity(ChunkIndex(x, z)))
						{
							isCreate = true;
							break;
						}
					}
					if (isCreate) break;
				}
			}
		
			if (!isCreate)
			{
				LOG_INFO_BUILD("#####Create Map record Create fail CityBuid id = %s" , singleBuild.c_str());
			}
			else
			{
				LOG_INFO_BUILD("#####Create Map record Create success CityBuid id = %s" , singleBuild.c_str());
				successCount++;
			}
			createCount++;
		}

		LOG_INFO_BUILD("#####Create Map record Create CityBuid id = %s, maxcount = %d, successCount = %d" , singleBuild.c_str(), maxCount, successCount);


		//for (int i = 0; i < maxCount; i++)
		//{
		//	if (!world->getCityMgr()->checkCanAddSingleBuild(singleBuild)) break;
		//	int buildDir = -1;
		//	if (isValidDir(conf->m_dir))
		//	{
		//		buildDir = conf->m_dir;
		//	}
		//	else
		//	{
		//		buildDir = m_randGen.get(DIR_NEG_X, DIR_POS_Z);
		//	}
		//	int maxTryCount = 100;
		//	if (buildZoneList.size() == 1)
		//	{
		//		randStartX = buildZoneList[0][0].x;
		//		randStartZ = buildZoneList[0][0].z;
		//		randEndX = buildZoneList[0][1].x;
		//		randEndZ = buildZoneList[0][1].z;
		//	}
		//	else
		//	{
		//		buildDir = i % 4;// rand() % biomeParamMap.size();
		//		auto list = buildZoneList[buildDir];
		//		randStartX = list[0].x;
		//		randStartZ = list[0].z;
		//		randEndX = list[1].x;
		//		randEndZ = list[1].z;
		//		maxTryCount = 2000;
		//	}
		//	Rainbow::Vector2i realCRange = range;
		//	if (buildDir == DIR_NEG_X || buildDir == DIR_POS_X)
		//	{
		//		realCRange.x() = range.y();
		//		realCRange.y() = range.x();
		//		realBRange.x() = bsize.z();
		//		realBRange.y() = bsize.x();
		//	}
		//	while (maxTryCount > 0)
		//	{
		//		ChunkIndex chunkIndex = ChunkIndex(rand() % (randEndX - randStartX + 1) + randStartX, rand() % (randEndZ - randStartZ + 1) + randStartZ);
		//		if (world->getCityMgr()->checkPosCanPlaceCity(chunkIndex, realCRange.x(), realCRange.y(), conf->m_vPlaceBiomeList, buildDir))
		//		{
		//			// 如果建筑出现次数少于限制，并且可以添加，则添加
		//			if (buildZoneList.size() == 4)
		//			{
		//				GEN_LOG_INFO("ProcessCityGen matou : chunkidxX = %d, chunkidxZ = %d, dir = %d", chunkIndex.x*16, chunkIndex.z*16, buildDir);
		//			}
		//			GEN_LOG_INFO("ProcessCityGen addSingleBuildToWorld x:%d z:%d build:%s", chunkIndex.x, chunkIndex.z, singleBuild.c_str());
		//			if (addSingleBuildToWorld(world, chunkIndex, singleBuild, true, true, buildDir))
		//			{
		//				world->syncLoadChunk(chunkIndex, GetIPlayerControl() ? GetIPlayerControl()->GetChunkViewerForCtrl() : nullptr);
		//				Chunk* centerChunk = world->getChunk(chunkIndex);
		//				int baseHeight = 0;
		//				if (centerChunk)
		//					getRealChunkHeight(centerChunk, baseHeight);
		//				GEN_LOG_INFO("ProcessCityGen addNewCity x:%d z:%d baseHeight:%d buildname:%s", chunkIndex.x, chunkIndex.z, baseHeight, singleBuild.c_str());
		//				world->getCityMgr()->addNewCity(::CityData(chunkIndex, baseHeight, 1, realCRange, genCityNum, realBRange));
		//				genCityNum++;
		//				break;
		//			}
		//		}
		//		maxTryCount--;
		//	}
		//}

	}
	 
	LOG_INFO_BUILD("#####Create Map record CreateAll CityBuid Count = %d" , genCityNum);
	//GEN_LOG_INFO("EcosysUnitCityBuild selectedCities genCityNum:%d", genCityNum);
	return true;
}

void EcosysUnitCityBuild::leaveWorld()
{
	m_CandidateCitys.clear();
	m_CalculatCity = false;
	m_GenSmallBuilding = false;
}

void EcosysUnitCityBuild::GenSmallBuilding(World* world)
{
    if (!world || !GetCityConfigInterface()) {
        return;
    }
	if (m_GenSmallBuilding) return;
	m_GenSmallBuilding = true;
    
    // 获取单体建筑配置
    const CityBuildConfig::SingleBuildConfig& singleConfigs = GetCityConfigInterface()->getSingleBuildConfig();
    if (singleConfigs.m_builds.empty()) {
        return;
    }
    
    // 统计可用的小型建筑数量
    std::vector<std::string> availableBuildings;
    int validBuildingCount = 0;
    
    // 暂时使用建筑数量超过100的
    for (int i = 0; i < singleConfigs.m_builds.size(); i++) {
        const auto& build = singleConfigs.m_builds[i];
        if (build.m_maxCount > 100) {
            availableBuildings.push_back(build.m_fileName.c_str());
        }
    }
    
    // 如果没有可用的建筑，直接返回
    if (availableBuildings.empty()) {
        GEN_LOG_INFO("GenSmallBuilding: No available small buildings found");
        return;
    }
    
    // 从道路两侧获取可放置建筑的点
    // 设置参数：最小距离40，侧向偏移10，总计生成20个点
    std::vector<WCoord> buildingPoints = GetEcosysUnitRoadBuild().generateBuildingPointsAlongRoads(
        world,
        50.0f,  // 建筑间最小距离
        30.0f,  // 建筑距离道路的侧向偏移距离
        1000      // 最多生成建筑点
    );
    
    // 如果没有获取到建筑点，直接返回
    if (buildingPoints.empty()) {
        GEN_LOG_INFO("GenSmallBuilding: No valid building points found along roads");
        return;
    }
    
    // 设置随机种子，确保生成的结果可重现
    ChunkRandGen randGen;
    randGen.setSeed64(world->getChunkSeed(1,1) ^ 0x12345678);
    
    // 统计成功放置的建筑数量
    int placedCount = 0;
    
    // 遍历所有建筑点，尝试放置建筑
    for (const auto& point : buildingPoints) {
        // 获取对应的区块索引
        ChunkIndex chunkIdx(BlockDivSection(point.x), BlockDivSection(point.z));
		world->syncLoadChunk(chunkIdx, GetIPlayerControl() ? GetIPlayerControl()->GetChunkViewerForCtrl() : nullptr);

        // 随机选择一个建筑
        std::string selectedBuilding = availableBuildings[randGen.nextInt(availableBuildings.size())];
        
        // 尝试放置建筑
        if (addSingleBuildToWorld(world, chunkIdx, selectedBuilding, false)) {
            placedCount++;
            GEN_LOG_INFO("GenSmallBuilding: Successfully placed building %s at (%d, %d, %d) chunkIdx:%d, %d", 
                selectedBuilding.c_str(), point.x, point.y, point.z, chunkIdx.x, chunkIdx.z);
        }
    }
    
    GEN_LOG_INFO("GenSmallBuilding: Placed %d buildings out of %d points", placedCount, buildingPoints.size());
}

bool EcosysUnitCityBuild::_addSingleBuildToWorld(World* world, Chunk* chunk, const ChunkIndex& index, const std::string& buildName, bool force, bool add2City, int buildDir)
{
	//m_Chunk = index;
	if (world == nullptr || !GetCityConfigInterface())
	{
		GEN_LOG_INFO("EcosysUnitCityBuild _addSingleBuildToWorld world == nullptr || !GetCityConfigInterface() buildName:%s", buildName.c_str());
		return false;
	}

	const auto& singleConfig = GetCityConfigInterface()->getSingleBuildConfig();

	const CityBuildConfig::SingleBuildData* pBuildData = GetCityConfigInterface()->getSingleBuildDataByName(buildName);
	if (!pBuildData)
	{
		GEN_LOG_INFO("EcosysUnitCityBuild _addSingleBuildToWorld pBuildData == nullptr buildName:%s", buildName.c_str());
		return false;
	}

	int crx = pBuildData->m_range.x();
	int crz = pBuildData->m_range.y();
	int brx = pBuildData->m_size.x();
	int brz = pBuildData->m_size.z();
	if (buildDir == DIR_NEG_X || buildDir == DIR_POS_X)
	{
		crx = pBuildData->m_range.y();
		crz = pBuildData->m_range.x();
		brx = pBuildData->m_size.z();
		brz = pBuildData->m_size.x();
	}
	// 遍历加载chunk
	ChunkIndex chunkIndex = index;
	//int maxRange = std::max(pBuildData->m_range.x(), pBuildData->m_range.y());
	for (int x = 0; x < crx; x++)
	{
		for (int z = 0; z < crz; z++)
		{
			chunkIndex = ChunkIndex(index.x + x, index.z + z);
			world->syncLoadChunk(chunkIndex, GetIPlayerControl() ? GetIPlayerControl()->GetChunkViewerForCtrl() : nullptr);
		}
	}

	Chunk* curChunk = world->getChunk(index);
	if (curChunk == nullptr)
	{
		GEN_LOG_INFO("EcosysUnitCityBuild _addSingleBuildToWorld curChunk == nullptr buildName:%s", buildName.c_str());
		assert(0);
		return false;
	}

	WCoord startPos = WCoord(index.x * 16, EcosysBuildHelp::getRealHeightChunk(curChunk, 7, 7), index.z * 16);
	GEN_LOG_INFO("EcosysUnitCityBuild _addSingleBuildToWorld startPos:%f, %f, %f", startPos.x, startPos.y, startPos.z);
	//先看下单个chunk的是否平整
	BuildPlaceParameter placeParam;
	placeParam.heightScore = 20;
	placeParam.limitHeightDiff = 5;
	placeParam.maxHeightDiff = 5;
	placeParam.waterScore = 500;
	BuildSampleResult errResult;
	if (!EcosysBuildHelp::getChunkErrScore(world->getWorldProxy(), curChunk, startPos.y, placeParam, errResult))
	{
		GEN_LOG_INFO("EcosysUnitCityBuild _addSingleBuildToWorld getChunkErrScore false buildName:%s", buildName.c_str());
		return false;
	}
	// if (!force && errResult.err >= 30)
	// {
	// 	GEN_LOG_INFO("EcosysUnitCityBuild _addSingleBuildToWorld errResult.err >= 30 buildName:%s", buildName.c_str());
	// 	return false;
	// }
	startPos.y = (errResult.averageHeight);
	//放建筑
	{
		GEN_LOG_INFO("EcosysUnitCityBuild _addSingleBuildToWorld onAddSingleBuild success buildName:%s", buildName.c_str());
		if (add2City)
		{
			world->getCityMgr()->onAddSingleBuild(pBuildData->m_fileName.c_str(), index.x, index.z, crx, crz, brx, brz);
		}

		WCoord buildBeginPos;
		ChunkIndex bottomLeft;
		ChunkIndex centerIndex;
		int chunkRange = 0;
		if (force)
		{
			buildBeginPos = EcosysBuildHelp::getChunkStartPos(index);
			buildBeginPos.y = startPos.y + 1;
			bottomLeft = index;
			chunkRange = 1;
		}
		else
		{
			//如果建筑大小小于16, 就直接放了
			if (pBuildData->m_size.x() < 16 && pBuildData->m_size.z() < 16)
			{
				buildBeginPos = EcosysBuildHelp::getChunkStartPos(index);
				buildBeginPos.y = startPos.y + 1;
				buildBeginPos.x += 7 - pBuildData->m_size.x() / 2;
				buildBeginPos.z += 7 - pBuildData->m_size.z() / 2;
				bottomLeft = index;
				centerIndex = index;
				chunkRange = 1;
			}
			//超过就要算一个合适的位置了
			else
			{
				bool check = false;
				std::vector<int> biomids;
				//这里的判断很模糊, 正方形判断.
				int crange = std::max((pBuildData->m_size.x() / CHUNK_BLOCK_X) + 1, (pBuildData->m_size.z() / CHUNK_BLOCK_X) + 1);
				bottomLeft = EcosysBuildHelp::checkBiomeUnLoadDir(index.x, index.z, crange, world->getWorldProxy(), check);
				if (!check)
				{
					return false;
				}
				std::vector<ChunkIndex> vector2;
				buildBeginPos = EcosysBuildHelp::getChunkStartPos(bottomLeft);
				buildBeginPos.y = startPos.y + 1;
				centerIndex = bottomLeft + ChunkIndex(crange / 2, crange / 2);
				chunkRange = crange;
			}
		}
		// if (!curChunk->m_specialBiome.empty())
		// {
		// 	int halfRange = chunkRange / 2;
		// 	for (auto& p : curChunk->m_specialBiome)
		// 	{
		// 		int halfPX = p.range.x() / 2;
		// 		int halfPZ = p.range.y() / 2;
		// 		if (SpecialBiome_City == p.type)
		// 		{
		// 			int disX = abs(p.leftDown.x + halfPX - bottomLeft.x - halfRange);
		// 			int disZ = abs(p.leftDown.z + halfPZ - bottomLeft.z - halfRange);
		// 			if (disX <= halfPX + halfRange && disZ <= halfPZ + halfRange)
		// 			{
		// 				GEN_LOG_INFO("EcosysUnitCityBuild _addSingleBuildToWorld SpecialBiome_City range check false buildName:%s", buildName.c_str());
		// 				return false;
		// 			}
		// 		}
		// 	}
		// }
		//EcosysBuildHelp::tryLoadRectEdgeTrunk(pworld, buildBeginPos, buildBeginPos + WCoord(125, 0, 107), 7, vector2);
		//unsigned int pptaskid = pworld->getWorld()->DoTaskAfterLoadedChunk(vector2, [&, pworld, buildBeginPos, index](unsigned int ppTaskId) {
		BuildData data;
		if (buildDir == -1 || !(buildDir >= 0 && buildDir < 4))
		{
			if (isValidDir(pBuildData->m_dir))
			{
				data.dir = pBuildData->m_dir;
			}
			else
			{
				data.dir = m_randGen.get(DIR_NEG_X, DIR_POS_Z);
			}
		}
		else
		{
			data.dir = buildDir;
		}
		//data.id_1 = "cityBuild";
		data.groundfloor = pBuildData->m_groundFloor;
		buildBeginPos.y += pBuildData->m_groundFloor;
		data.id_2 = BuildCreaterType_City;
		data.startPos = buildBeginPos;
		data.fileName = pBuildData->m_fileName;
		data.paramter = pBuildData->m_paramter;

		GEN_LOG_INFO("EcosysUnitCityBuild _addSingleBuildToWorld AddBuild buildName:%s startpos:%f, %f, %f", buildName.c_str(), buildBeginPos.x, buildBeginPos.y, buildBeginPos.z);
		world->getBuildMgr()->AddBuild(data, 1, "SingleBase");
		
		if (findBuildData(SpecialEcosysType_Custom, centerIndex, 0, world->getWorldProxy()) != NULL)
		{
			GEN_LOG_INFO("EcosysUnitCityBuild _addSingleBuildToWorld buildGenFinish buildName:%s centerIndex:%d, %d", buildName.c_str(), centerIndex.x, centerIndex.z);
			buildGenFinish(SpecialEcosysType_Custom, centerIndex, world->getWorldProxy());
		}
		else
		{
			GEN_LOG_INFO("EcosysUnitCityBuild _addSingleBuildToWorld addBuildData buildName:%s centerIndex:%d, %d", buildName.c_str(), centerIndex.x, centerIndex.z);
			addBuildData(SpecialEcosysType_Custom, centerIndex, ChunkIndex(pBuildData->m_range.x(), pBuildData->m_range.y()), world->getWorldProxy(), SpecialEcosysBuildStatus_END);
		}
		
	}
	return true;
}




bool EcosysUnitCityBuild::_addWaterBuildToWorld(WorldProxy* pworld, Chunk* curChunk, const ChunkIndex& index, bool force, int buildDir
)
{
	const auto& singleConfig = GetCityConfigInterface()->getSingleBuildConfig();
	if (singleConfig.m_watertBuilds.empty())
	{
		return false;
	}
	//先随出要放的建筑
	int buildIndex = 0;
	{
		int randWeight = singleConfig.m_waterTotalWeights <= 0 ? 0 : m_randGen.nextInt(singleConfig.m_waterTotalWeights);
		for (; buildIndex < singleConfig.m_watertBuilds.size(); buildIndex++)
		{
			randWeight -= singleConfig.m_watertBuilds[buildIndex].m_weight;
			if (randWeight <= 0)
			{
				break;
			}
		}
	}
	if (!CHECK_VECTOR_INDEX(buildIndex, singleConfig.m_watertBuilds))
	{
		assert(0);
		return false;
	}
	const CityBuildConfig::WaterBuildData* pBuildData = &(singleConfig.m_watertBuilds[buildIndex]);

	if (!force && !checkCanGen(SpecialEcosysType_Custom, index, ChunkIndex(pBuildData->m_range.x(), pBuildData->m_range.y()), pworld))
	{
		return false;
	}

	WCoord startPos = WCoord(index.x * 16, EcosysBuildHelp::getRealHeightChunk(curChunk, 7, 7), index.z * 16);
	//先看下单个chunk的是否平整
	BuildPlaceParameter placeParam;
	placeParam.heightScore = 20;
	placeParam.limitHeightDiff = 5;
	placeParam.maxHeightDiff = 5;
	BuildSampleResult errResult;
	if (!EcosysBuildHelp::getChunkErrScore(pworld, curChunk, startPos.y, placeParam, errResult))
	{
		return false;
	}
	if (!force && errResult.err >= 30)
	{
		return false;
	}
	startPos.y = errResult.averageHeight;

	//放建筑
	{
		WCoord buildBeginPos;
		ChunkIndex bottomLeft;
		ChunkIndex centerIndex;
		int chunkRange = 0;
		//如果建筑大小小于16, 就直接放了
		if (pBuildData->m_size.x() < 16 && pBuildData->m_size.z() < 16)
		{
			buildBeginPos = EcosysBuildHelp::getChunkStartPos(index);
			buildBeginPos.y = startPos.y + 1;
			buildBeginPos.x += 7 - pBuildData->m_size.x() / 2;
			buildBeginPos.z += 7 - pBuildData->m_size.z() / 2;
			bottomLeft = index;
			centerIndex = index;
			chunkRange = 1;
		}
		//超过就要算一个合适的位置了
		else
		{
			bool check = false;
			std::vector<int> biomids;
			//这里的判断很模糊, 正方形判断.
			int crange = std::max((pBuildData->m_size.x() / CHUNK_BLOCK_X) + 1, (pBuildData->m_size.z() / CHUNK_BLOCK_X) + 1);
			bottomLeft = EcosysBuildHelp::checkBiomeUnLoadDir(index.x, index.z, crange, pworld, check);
			if (!check)
			{
				return false;
			}
			std::vector<ChunkIndex> vector2;
			buildBeginPos = EcosysBuildHelp::getChunkStartPos(bottomLeft);
			buildBeginPos.y = startPos.y + 1;
			chunkRange = crange;
			centerIndex = bottomLeft + ChunkIndex(crange / 2, crange / 2);
		}
		buildBeginPos.y -= pBuildData->m_downHeight;
		if (!curChunk->m_specialBiome.empty())
		{
			int halfRange = chunkRange / 2;
			for (auto& p : curChunk->m_specialBiome)
			{
				int halfPX = p.range.x() / 2;
				int halfPZ = p.range.y() / 2;
				if (SpecialBiome_City == p.type)
				{
					int disX = abs(p.leftDown.x + halfPX - bottomLeft.x - halfRange);
					int disZ = abs(p.leftDown.z + halfPZ - bottomLeft.z - halfRange);
					if (disX <= halfPX + halfRange && disZ <= halfPZ + halfRange)
					{
						return false;
					}
				}
			}
		}
		//EcosysBuildHelp::tryLoadRectEdgeTrunk(pworld, buildBeginPos, buildBeginPos + WCoord(125, 0, 107), 7, vector2);
		//unsigned int pptaskid = pworld->getWorld()->DoTaskAfterLoadedChunk(vector2, [&, pworld, buildBeginPos, index](unsigned int ppTaskId) {
		BuildData data;
		if (isValidDir(pBuildData->m_dir))
		{
			data.dir = pBuildData->m_dir;
		}
		else
		{
			data.dir = m_randGen.get(DIR_NEG_X, DIR_POS_Z);
		}
		//data.id_1 = "cityBuild";
		data.id_2 = BuildCreaterType_City;
		data.startPos = buildBeginPos;
		data.fileName = pBuildData->m_fileName;
		data.paramter = pBuildData->m_paramter;

		pworld->getWorld()->getBuildMgr()->AddBuild(data, buildIndex, "Warter");
		
		if (findBuildData(SpecialEcosysType_Custom, centerIndex, 0, pworld) != NULL)
		{
			buildGenFinish(SpecialEcosysType_Custom, centerIndex, pworld);
		}
		else
		{
			addBuildData(SpecialEcosysType_Custom, index, ChunkIndex(pBuildData->m_range.x(), pBuildData->m_range.y()), pworld, SpecialEcosysBuildStatus_END);
		}
		//上报, 对周围玩家上报
		if (pBuildData->m_reportId > 0)
		{
			auto centerPos = buildBeginPos;
			if (pworld->getWorld() && pworld->getWorld()->getActorMgr())
			{
				//const auto& players = pworld->getWorld()->getActorMgr()->getAllPlayer();
				for (int i = 0; i < pworld->getWorld()->getActorMgr()->getNumPlayer(); i++)
				{
					IClientPlayer* player = pworld->getWorld()->getActorMgr()->iGetIthPlayer(i);//pList[i];
					if (!player)
					{
						continue;
					}
					if (player->iGetCurMapID() == pworld->getCurMapID())
					{
						auto curDis = centerPos - CoordDivBlock(player->iGetPosition());
						if (curDis.x * curDis.x + curDis.z * curDis.z <= (921600))
						{
							ObserverEvent obevent;
							obevent.SetData_CustomDefault(pBuildData->m_reportId);
							obevent.SetData_Actor(player->getUin());
							GetObserverEventManager().OnTriggerEvent("Build.Report", &obevent);
						}

					}
				}
			}
		}
		//});
		/*addTask(pptaskid);
		if (!isLegalTaskId(pTaskId) && isLegalTaskId(pptaskid))
		{
			addBuildData(SpecialEcosysType_CitySingleBuild, index, pworld);
		}*/
		//});
		//addTask(taskid);
		//addBuildData(SpecialEcosysType_IceAltar, index, pworld);
		//if (isLegalTaskId(taskid))
		//{
			//改变状态
			//addBuildData(SpecialEcosysType_Custom, index, ChunkIndex(16, 16), pworld);
		//}
	}
	return true;
}

bool EcosysUnitCityBuild::addSingleBuildToWorld(World* world, ChunkIndex index, const std::string& buildName, bool force, bool add2City, int buildDir)
{
	if (world == nullptr)
	{
		GEN_LOG_INFO("EcosysUnitCityBuild addSingleBuildToWorld world is nullptr");
		return false;
	}
	world->syncLoadChunk(index, GetIPlayerControl() ? GetIPlayerControl()->GetChunkViewerForCtrl() : nullptr);
	Chunk* curChunk = world->getChunk(index);

	if (curChunk == nullptr)
	{
		GEN_LOG_INFO("EcosysUnitCityBuild addSingleBuildToWorld curChunk is nullptr index:%d %d", index.x, index.z);
		assert(0);
		return false;
	}

	return _addSingleBuildToWorld(world, curChunk, index, buildName, force, add2City, buildDir);
}

bool EcosysUnitCityBuild::addSpecialBuildToWorld(WorldProxy* pworld, const WCoord& spawnPos, const std::vector<ChunkSpecialBiomeData>& cityDatas, WCoord& retPos)
{
	BuildAutoClose buildCon;
	if (!GetCityConfigInterface())
	{
		return false;
	}
	if (!pworld->getWorld()) return false;
	//const auto& singleConfig = CityConfig::getSingletonPtr()->getSingleBuildConfig();
	////先随出要放的建筑
	//int buildIndex = 0;
	//{
	//	m_randGen.setSeed(spawnPos.x + spawnPos.z);
	//	std::vector<int> builds;
	//	for (; buildIndex < singleConfig.m_builds.size(); buildIndex++)
	//	{
	//		if (singleConfig.m_builds[buildIndex].m_reportId == 26)
	//		{
	//			builds.push_back(buildIndex);
	//		}
	//	}
	//	if (builds.empty()) return false;
	//	buildIndex = builds[m_randGen.get(0, builds.size())];
	//}
	const CityBuildConfig::SingleBuildData* pBuildData = GetCityConfigInterface()->GetSingleBuildData();
	if (!pBuildData) return false;

	Rainbow::Vector3i buildSize = pBuildData->m_size;
	int cx = BlockDivSection(spawnPos.x);
	int cz = BlockDivSection(spawnPos.z);
	int buildChunkSize = std::max((buildSize.x() / CHUNK_SECTION_DIM) + 1, (buildSize.z() / CHUNK_SECTION_DIM) + 1);
	int halfBuildSize = buildChunkSize / 2;
	int baseRange = GetCityConfigInterface()->GetBossBuildRange();
	auto isInCityRange = [&](const std::vector<ChunkSpecialBiomeData>& datas, ChunkIndex pos) ->bool
	{
		ChunkIndex buildCenterIndex(pos.x + halfBuildSize, pos.z + halfBuildSize);
		for (const auto& p : datas)
		{
			ChunkIndex leftDowm((p.leftDown.x - 1), (p.leftDown.z - 1));
			int halfPX = p.range.x() / 2 + 1;
			int halfPZ = p.range.y() / 2 + 1;
			int disX = abs(p.leftDown.x + halfPX - buildCenterIndex.x);
			int disZ = abs(p.leftDown.z + halfPZ - buildCenterIndex.z);
			if (disX <= halfPX + halfBuildSize && disZ <= halfPZ + halfBuildSize)
			{
				return true;
			}
		}
		//再看是不是在出生点范围内
		{
			int disX = abs(cx - buildCenterIndex.x);
			int disZ = abs(cz - buildCenterIndex.z);
			if (disX <= baseRange + halfBuildSize && disZ <= baseRange + halfBuildSize)
			{
				return true;
			}
		}
		return false;
	};
	bool bfind = false;
	int step = 10;
	int curRange = step;
	for (int test = 2; test >= 0 && !bfind; test--)
	{
		curRange += step;
		int bottomX = cx - curRange;
		int bottomZ = cz - curRange;
		int topX = cx + curRange;
		int topZ = cz + curRange;
		int inner_bottomX = bottomX + step;
		int inner_bottomZ = bottomZ + step;
		int inner_topX = topX - step;
		int inner_topZ = topZ - step;;
		for (int x = bottomX; x <= topX && !bfind; x += 4)
		{
			for (int z = bottomZ; z <= topZ && !bfind; z += 4)
			{
				if (x >= inner_bottomX && x <= inner_topX && z >= inner_bottomZ && z <= inner_topZ)
				{
					continue;
				}
				if (!isInCityRange(cityDatas, ChunkIndex(x, z)))
				{
					//这个区块加载
					pworld->getWorld()->syncLoadChunk(x, z);
					Chunk* chunk = pworld->getWorld()->getChunkBySCoord(x, z);
					//先看下单个chunk的是否平整
					BuildPlaceParameter placeParam;
					placeParam.heightScore = 20;
					placeParam.limitHeightDiff = 40;
					placeParam.maxHeightDiff = 40;
					placeParam.waterScore = 10;
					BuildSampleResult errResult;
					if (!EcosysBuildHelp::getChunkErrScore(pworld, chunk, EcosysBuildHelp::getRealHeightChunk(chunk, 7, 7), placeParam, errResult))
					{
						continue;
					}
					if (errResult.isWater) continue;
					int height = errResult.averageHeight + 0.5f;

					ChunkIndex buildBottomIndex(x, z);
					ChunkIndex buildCenterIndex(x + halfBuildSize, z + halfBuildSize);
					BuildData data;
					if (isValidDir(pBuildData->m_dir))
					{
						data.dir = pBuildData->m_dir;
					}
					else
					{
						data.dir = m_randGen.get(DIR_NEG_X, DIR_POS_Z);
					}
					//data.id_1 = "cityBuild";
					data.id_2 = BuildCreaterType_City;
					data.startPos = EcosysBuildHelp::getChunkStartPos(buildBottomIndex);
					data.startPos.y = height;
					data.fileName = pBuildData->m_fileName;
					data.paramter = pBuildData->m_paramter;
					pworld->getWorld()->getBuildMgr()->AddBuild(data, 0, "Other");
					
					retPos = EcosysBuildHelp::getChunkStartPos(buildCenterIndex);
					//addSpecialBuildMark(pworld->getWorld(), centerPos.x, centerPos.z);
					if (findBuildData(SpecialEcosysType_Custom, buildCenterIndex, 0, pworld) != NULL)
					{
						buildGenFinish(SpecialEcosysType_Custom, buildCenterIndex, pworld);
					}
					else
					{
						addBuildData(SpecialEcosysType_Custom, buildCenterIndex, ChunkIndex(pBuildData->m_range.x(), pBuildData->m_range.y()), pworld, SpecialEcosysBuildStatus_END);
					}

					bfind = true;
				}
			}
		}
	}
	return bfind;
}

void EcosysUnitCityBuild::addSpecialBuildMark(World* world, int x, int z)
{
	//是本机直接处理, 给自己发个
	if (GetClientInfoProxy()->getRoomHostType() != ROOM_SERVER_RENT)
	{
		//发事件
		MINIW::ScriptVM::game()->callFunction("PlayerMiniMapAddMarkData", "ii", x, z);
	}
	//再给其他客机
	{
		jsonxx::Object ret;
		ret << "x" << x;
		ret << "z" << z;
		int size = 0;
		unsigned char* p = NULL;
		ret.saveBinary(p, size);
		SandBoxManager::getSingletonPtr()->sendBroadCast((char*)("SPECIAL_BUILD_MARK"), p, size);
		free(p);
	}
}

void EcosysUnitCityBuild::ReplaceCity(World* world)
{
	// 遍历singleBuilds
	auto singleBuilds = world->getCityMgr()->GetAllSingleBuildData();
	for (auto& pair : singleBuilds)
	{
		auto config = GetCityConfigInterface()->getSingleBuildDataByName(pair.first);
		if (!config) continue;

		// 遍历chunk
		auto& buildDatas = pair.second;
		for (auto& build : buildDatas.buildData)
		{
			addSingleBuildToWorld(world, ChunkIndex(build.x, build.z), config->m_fileName.c_str(), true, false);
		}
	}
}


IMPLEMENT_GETMETHOD_MANUAL_INIT(EcosysUnitCityBuild)