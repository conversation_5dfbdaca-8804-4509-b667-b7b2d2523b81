﻿
#ifndef __LUA_INTERFACE_PROXY_H__
#define __LUA_INTERFACE_PROXY_H__ 1

#include "Utilities/Singleton.h"
#include "ScriptVM/OgreScriptLuaVM.h"

//#include "GameEvent.h"
//#include "OgreFileSystem.h"
#include "Global.h"
#include "ConstAtLua.h"
#include "Common/SingletonDefinition.h"
#include "SandboxEngine.h"

#include "ObserverEvent.h"

class EXPORT_SANDBOXENGINE LuaInterfaceProxy;
class LuaInterfaceProxy //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	LuaInterfaceProxy();
	~LuaInterfaceProxy(void);
    //{{{
    int os_mkdir(const char* path);
    // io 操作 {{{
    void* io_open(const char* path, bool mode);
    void io_write(void* fp, const std::string str);
    std::string io_read(void* fp);
    std::string io_line(void* fp);
    void io_close(void*fp);
    bool io_dir_exsit(const char* path);

    bool dofile(const char* path);
    bool loadpackage(const char* path);

	const char* getStdioRoot();

    //void setRentLuaLogFlag(int flag);
	
	void log(const char* msg);
	void androidLogcat(const char* text);

  	//位操作
	int bnot(int a);
    int band(int a, int b);
    int bor(int a, int b);
	int bxor(int a, int b);
    int lshift(int a, int left);
    int rshift(int a, int right);

    bool isdebug();

	bool is_valid_nickname(const std::string& nickname, int min_length, int max_length);

	void SaveToFile(std::string filePath, std::string content);
	std::string ReadFromFile(std::string filePath);

    unsigned int random(unsigned int min, unsigned int max);
    void randomseed(unsigned int seed);

	unsigned int string2Int(const char *k);

	std::string hash_sha512(const char* str);


    ConstAtLua* get_lua_const();

	SocConstAtLua* get_soc_lua_const();

    std::string aes_encryption(std::string key, std::string iv, std::string pInBuf);
    std::string aes_decryption(std::string key, std::string iv, std::string pInBuf);
    //}}}

    void decrypt(void* block, unsigned int len);


    void callLuaStringWithCallback(const char *funcName, const char *sessionId, const char *params);
	void responseLuaWithCallback(const char *funcName, const char *sessionId, const char *responseJson);

	void callLuaString(const char*);
	void showGameTips(const int&);
	void showGameTips(const char*);
	void showGameTips(std::string);
	/*
	v2：添加参数withoutFilter，是否屏蔽关键字  默认屏蔽
	*/
	void showGameTips(int stringId, int type, bool withoutFilter = false);
	
	// C++中 trigger事件, 仅支持简单数据结构. 不再使用json传，改为使用userdata
	int event_trigger(const char* funcname, const char* msgid, const ObserverEvent* obevent);
	void toHex(char* hexbuf, const unsigned char* content, int len);
	bool shouldUseNewHpRule();
	//tolua_end
	void setRentLuaLogFlag(int flag);


	// C++中 trigger事件, 仅支持简单数据结构. 不再使用json传，改为使用userdata （合并处理）
	void event_trigger_begin();
	void event_trigger_add(const char* msgid, ObserverEvent* obevent);
	void event_trigger_end(const char* funcname);
public:
	MINIW::ScriptVM* m_VM;

	unsigned int    m_seed;
	std::string     m_filecontent;
	// for debug {{{
	int m_FP;
	char m_buffer[1024];
	// for debug  }}} 
	//config{{{
	ConstAtLua m_const;
	//}}}
	SocConstAtLua m_soc_const;

	int  m_rent_lua_log_flag;   //租赁服是否打印lua日志 0=关闭 1=打开
private:
	ObserverEvents m_events;



}; //tolua_exports


//DECLARE_GETMETHOD(LuaInterfaceProxy)
EXPORT_SANDBOXENGINE LuaInterfaceProxy& GetLuaInterfaceProxy();
EXPORT_SANDBOXENGINE LuaInterfaceProxy* GetLuaInterfaceProxyPtr();

#endif//__LUA_INTERFACE_PROXY_H__