# AIFlyPointToPoint 使用说明

## 组件介绍

`AIFlyPointToPoint`是一个智能飞行AI组件，用于让飞行生物从一个指定位置飞行到另一个指定位置。

## 特性

✅ **直线飞行** - 基于seek算法的直线移动  
✅ **自动升降** - 智能计算飞行高度，避免撞地  
✅ **自动避障** - 检测前方障碍物并自动绕行  
✅ **卡死检测** - 检测并处理卡住情况  
✅ **路径验证** - 验证路径可通行性  

## 构造参数

```cpp
AIFlyPointToPoint(ClientMob *pActor, 
                  const WCoord& startPos,     // 起始位置
                  const WCoord& endPos,       // 目标位置  
                  float speed = 1.0f,         // 飞行速度倍率
                  int avoidanceRange = 3,     // 避障检测范围(方块)
                  float avoidanceHeight = 50.0f); // 避障高度
```

## 使用示例

### 基本使用
```cpp
// 创建飞行AI，从当前位置飞到目标位置
WCoord startPos = mob->getPosition();
WCoord targetPos = WCoord(1000, 150, 2000);

AIFlyPointToPoint* flyAI = new AIFlyPointToPoint(mob, startPos, targetPos);
mob->addAI(flyAI);
```

### 自定义参数
```cpp
// 高速飞行，大范围避障
WCoord startPos = WCoord(0, 100, 0);
WCoord targetPos = WCoord(2000, 200, 3000);

AIFlyPointToPoint* flyAI = new AIFlyPointToPoint(
    mob,
    startPos,
    targetPos,
    2.0f,  // 2倍速度
    5,     // 5个方块避障范围
    100.0f // 100单位避障高度
);
mob->addAI(flyAI);
```

### 动态更改目标
```cpp
// 运行时更改目标位置
WCoord newTarget = WCoord(5000, 300, 1000);
flyAI->setTargetPos(newTarget);

// 检查是否到达目标
if (flyAI->hasReachedTarget()) {
    LOG_INFO("飞行生物已到达目标位置");
}
```

## 避障机制

### 障碍物检测
- 检测前方3-5个方块范围内的实体方块
- 排除空气、水、岩浆等可穿过方块
- 检测飞行路径上的垂直障碍

### 避障策略
1. **向上绕行** - 优先尝试提升高度越过障碍
2. **左右绕行** - 如果上方受阻，尝试水平绕行  
3. **强制上升** - 最后手段，直接大幅上升

### 智能高度计算
- 自动检测目标区域地面高度
- 在地面上方保持安全飞行高度(150单位)
- 确保不低于起始高度

## 适用场景

✅ **任务引导** - NPC飞行到指定地点  
✅ **物流运输** - 货物运输飞行器  
✅ **巡逻路线** - 按固定路线巡逻  
✅ **追踪目标** - 动态追踪移动目标  

## 注意事项

1. **仅适用于飞行生物** - 需要FlyLoc移动类型
2. **开放环境** - 在密集建筑区可能频繁避障
3. **性能考虑** - 避障检测有计算开销
4. **到达判定** - 距离目标100单位内认为已到达

## 与其他AI组件对比

| 特性 | AIFlyPointToPoint | AIRandomFly | AIFlyLoveBlock |
|------|------------------|-------------|----------------|
| 目标设定 | ✅ 精确坐标 | ❌ 随机生成 | ❌ 搜索方块 |
| 避障功能 | ✅ 智能避障 | ❌ 无避障 | ❌ 无避障 |
| 路径规划 | ✅ 智能规划 | ❌ 简单直线 | ❌ 简单直线 |
| 适用场景 | 🎯 精确导航 | 🔄 自由漫游 | 🎯 物品吸引 |