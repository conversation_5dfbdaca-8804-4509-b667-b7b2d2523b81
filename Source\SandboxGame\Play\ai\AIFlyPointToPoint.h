#pragma once

#include <OgreWCoord.h>
#include "AIBase.h"

class LivingLocoMotion;

class AIFlyPointToPoint : public AIBase //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	AIFlyPointToPoint(ClientMob *pActor, const WCoord& startPos, const WCoord& endPos, 
					  float speed = 1.0f, int avoidanceRange = 3, float avoidanceHeight = 50.0f);
	~AIFlyPointToPoint();
	
	virtual bool willRun();
	virtual bool continueRun();
	virtual void start();
	virtual void reset();
	virtual void update();
	virtual AI_MOTION_TYPE getMotionType() { return FKY_BLOCK_ATTRACTED; }
	
	// 设置新的目标点
	void setTargetPos(const WCoord& newTarget);
	// 检查是否到达目标
	bool hasReachedTarget() const;
	//tolua_end

private:
	// 避障检测
	bool detectObstacle(const WCoord& direction, int range);
	// 计算避障路径
	WCoord calculateAvoidancePath();
	// 计算智能高度
	WCoord calculateSmartHeight(const WCoord& targetPos);
	// 检查路径是否清晰
	bool isPathClear(const WCoord& from, const WCoord& to);

private:
	LivingLocoMotion* m_pLivingLocomotion;
	
	// 路径点
	WCoord m_StartPos;
	WCoord m_EndPos;
	WCoord m_CurrentTarget;
	
	// 移动参数
	float m_Speed;
	int m_AvoidanceRange;    // 避障检测范围（方块）
	float m_AvoidanceHeight;  // 避障时的额外高度
	
	// 状态管理
	bool m_HasReachedTarget;
	int m_StuckCounter;       // 卡住计数器
	WCoord m_LastPosition;    // 上一帧位置
	int m_UpdateCounter;      // 更新计数器
	
	// 避障状态
	bool m_IsAvoiding;        // 是否正在避障
	WCoord m_AvoidanceTarget; // 避障目标点
	int m_AvoidanceTicks;     // 避障持续时间
}; //tolua_exports