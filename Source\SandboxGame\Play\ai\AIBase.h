
#ifndef __AIBASE_H__
#define __AIBASE_H__
#include "actors/ActorManager.h"
#include "SandboxGame.h"

class ClientMob;
class ClientPlayer;
//tolua_begin
enum AI_MOTION_TYPE{
	IDLE, //空闲
	LOOKIDLE, //闲看
	WANDER, //闲逛
	ATK_MELEE, //近战攻击
	ATK_REMOTE, // 远程攻击
	FOLLOW_OWNER, // 跟随主人
	FOLLOW_PARENT, // 跟随父母
	SWIMMING, // 游泳
	INJURED_PANIC, // 受伤逃跑
	BUFF_PANIC, //buff触发的逃跑
	PLAYER_PANIC, //被玩家吓跑
	FISH_PANIC, //鱼类逃跑
	SEPARATE_PANIC, //萌眼星人合体分离逃跑
	SELF_BOMB, // 爆炸攻击
	FKY_ITEM_ATTRACTED, //飞行生物被道具吸引
	FKY_BLOCK_ATTRACTED, //飞行生物被方块吸引
	BE_ATTRACTED, // 被吸引
	COPULATION, // 交配
	THIEF, //扒手
	BANANAFAN,	//香蕉爱好者
	FLY_CHANGE_BLOCK,	// 飞行生物寻找方块，改变方块
	FLY_STAY_BLOCK,		// 飞行生物寻找方块，概率停留在方块上方
	PREY_ON, //捕食
	FIERCE, //龇牙
};
//tolua_end
class EXPORT_SANDBOXGAME AIBase;
class AIBase //tolua_exports
{ //tolua_exports
public:
	AIBase() : m_MutexBits(0)/*, m_pTargetActor(0)*/
	{
		m_TickCount = 0;
		m_TickNum = 0;
		m_isRunning = false;
		m_bPriority = 0;
	}
	explicit AIBase(ClientMob* pActor) : m_MutexBits(0), m_pMobActor(pActor)
	{
		m_TickCount=0;
		m_TickNum=0;
		m_isRunning=false;
		m_bPriority=0;
	}
	//tolua_begin
	virtual ~AIBase() ;

	virtual bool willRun() = 0;
	virtual bool continueRun() {return willRun();}
	virtual bool isInterruptible() {return true;}
	virtual void start() {}
	virtual void reset() {}
	virtual void update() {}
	void setMutexBits(int iMutex) {m_MutexBits = iMutex;}
	int getMutexBits() {return m_MutexBits;}

	virtual bool canInterruptInteract() { return false; }
	virtual bool canInterruptedByInteract() { return true; }
	virtual AI_MOTION_TYPE getMotionType() { return IDLE; }
	//tolua_end
	virtual void OnCollideWithPlayer(ClientPlayer* player) {};
protected:
	ClientMob *m_pMobActor = nullptr;
	//void setTarget(ClientActor *ptarget);
	//ClientActor *m_pTargetActor;

public:
	//tolua_begin
	//��������AILua����
	int m_TickCount;
	int m_TickNum;
	bool m_isRunning;
	int m_bPriority;
	//
	//tolua_end
private:
	int m_MutexBits;
}; //tolua_exports

class AIBossBase //tolua_exports
{ //tolua_exports
public:
	AIBossBase()
	{
	}
	//tolua_begin
	virtual void Release() = 0;
	virtual ~AIBossBase(){} ;
	virtual void start() {}
	//tolua_end
protected:
}; //tolua_exports

//tolua_begin
bool baseAtkDist(ClientActor *pActor, ClientMob * pMob);
bool baseIsSuitableTarget(ClientActor *pActor, ClientMob * pMob, bool checkSight = false);
//tolua_end
#endif