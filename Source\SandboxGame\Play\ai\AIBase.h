
#ifndef __AIBASE_H__
#define __AIBASE_H__
#include "actors/ActorManager.h"
#include "SandboxGame.h"

class ClientMob;
class ClientPlayer;
//tolua_begin
enum AI_MOTION_TYPE{
	IDLE, //����
	LOOKIDLE, //�п�
	WANDER, //�й�
	ATK_MELEE, //��ս����
	ATK_REMOTE, // Զ�̹���
	FOLLOW_OWNER, // ��������
	FOLLOW_PARENT, // ���游ĸ
	SWIMMING, // ��Ӿ
	INJURED_PANIC, // ��������
	BUFF_PANIC, //buff����������
	PLAYER_PANIC, //���������
	FISH_PANIC, //��������
	SEPARATE_PANIC, //�������˺����������
	SELF_BOMB, // ��ը����
	FKY_ITEM_ATTRACTED, //�������ﱻ��������
	FKY_BLOCK_ATTRACTED, //�������ﱻ��������
	BE_ATTRACTED, // ������
	COPULATION, // ����
	THIEF, //����
	BANANAFAN,	//�㽶������
	FLY_CHANGE_BLOCK,	// ��������Ѱ�ҷ��飬�ı䷽��
	FLY_STAY_BLOCK,		// ��������Ѱ�ҷ��飬����ͣ���ڷ����Ϸ�
	PREY_ON, //��ʳ
	FIERCE, //����
};
//tolua_end
class EXPORT_SANDBOXGAME AIBase;
class AIBase //tolua_exports
{ //tolua_exports
public:
	AIBase() : m_MutexBits(0)/*, m_pTargetActor(0)*/
	{
		m_TickCount = 0;
		m_TickNum = 0;
		m_isRunning = false;
		m_bPriority = 0;
	}
	explicit AIBase(ClientMob* pActor) : m_MutexBits(0), m_pMobActor(pActor)
	{
		m_TickCount=0;
		m_TickNum=0;
		m_isRunning=false;
		m_bPriority=0;
	}
	//tolua_begin
	virtual ~AIBase() ;

	virtual bool willRun() = 0;
	virtual bool continueRun() {return willRun();}
	virtual bool isInterruptible() {return true;}
	virtual void start() {}
	virtual void reset() {}
	virtual void update() {}
	void setMutexBits(int iMutex) {m_MutexBits = iMutex;}
	int getMutexBits() {return m_MutexBits;}

	virtual bool canInterruptInteract() { return false; }
	virtual bool canInterruptedByInteract() { return true; }
	virtual AI_MOTION_TYPE getMotionType() { return IDLE; }
	//tolua_end
	virtual void OnCollideWithPlayer(ClientPlayer* player) {};
protected:
	ClientMob *m_pMobActor = nullptr;
	//void setTarget(ClientActor *ptarget);
	//ClientActor *m_pTargetActor;

public:
	//tolua_begin
	//��������AILua����
	int m_TickCount;
	int m_TickNum;
	bool m_isRunning;
	int m_bPriority;
	//
	//tolua_end
private:
	int m_MutexBits;
}; //tolua_exports

class AIBossBase //tolua_exports
{ //tolua_exports
public:
	AIBossBase()
	{
	}
	//tolua_begin
	virtual void Release() = 0;
	virtual ~AIBossBase(){} ;
	virtual void start() {}
	//tolua_end
protected:
}; //tolua_exports

//tolua_begin
bool baseAtkDist(ClientActor *pActor, ClientMob * pMob);
bool baseIsSuitableTarget(ClientActor *pActor, ClientMob * pMob, bool checkSight = false);
//tolua_end
#endif