
#include "AIFlyAttack.h"
#include "ClientMob.h"
#include "ActorVision.h"
#include "LivingLocoMotion.h"
#include "ToAttackTargetComponent.h"
#include "LivingAttrib.h"

using namespace MNSandbox;


AIFlyAttack::AIFlyAttack(ClientMob *pActor, bool trace, float speed, int buffid, int bufflevel) :AIBase(pActor)
, m_Trace(trace), m_TraceTimer(0), m_SpeedMulty(speed), m_AttackTick(0)
, m_nBuffid(buffid), m_nBufflevel(bufflevel)
{
	setMutexBits(3);
	m_pLivingLocomotion = dynamic_cast<LivingLocoMotion *>(m_pMobActor->getLocoMotion());
	assert(m_pLivingLocomotion != NULL);
}

AIFlyAttack::~AIFlyAttack()
{
}

//触发条件：攻击目标不为空时
bool AIFlyAttack::willRun()
{
	ClientActor *target = nullptr;
	auto targetComponent = m_pMobActor->getToAttackTargetComponent();
	if (targetComponent)
	{
		target = targetComponent->getTarget();
	}
	if (NULL == target || target->isDead() || target->isInWater())
	{
		return false;
	}
	else
	{
		if (m_pMobActor->getVision()->canSee(target))
			return true;
		else
			return false;
	}
	
	return false;
}

bool AIFlyAttack::continueRun()
{
	if (m_pMobActor->isInWater())
	{
		return false;
	}

	ClientActor *target = nullptr;
	auto targetComponent = m_pMobActor->getToAttackTargetComponent();
	if (targetComponent)
	{
		target = targetComponent->getTarget();
	}
	if (NULL == target || target->isDead() || target->isInWater())
	{
		return false;
	}
	else
	{
		if (m_pMobActor->getVision()->canSee(target))
			return true;

		return false;
	}

	return false;
}

void AIFlyAttack::start()
{
	m_TraceTimer = 0;
	if(m_pLivingLocomotion == NULL) return; 
	m_pLivingLocomotion->m_SpeedMultiple = m_SpeedMulty;

	m_pLivingLocomotion->m_HasTarget = true;
	if(m_pMobActor == NULL) return;
	ClientActor *target = nullptr;
	auto targetComponent = m_pMobActor->getToAttackTargetComponent();
	if (targetComponent)
	{
		target = targetComponent->getTarget();
	}
	if(target == NULL)  return;
	m_pLivingLocomotion->m_MoveTarget = target->getPosition() + 100 * Rainbow::Vector3f::yAxis;
	m_pLivingLocomotion->setBehaviorOn(BehaviorType::Pursuit);
}

void AIFlyAttack::reset()
{
	m_TraceTimer = 0;
	if(m_pLivingLocomotion){
		m_pLivingLocomotion->m_SpeedMultiple = 1.0f;
		m_pLivingLocomotion->setBehaviorOff(BehaviorType::Pursuit);
	}
}

bool AIFlyAttack::atkDist(ClientActor *pActor)
{
	double dist = static_cast<ClientMob *>(m_pMobActor)->getDef()->AttackDistance*BLOCK_SIZE;
	dist = dist*dist;

	WCoord targetpos = pActor->getLocoMotion()->getPosition();
	if (m_pMobActor->getSquareDistToPos(targetpos.x, targetpos.y, targetpos.z) <= dist)
	{
		return true;
	}
	else
	{
		return false;
	}
}

void AIFlyAttack::update()
{
	if(m_pMobActor == NULL) return;
	ClientActor *target = nullptr;
	auto targetComponent = m_pMobActor->getToAttackTargetComponent();
	if (targetComponent)
	{
		target = targetComponent->getTarget();
	}
	if (NULL == target)  return;

	ActorVision *vision = m_pMobActor->getVision();

	m_AttackTick--;
	if (0 > m_AttackTick) m_AttackTick = 0;

	if (m_AttackTick <= 0)
	{
		if (vision && atkDist(target) && vision->canSee(target))
		{
			m_AttackTick = 30;
			if (m_pMobActor->getDef() && m_pMobActor->getDef()->AttackType == ATTACK_PUNCH)
			{
				m_pMobActor->attackActor(target);
				ActorLiving *live = dynamic_cast<ActorLiving *>(target);
				if(m_nBuffid && live)
				{
					live->getLivingAttrib()->addBuff(m_nBuffid, m_nBufflevel);
				}
			}
		}
	}
	if(m_pLivingLocomotion)
		m_pLivingLocomotion->m_MoveTarget = target->getPosition() + 100 * Rainbow::Vector3f::yAxis;
}