/*
** Lua binding: AITask
** Generated automatically by tolua++-1.0.92 on 10/26/21 10:16:04.
*/

#ifndef __cplusplus
#include "stdlib.h"
#endif
#include "string.h"

#include "Minitolua.h"

/* Exported function */
TOLUA_API int  tolua_AITask_open_manual(lua_State* tolua_S);
#include "AIBase.h"
#include "AIPickupItemEx.h"
#include "AIRandomSwim.h"
#include "AISwimming.h"
#include "AIFishAttack.h"
#include "AITargetHurtee.h"
#include "AIFishBeg.h"
#include "AISit.h"
#include "AIFollowOwner.h"
#include "AIWander.h"
#include "AIPetWander.h"
#include "AITargetOwnerHurtee.h"
#include "AITargetOwnerHurter.h"
#include "AITargetNonTamed.h"
#include "AIBeg.h"
#include "AIWatchClosest.h"
#include "AILookIdle.h"
#include "AIRestrictSun.h"
#include "AIFleeSun.h"
#include "AITargetNearest.h"
#include "AIBreakDoor.h"
#include "AIMoveTowardsRestriction.h"
#include "AIPanic.h"
#include "AITempt.h"
#include "AILeapAtTarget.h"
#include "AIMate.h"
#include "AIFollowParent.h"
#include "AIBoom.h"
#include "AIArrowAttack.h"
#include "AIEatGrass.h"
#include "AIFearPlayer.h"
#include "AIFollowDirection.h"
#include "AITargetSpecificEntity.h"
#include "AITransfiguration.h"
#include "AIProjectileAttack.h"
#include "AIClosestDance.h"
#include "AISleep.h"
#include "AILoggerHeads.h"
#include "AILayEggs.h"
#include "AILayEggInNest.h"
#include "AIHatch.h"
#include "AIEatFeedBlock.h"
#include "AIToppleOver.h"
#include "AISitbyItem.h"
#include "AIMilking.h"
#include "AIEatLeaf.h"
#include "AIEatFlower.h"
#include "AIRideHorse.h"
#include "AIKickAway.h"
#include "AIMakeTrouble.h"
#include "AIGetSpecialAttackattr.h"
#include "AISitBlock.h"
#include "AILoveBlock.h"
#include "AITargetFollowingPlayer.h"
#include "AIHoldMonster.h"
#include "AICeilingAtk.h"
#include "AIGoCeiling.h"
#include "AIPetDanceToPlayer.h"
#include "AISpecialAct.h"
#include "AIHungry.h"
#include "AIRandomFly.h"
#include "AIFlyAttack.h"
#include "AIFlyBeg.h"
#include "AIFlyLoveBlock.h"
#include "AIFlyPointToPoint.h"
#include "AIFearItem.h"
#include "AIDissolvedByItem.h"
#include "AIAttractBlock.h"
#include "AISavageSleep.h"
#include "AIWarning.h"
#include "AIItemPanic.h"
#include "AIPlayerPanic.h"
#include "AISeparate.h"
#include "AISeparatePanic.h"
#include "AICombine.h"
#include "AIWizardFly.h"
#include "AIWizardAttack.h"
#include "AIWizardProjectileAttack.h"
#include "AIIceWizardProjectileAttack.h"
#include "AIIceWizardFindActor.h"
#include "AIBumpAttack.h"
#include "AIDigBlock.h"
#include "AIPickupItem.h"
#include "AIStoreItem.h"
#include "AITakeItem.h"
#include "AICraftItem.h"
#include "AINpcSleep.h"
#include "AIHunger.h"
#include "AIEatFood.h"
#include "AIEatThenMutate.h"
#include "AIMutateFly.h"
#include "AIPatrolOnBlock.h"
#include "AIMutateTarget.h"
#include "AIPlant.h"
#include "AITargetScream.h"
#include "AIPanicBuff.h"
#include "AIPetPlayToPlayer.h"
#include "AIGhostBombAttack.h"
#include "AIGhostIceAttack.h"
#include "AIGhostBumpAttack.h"
#include "AIPetFollowOwner.h"
#include "AIThief.h"
#include "AIBananaFan.h"
#include "AIClimbTree.h"
#include "AIEvade.h"
#include "AILeopardAtk.h"
#include "ActorSavagePriest.h"
#include "AIBegEx.h"
#include "AIRanchWander.h"
#include "AIPosWander.h"
#include "AIFlyAttract.h"
#include "AIHungryStatus.h"
#include "AIHungryAtkTarget.h"
#include "AIHungryFollowPlayer.h"
#include "AiVacant.h"
#include "AILavaCrab.h"
#include "AIEarthCoreManLash.h"
#include "AIEarthCoreManRain.h"
#include "AIEarthCoreManSteal.h"
#include "AIFlyPanic.h"
#include "AIChangeBlock.h"
#include "AIFlyStayFlower.h"
#include "AISavageStandSleep.h"
#include "AIOriole.h"
#include "AIFlyFollow.h"
#include "AIStayBlock.h"
#include "AIAtkTiangou.h"
#include "AIAtkDestroyBlockTarget.h"
#include "AITargetSimilar.h"
#include "AIBatAttack.h"
#include "AIBatIdle.h"
#include "ClientMob.h"
#include "AIFishSwarm.h"
#include "AIFishSwimToSurface.h"
#include "AIFishFly.h"
#include "AIFishHoveringAround.h"
#include "AIBirdFishing.h"
#include "AIHungryDigEgg.h"
#include "AIWanderAmphibious.h"
#include "AIJumpGlissadeAmphibious.h"
#include "AILieAndRest.h"
#include "AIIceWizardFly.h"
#include "AIThrob.h"
#include "AIClean.h"
#include "AIFierce.h"
#include "AICreateVacantVortex.h"

/* function to register type */
static void tolua_reg_types (lua_State* tolua_S)
{
 tolua_usertype(tolua_S,"AIBase");
 tolua_usertype(tolua_S,"ClientMob");
 tolua_usertype(tolua_S,"ActorLiving");
}

/* method: addAiTaskHungryDigEgg of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiLieAndRest00
static int tolua_AITask_ClientMob_addAiLieAndRest00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
    tolua_Error tolua_err;
    if (
        !tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 5, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 6, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 7, 0, &tolua_err) ||
        !tolua_isnoobj(tolua_S, 8, &tolua_err)
        )
        goto tolua_lerror;
    else
#endif
    {
        ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
        int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
        int period = ((int)tolua_tonumber(tolua_S, 3, 0));
        int restTicksMin = ((int)tolua_tonumber(tolua_S, 4, 0));
        int restTicksMax = ((int)tolua_tonumber(tolua_S, 5, 0));
        int lieTick = ((int)tolua_tonumber(tolua_S, 6, 0));
        int standTick = ((int)tolua_tonumber(tolua_S, 7, 0));
#ifndef TOLUA_RELEASE
        if (!self) tolua_error(tolua_S, "invalid 'self' in function 'AILieAndRest'", NULL);
#endif
        {
            self->addAiTask<AILieAndRest>(iPriority, period, restTicksMin, restTicksMax, lieTick, standTick);
        }
    }
    return 0;
#ifndef TOLUA_RELEASE
    tolua_lerror :
    tolua_error(tolua_S, "#ferror in function 'AILieAndRest'.", &tolua_err);
    return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskHungryDigEgg of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskHungryDigEgg00
static int tolua_AITask_ClientMob_addAiTaskHungryDigEgg00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
	tolua_Error tolua_err;
	if (
		!tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 5, 0, &tolua_err) ||
		!tolua_isnoobj(tolua_S, 6, &tolua_err)
		)
		goto tolua_lerror;
	else
#endif
	{
		ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
		int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
		int blockid = ((int)tolua_tonumber(tolua_S, 3, 0));
		float fAddFood = ((int)tolua_tonumber(tolua_S, 4, 0));
		float searchBlockDist = ((int)tolua_tonumber(tolua_S, 5, 0));
#ifndef TOLUA_RELEASE
		if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAiTaskHungryDigEgg'", NULL);
#endif
		{
			self->addAiTask<AIHungryDigEgg>(iPriority, blockid, fAddFood, searchBlockDist);
		}
	}
	return 0;
#ifndef TOLUA_RELEASE
	tolua_lerror :
	tolua_error(tolua_S, "#ferror in function 'addAiTaskHungryDigEgg'.", &tolua_err);
	return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE


/* method: AITaskTargetSimilar of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAITaskTargetSimilar00
static int tolua_AITask_ClientMob_AITaskTargetSimilar00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
	tolua_Error tolua_err;
	if (
		!tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 5, 0, &tolua_err) ||
		!tolua_isnoobj(tolua_S, 6, &tolua_err)
		)
		goto tolua_lerror;
	else
#endif
	{
		ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
		int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
        int iAtkType = ((int)tolua_tonumber(tolua_S, 3, 0));
        int iChance = ((int)tolua_tonumber(tolua_S, 4, 0));
		int distance = ((int)tolua_tonumber(tolua_S, 5, 0));
#ifndef TOLUA_RELEASE
		if (!self) tolua_error(tolua_S, "invalid 'self' in function 'AITaskTargetSimilar'", NULL);
#endif
		{
			self->addAiTask<AITargetSimilar>(iPriority, iAtkType, iChance, distance);
		}
	}
	return 0;
#ifndef TOLUA_RELEASE
	tolua_lerror :
	tolua_error(tolua_S, "#ferror in function 'AITaskTargetSimilar'.", &tolua_err);
	return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIAtkDestroyBlockTarget of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIAtkDestroyBlockTarget00
static int tolua_AITask_ClientMob_addAIAtkDestroyBlockTarget00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
	tolua_Error tolua_err;
	if (
		!tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
		!tolua_isnoobj(tolua_S, 4, &tolua_err)
		)
		goto tolua_lerror;
	else
#endif
	{
		ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
		int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
        int distance = ((int)tolua_tonumber(tolua_S, 3, 0));
#ifndef TOLUA_RELEASE
		if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAIAtkDestroyBlockTarget'", NULL);
#endif
		{
			self->addAiTask<AIAtkDestroyBlockTarget>(iPriority, distance);
		}
	}
	return 0;
#ifndef TOLUA_RELEASE
	tolua_lerror :
	tolua_error(tolua_S, "#ferror in function 'addAIAtkDestroyBlockTarget'.", &tolua_err);
	return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskSwimming of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskSwimming00
static int tolua_AITask_ClientMob_addAiTaskSwimming00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskSwimming'", NULL);
#endif
  {
   self->addAiTask<AISwimming>(iPriority);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskSwimming'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskRandSwim of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskRandSwim00
static int tolua_AITask_ClientMob_addAiTaskRandSwim00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
     !tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
     !tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
     !tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
     !tolua_isnumber(tolua_S, 5, 0, &tolua_err) ||
     !tolua_isnoobj(tolua_S, 6, &tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
     ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
     int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
     int interval = ((int)tolua_tonumber(tolua_S, 3, 0));
     int minHight = ((int)tolua_tonumber(tolua_S, 4, 0));
     int maxHight = ((int)tolua_tonumber(tolua_S, 5, 0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskRandSwim'", NULL);
#endif
  {
   self->addAiTask<AIRandomSwim>(iPriority,interval, minHight, maxHight);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskRandSwim'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskFearPlayer of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskFearPlayer00
static int tolua_AITask_ClientMob_addAiTaskFearPlayer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int fearDist = ((int)  tolua_tonumber(tolua_S,3,0));
  float speedMulty = ((float)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskFearPlayer'", NULL);
#endif
  {
   self->addAiTask<AIFearPlayer>(iPriority,fearDist,speedMulty);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskFearPlayer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskFishBeg of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskFishBeg00
static int tolua_AITask_ClientMob_addAiTaskFishBeg00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iFavoriteFoodID = ((int)  tolua_tonumber(tolua_S,3,0));
  int iDist = ((int)  tolua_tonumber(tolua_S,4,0));
  bool fearMotion = ((bool)  tolua_toboolean(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskFishBeg'", NULL);
#endif
  {
   self->addAiTask<AIFishBeg>(iPriority,iFavoriteFoodID,iDist,fearMotion);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskFishBeg'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskFishAttack of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskFishAttack00
static int tolua_AITask_ClientMob_addAiTaskFishAttack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  bool iTrace = ((bool)  tolua_toboolean(tolua_S,3,0));
  float speedMulty = ((float)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskFishAttack'", NULL);
#endif
  {
   self->addAiTask<AIFishAttack>(iPriority,iTrace,speedMulty);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskFishAttack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskTransfiguration of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskTransfiguration00
static int tolua_AITask_ClientMob_addAiTaskTransfiguration00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int targetID = ((int)  tolua_tonumber(tolua_S,3,0));
  int waitTick = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskTransfiguration'", NULL);
#endif
  {
   self->addAiTask<AITransfiguration>(iPriority,targetID,waitTick);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskTransfiguration'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskSit of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskSit00
static int tolua_AITask_ClientMob_addAiTaskSit00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskSit'", NULL);
#endif
  {
   self->addAiTask<AISit>(iPriority);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskSit'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskAtk of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskAtk00
static int tolua_AITask_ClientMob_addAiTaskAtk00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iAtkeeType = ((int)  tolua_tonumber(tolua_S,3,0));
  bool iTrace = ((bool)  tolua_toboolean(tolua_S,4,0));
  float speed = ((float)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskAtk'", NULL);
#endif
  {
   self->addAiTask<AIAtk>(iPriority,iAtkeeType,iTrace,speed);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskAtk'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskAtk_TianGou of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskAtk_TianGou00
static int tolua_AITask_ClientMob_addAiTaskAtk_TianGou00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iAtkeeType = ((int)  tolua_tonumber(tolua_S,3,0));
  bool iTrace = ((bool)  tolua_toboolean(tolua_S,4,0));
  float speed = ((float)  tolua_tonumber(tolua_S,5,0));
  bool addBuff = ((bool)  tolua_toboolean(tolua_S,6,true));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskAtk_TianGou'", NULL);
#endif
  {
   self->addAiTask<AIAtkTiangou>(iPriority,iAtkeeType,iTrace,speed,addBuff);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskAtk_TianGou'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskArrowAttack of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskArrowAttack00
static int tolua_AITask_ClientMob_addAiTaskArrowAttack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,7,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,8,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int minAtkTime = ((int)  tolua_tonumber(tolua_S,4,0));
  int maxAtkTime = ((int)  tolua_tonumber(tolua_S,5,0));
  int atkrange = ((int)  tolua_tonumber(tolua_S,6,0));
  bool findObstacle = ((bool)  tolua_toboolean(tolua_S,7,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskArrowAttack'", NULL);
#endif
  {
   self->addAiTask<AIArrowAttack>(iPriority,speed,minAtkTime,maxAtkTime,atkrange,findObstacle);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskArrowAttack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskProjectileAttack of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskProjectileAttack00
static int tolua_AITask_ClientMob_addAiTaskProjectileAttack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,11,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,12,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float moveSpeed = ((float)  tolua_tonumber(tolua_S,3,0));
  int minAtkTime = ((int)  tolua_tonumber(tolua_S,4,0));
  int maxAtkTime = ((int)  tolua_tonumber(tolua_S,5,0));
  int atkrange = ((int)  tolua_tonumber(tolua_S,6,0));
  int projectileId = ((int)  tolua_tonumber(tolua_S,7,0));
  float power = ((float)  tolua_tonumber(tolua_S,8,0));
  int buffId = ((int)  tolua_tonumber(tolua_S,9,0));
  int count = ((int)  tolua_tonumber(tolua_S,10,0));
  int prob = ((int)  tolua_tonumber(tolua_S,11,100));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskProjectileAttack'", NULL);
#endif
  {
   self->addAiTask<AIProjectileAttack>(iPriority,moveSpeed,minAtkTime,maxAtkTime,atkrange,projectileId,power,buffId,count,prob);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskProjectileAttack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskPetFollowOwner of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskPetFollowOwner00
static int tolua_AITask_ClientMob_addAiTaskPetFollowOwner00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int iMinDist = ((int)  tolua_tonumber(tolua_S,4,0));
  int iTeleport = ((int)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskPetFollowOwner'", NULL);
#endif
  {
   self->addAiTask<AIPetFollowOwner>(iPriority,speed,iMinDist,iTeleport);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskPetFollowOwner'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskFollowOwner of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskFollowOwner00
static int tolua_AITask_ClientMob_addAiTaskFollowOwner00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int iMaxDist = ((int)  tolua_tonumber(tolua_S,4,0));
  int iMinDist = ((int)  tolua_tonumber(tolua_S,5,0));
  int iTeleport = ((int)  tolua_tonumber(tolua_S,6,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskFollowOwner'", NULL);
#endif
  {
   self->addAiTask<AIFollowOwner>(iPriority,speed,iMaxDist,iMinDist,iTeleport);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskFollowOwner'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskWander of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskWander00
static int tolua_AITask_ClientMob_addAiTaskWander00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isboolean(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int prob = ((int)  tolua_tonumber(tolua_S,4,120));
  bool howl = ((bool)  tolua_toboolean(tolua_S,5,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskWander'", NULL);
#endif
  {
   self->addAiTask<AIWander>(iPriority,speed,prob,howl);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskWander'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskPetWander of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskPetWander00
static int tolua_AITask_ClientMob_addAiTaskPetWander00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int prob = ((int)  tolua_tonumber(tolua_S,4,120));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskPetWander'", NULL);
#endif
  {
   self->addAiTask<AIPetWander>(iPriority,speed,prob);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskPetWander'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskBeg of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskBeg00
static int tolua_AITask_ClientMob_addAiTaskBeg00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iFavoriteFoodID = ((int)  tolua_tonumber(tolua_S,3,0));
  int iDist = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskBeg'", NULL);
#endif
  {
   self->addAiTask<AIBeg>(iPriority,iFavoriteFoodID,iDist);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskBeg'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskBegEx of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskBegEx00
static int tolua_AITask_ClientMob_addAiTaskBegEx00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  char* favoriteFoodId = ((char*)  tolua_tostring(tolua_S,3,0));
  int iDist = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskBegEx'", NULL);
#endif
  {
   self->addAiTask<AIBegEx>(iPriority,favoriteFoodId,iDist);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskBegEx'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskWatchClosest of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskWatchClosest00
static int tolua_AITask_ClientMob_addAiTaskWatchClosest00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iDist = ((int)  tolua_tonumber(tolua_S,3,0));
  int prob = ((int)  tolua_tonumber(tolua_S,4,50));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskWatchClosest'", NULL);
#endif
  {
   self->addAiTask<AIWatchClosest>(iPriority,iDist,prob);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskWatchClosest'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskLookIdle of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskLookIdle00
static int tolua_AITask_ClientMob_addAiTaskLookIdle00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int prob = ((int)  tolua_tonumber(tolua_S,3,50));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskLookIdle'", NULL);
#endif
  {
   self->addAiTask<AILookIdle>(iPriority,prob);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskLookIdle'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskTargetOnwnerHurtee of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskTargetOnwnerHurtee00
static int tolua_AITask_ClientMob_addAiTaskTargetOnwnerHurtee00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskTargetOnwnerHurtee'", NULL);
#endif
  {
   self->addAiTaskTarget<AITargetOwnerHurtee>(iPriority);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskTargetOnwnerHurtee'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskTargetOnwnerHurter of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskTargetOnwnerHurter00
static int tolua_AITask_ClientMob_addAiTaskTargetOnwnerHurter00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskTargetOnwnerHurter'", NULL);
#endif
  {
   self->addAiTaskTarget<AITargetOwnerHurter>(iPriority);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskTargetOnwnerHurter'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskTargetHurtee of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskTargetHurtee00
static int tolua_AITask_ClientMob_addAiTaskTargetHurtee00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,3,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  bool Help = ((bool)  tolua_toboolean(tolua_S,3,0));
  bool BossExclude = ((bool)  tolua_toboolean(tolua_S,4,true));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskTargetHurtee'", NULL);
#endif
  {
   self->addAiTaskTarget<AITargetHurtee>(iPriority,Help,BossExclude);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskTargetHurtee'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskTargetNonTamed of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskTargetNonTamed00
static int tolua_AITask_ClientMob_addAiTaskTargetNonTamed00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iAtkeeID = ((int)  tolua_tonumber(tolua_S,3,0));
  int iChance = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskTargetNonTamed'", NULL);
#endif
  {
   self->addAiTaskTarget<AITargetNonTamed>(iPriority,iAtkeeID,iChance);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskTargetNonTamed'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskTargetSpecificEntity of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskTargetSpecificEntity00
static int tolua_AITask_ClientMob_addAiTaskTargetSpecificEntity00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iAtkeeID = ((int)  tolua_tonumber(tolua_S,3,0));
  int iChance = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskTargetSpecificEntity'", NULL);
#endif
  {
   self->addAiTaskTarget<AITargetSpecificEntity>(iPriority,iAtkeeID,iChance);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskTargetSpecificEntity'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskRestrictSun of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskRestrictSun00
static int tolua_AITask_ClientMob_addAiTaskRestrictSun00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskRestrictSun'", NULL);
#endif
  {
   self->addAiTask<AIRestrictSun>(iPriority);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskRestrictSun'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskFleeSun of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskFleeSun00
static int tolua_AITask_ClientMob_addAiTaskFleeSun00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskFleeSun'", NULL);
#endif
  {
   self->addAiTask<AIFleeSun>(iPriority,speed);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskFleeSun'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskTargetNearest of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskTargetNearest00
static int tolua_AITask_ClientMob_addAiTaskTargetNearest00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,8,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iChance = ((int)  tolua_tonumber(tolua_S,3,0));
  bool iCheckSight = ((bool)  tolua_toboolean(tolua_S,4,0));
  float brightNess = ((float)  tolua_tonumber(tolua_S,5,0));
  float minhp = ((float)  tolua_tonumber(tolua_S,6,0));
  int bossEffect = ((int)  tolua_tonumber(tolua_S,7,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskTargetNearest'", NULL);
#endif
  {
   self->addAiTaskTarget<AITargetNearest>(iPriority,iChance,iCheckSight,brightNess,minhp,bossEffect);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskTargetNearest'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskBreakDoor of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskBreakDoor00
static int tolua_AITask_ClientMob_addAiTaskBreakDoor00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskBreakDoor'", NULL);
#endif
  {
   self->addAiTask<AIBreakDoor>(iPriority);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskBreakDoor'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskMoveTowardsRestriction of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskMoveTowardsRestriction00
static int tolua_AITask_ClientMob_addAiTaskMoveTowardsRestriction00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskMoveTowardsRestriction'", NULL);
#endif
  {
   self->addAiTask<AIMoveTowardsRestriction>(iPriority,speed);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskMoveTowardsRestriction'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiLeapAtTarget of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiLeapAtTarget00
static int tolua_AITask_ClientMob_addAiLeapAtTarget00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
	tolua_Error tolua_err;
	if (
		!tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 5, 0, &tolua_err) ||
		!tolua_isnoobj(tolua_S, 6, &tolua_err)
		)
		goto tolua_lerror;
	else
#endif
	{
		ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
		int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
		float motionY = ((float)tolua_tonumber(tolua_S, 3, 0));
		int minRange = ((int)tolua_tonumber(tolua_S, 4, 0));
		int maxRange = ((int)tolua_tonumber(tolua_S, 5, 0));
#ifndef TOLUA_RELEASE
		if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAiLeapAtTarget'", NULL);
#endif
		{
			self->addAiTask<AILeapAtTarget>(iPriority, motionY, minRange, maxRange);
		}
	}
	return 0;
#ifndef TOLUA_RELEASE
	tolua_lerror :
				 tolua_error(tolua_S, "#ferror in function 'addAiLeapAtTarget'.", &tolua_err);
				 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskPanic of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskPanic00
static int tolua_AITask_ClientMob_addAiTaskPanic00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  float maxhp = ((float)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskPanic'", NULL);
#endif
  {
   self->addAiTask<AIPanic>(iPriority,speed,maxhp);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskPanic'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskFlyPanic of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskFlyPanic00
static int tolua_AITask_ClientMob_addAiTaskFlyPanic00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int maxDis = ((int)  tolua_tonumber(tolua_S,4,0));
  float maxhp = ((float)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskFlyPanic'", NULL);
#endif
  {
   self->addAiTask<AIFlyPanic>(iPriority,speed,maxDis,maxhp);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskFlyPanic'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiMate of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiMate00
static int tolua_AITask_ClientMob_addAiMate00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int minbabies = ((int)  tolua_tonumber(tolua_S,4,1));
  int maxbabies = ((int)  tolua_tonumber(tolua_S,5,1));
  int itemID = ((int)  tolua_tonumber(tolua_S,6,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiMate'", NULL);
#endif
  {
   self->addAiTask<AIMate>(iPriority,speed,minbabies,maxbabies,itemID);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiMate'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskFollowParent of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskFollowParent00
static int tolua_AITask_ClientMob_addAiTaskFollowParent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskFollowParent'", NULL);
#endif
  {
   self->addAiTask<AIFollowParent>(iPriority,speed);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskFollowParent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskBoomAtk of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskBoomAtk00
static int tolua_AITask_ClientMob_addAiTaskBoomAtk00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskBoomAtk'", NULL);
#endif
  {
   self->addAiTask<AIBoom>(iPriority);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskBoomAtk'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskEatGrass of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskEatGrass00
static int tolua_AITask_ClientMob_addAiTaskEatGrass00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskEatGrass'", NULL);
#endif
  {
   self->addAiTask<AIEatGrass>(iPriority);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskEatGrass'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskFollowDirection of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskFollowDirection00
static int tolua_AITask_ClientMob_addAiTaskFollowDirection00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskFollowDirection'", NULL);
#endif
  {
   self->addAiTask<AIFollowDirection>(iPriority,speed);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskFollowDirection'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskClosestDance of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskClosestDance00
static int tolua_AITask_ClientMob_addAiTaskClosestDance00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,10,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iDist = ((int)  tolua_tonumber(tolua_S,3,0));
  int number = ((int)  tolua_tonumber(tolua_S,4,0));
  float speed = ((float)  tolua_tonumber(tolua_S,5,0));
  int cdtime = ((int)  tolua_tonumber(tolua_S,6,0));
  int width = ((int)  tolua_tonumber(tolua_S,7,0));
  int dancetime = ((int)  tolua_tonumber(tolua_S,8,0));
  int prob = ((int)  tolua_tonumber(tolua_S,9,20));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskClosestDance'", NULL);
#endif
  {
   self->addAiTaskTarget<AIClosestDance>(iPriority,iDist,number,speed,cdtime,width,dancetime,prob);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskClosestDance'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTargetMutate of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTargetMutate00
static int tolua_AITask_ClientMob_addAiTargetMutate00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,8,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,3,0));
  int prob = ((int)  tolua_tonumber(tolua_S,4,0));
  int buffid = ((int)  tolua_tonumber(tolua_S,5,0));
  bool CheckSight = ((bool)  tolua_toboolean(tolua_S,6,0));
  int length = ((int)  tolua_tonumber(tolua_S,7,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTargetMutate'", NULL);
#endif
  {
   self->addAiTaskTarget<AIMutateTarget>(iPriority,blockid,prob,buffid,CheckSight,length);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTargetMutate'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAITaskToppleOver of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAITaskToppleOver00
static int tolua_AITask_ClientMob_addAITaskToppleOver00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAITaskToppleOver'", NULL);
#endif
  {
   self->addAiTask<AIToppleOver>(iPriority);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAITaskToppleOver'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAITaskSitbyItem of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAITaskSitbyItem00
static int tolua_AITask_ClientMob_addAITaskSitbyItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAITaskSitbyItem'", NULL);
#endif
  {
   self->addAiTask<AISitbyItem>(iPriority,speed);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAITaskSitbyItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskEatLeaf of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskEatLeaf00
static int tolua_AITask_ClientMob_addAiTaskEatLeaf00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int prob = ((int)  tolua_tonumber(tolua_S,3,0));
  int probadult = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskEatLeaf'", NULL);
#endif
  {
   self->addAiTask<AIEatLeaf>(iPriority,prob,probadult);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskEatLeaf'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAITaskEatFlower of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAITaskEatFlower00
static int tolua_AITask_ClientMob_addAITaskEatFlower00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int prob = ((int)  tolua_tonumber(tolua_S,3,0));
  int probadult = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAITaskEatFlower'", NULL);
#endif
  {
   self->addAiTask<AIEatFlower>(iPriority,prob,probadult);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAITaskEatFlower'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskRide of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskRide00
static int tolua_AITask_ClientMob_addAiTaskRide00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int monsterid = ((int)  tolua_tonumber(tolua_S,3,0));
  int Dist = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskRide'", NULL);
#endif
  {
   self->addAiTask<AIRideHorse>(iPriority,monsterid,Dist);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskRide'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskKickAway of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskKickAway00
static int tolua_AITask_ClientMob_addAiTaskKickAway00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int monsterid = ((int)  tolua_tonumber(tolua_S,3,0));
  int prob = ((int)  tolua_tonumber(tolua_S,4,0));
  int force = ((int)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskKickAway'", NULL);
#endif
  {
   self->addAiTask<AIKickAway>(iPriority,monsterid,prob,force);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskKickAway'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskMakeTrouble of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskMakeTrouble00
static int tolua_AITask_ClientMob_addAiTaskMakeTrouble00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,7,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,8,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,3,0));
  int Prob = ((int)  tolua_tonumber(tolua_S,4,0));
  float speed = ((float)  tolua_tonumber(tolua_S,5,0));
  float searchBlockDist = ((float)  tolua_tonumber(tolua_S,6,0));
  bool isHit = ((bool)  tolua_toboolean(tolua_S,7,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskMakeTrouble'", NULL);
#endif
  {
   self->addAiTask<AIMakeTrouble>(iPriority,blockid,Prob,speed,searchBlockDist,isHit);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskMakeTrouble'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIGetSpecialAttackattr of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIGetSpecialAttackattr00
static int tolua_AITask_ClientMob_addAIGetSpecialAttackattr00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,3,0));
  int Prob = ((int)  tolua_tonumber(tolua_S,4,0));
  float speed = ((float)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIGetSpecialAttackattr'", NULL);
#endif
  {
   self->addAiTask<AIGetSpecialAttackattr>(iPriority,blockid,Prob,speed);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIGetSpecialAttackattr'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAISitBlock of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAISitBlock00
static int tolua_AITask_ClientMob_addAISitBlock00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,3,0));
  int Prob = ((int)  tolua_tonumber(tolua_S,4,0));
  float speed = ((float)  tolua_tonumber(tolua_S,5,0));
  int persist_tick = ((int)  tolua_tonumber(tolua_S,6,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAISitBlock'", NULL);
#endif
  {
   self->addAiTask<AISitBlock>(iPriority,blockid,Prob,speed,persist_tick);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAISitBlock'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAILoveBlock of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAILoveBlock00
static int tolua_AITask_ClientMob_addAILoveBlock00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,3,0));
  int Prob = ((int)  tolua_tonumber(tolua_S,4,0));
  float speed = ((float)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAILoveBlock'", NULL);
#endif
  {
   self->addAiTask<AILoveBlock>(iPriority,blockid,Prob,speed);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAILoveBlock'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAITargetFollowingPlayer of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAITargetFollowingPlayer00
static int tolua_AITask_ClientMob_addAITargetFollowingPlayer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int prob = ((int)  tolua_tonumber(tolua_S,3,0));
  bool iCheckSight = ((bool)  tolua_toboolean(tolua_S,4,0));
  int speed = ((int)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAITargetFollowingPlayer'", NULL);
#endif
  {
   self->addAiTask<AITargetFollowingPlayer>(iPriority,prob,iCheckSight,speed);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAITargetFollowingPlayer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIHoldMonster of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIHoldMonster00
static int tolua_AITask_ClientMob_addAIHoldMonster00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int monsterid = ((int)  tolua_tonumber(tolua_S,3,0));
  int prob = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIHoldMonster'", NULL);
#endif
  {
   self->addAiTask<AIHoldMonster>(iPriority,monsterid,prob);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIHoldMonster'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAICeilingAtk of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAICeilingAtk00
static int tolua_AITask_ClientMob_addAICeilingAtk00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int attachTimer = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAICeilingAtk'", NULL);
#endif
  {
   self->addAiTask<AICeilingAtk>(iPriority,attachTimer);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAICeilingAtk'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIGoCeiling of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIGoCeiling00
static int tolua_AITask_ClientMob_addAIGoCeiling00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int prob = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIGoCeiling'", NULL);
#endif
  {
   self->addAiTask<AIGoCeiling>(iPriority,prob);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIGoCeiling'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskSleep of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskSleep00
static int tolua_AITask_ClientMob_addAiTaskSleep00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,8,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int period = ((int)  tolua_tonumber(tolua_S,3,0));
  int sleepticks = ((int)  tolua_tonumber(tolua_S,4,0));
  int productID = ((int)  tolua_tonumber(tolua_S,5,0));
  int minNum = ((int)  tolua_tonumber(tolua_S,6,0));
  int maxNum = ((int)  tolua_tonumber(tolua_S,7,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskSleep'", NULL);
#endif
  {
   self->addAiTask<AISleep>(iPriority,period,sleepticks,productID,minNum,maxNum);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskSleep'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiLoggerHeads of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiLoggerHeads00
static int tolua_AITask_ClientMob_addAiLoggerHeads00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int itemid = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiLoggerHeads'", NULL);
#endif
  {
   self->addAiTask<AILoggerHeads>(iPriority,speed,itemid);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiLoggerHeads'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskLayEggs of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskLayEggs00
static int tolua_AITask_ClientMob_addAiTaskLayEggs00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isstring(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,11,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int layprob = ((int)  tolua_tonumber(tolua_S,3,0));
  const char* laysound = ((const char*)  tolua_tostring(tolua_S,4,0));
  int item1 = ((int)  tolua_tonumber(tolua_S,5,0));
  int prob1 = ((int)  tolua_tonumber(tolua_S,6,100));
  int item2 = ((int)  tolua_tonumber(tolua_S,7,0));
  int prob2 = ((int)  tolua_tonumber(tolua_S,8,0));
  int item3 = ((int)  tolua_tonumber(tolua_S,9,0));
  int prob3 = ((int)  tolua_tonumber(tolua_S,10,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskLayEggs'", NULL);
#endif
  {
   self->addAiTaskLayEggs(iPriority,layprob,laysound,item1,prob1,item2,prob2,item3,prob3);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskLayEggs'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskLayEggInNest of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskLayEggInNest00
static int tolua_AITask_ClientMob_addAiTaskLayEggInNest00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int hatch_blockid = ((int)  tolua_tonumber(tolua_S,3,0));
  int eggid = ((int)  tolua_tonumber(tolua_S,4,0));
  int nestgen = ((int)  tolua_tonumber(tolua_S,5,0));
  int eggblockid = ((int)tolua_tonumber(tolua_S, 6, 0));

#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskLayEggInNest'", NULL);
#endif
  {
   self->addAiTask<AILayEggInNest>(iPriority,hatch_blockid,eggid,nestgen, eggblockid);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskLayEggInNest'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskHatch of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskHatch00
static int tolua_AITask_ClientMob_addAiTaskHatch00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int hatch_blockid = ((int)  tolua_tonumber(tolua_S,3,0));
  int maxhatchticks = ((int)  tolua_tonumber(tolua_S,4,0));
  int babyId = ((int)tolua_tonumber(tolua_S, 5, 0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskHatch'", NULL);
#endif
  {
   self->addAiTask<AIHatch>(iPriority,hatch_blockid,maxhatchticks, babyId);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskHatch'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskEatFeedBlock of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskEatFeedBlock00
static int tolua_AITask_ClientMob_addAiTaskEatFeedBlock00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int feedblock = ((int)  tolua_tonumber(tolua_S,3,0));
  int maxeatticks = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskEatFeedBlock'", NULL);
#endif
  {
   self->addAiTask<AIEatFeedBlock>(iPriority,feedblock,maxeatticks);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskEatFeedBlock'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskMilking of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskMilking00
static int tolua_AITask_ClientMob_addAiTaskMilking00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskMilking'", NULL);
#endif
  {
   self->addAiTask<AIMilking>(iPriority);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskMilking'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskPetPlayToPlayer of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskPetPlayToPlayer00
static int tolua_AITask_ClientMob_addAiTaskPetPlayToPlayer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isstring(tolua_S,9,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,10,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iDist = ((int)  tolua_tonumber(tolua_S,3,0));
  int width = ((int)  tolua_tonumber(tolua_S,4,0));
  int playtick = ((int)  tolua_tonumber(tolua_S,5,0));
  int animal_seq = ((int)  tolua_tonumber(tolua_S,6,0));
  int prob = ((int)  tolua_tonumber(tolua_S,7,0));
  float speed = ((float)  tolua_tonumber(tolua_S,8,0));
  const char* effect = ((const char*)  tolua_tostring(tolua_S,9,""));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskPetPlayToPlayer'", NULL);
#endif
  {
   self->addAiTask<AIPetPlayToPlayer>(iPriority,iDist,width,playtick,animal_seq,prob,speed,effect);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskPetPlayToPlayer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskPetDanceToPlayer of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskPetDanceToPlayer00
static int tolua_AITask_ClientMob_addAiTaskPetDanceToPlayer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,8,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iDist = ((int)  tolua_tonumber(tolua_S,3,0));
  int width = ((int)  tolua_tonumber(tolua_S,4,0));
  int dancetick = ((int)  tolua_tonumber(tolua_S,5,0));
  int prob = ((int)  tolua_tonumber(tolua_S,6,0));
  float speed = ((float)  tolua_tonumber(tolua_S,7,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskPetDanceToPlayer'", NULL);
#endif
  {
   self->addAiTask<AIPetDanceToPlayer>(iPriority,iDist,width,dancetick,prob,speed);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskPetDanceToPlayer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAITaskSpecialAct of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAITaskSpecialAct00
static int tolua_AITask_ClientMob_addAITaskSpecialAct00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int period = ((int)  tolua_tonumber(tolua_S,3,0));
  int sleepticks = ((int)  tolua_tonumber(tolua_S,4,0));
  int actorflag = ((int)  tolua_tonumber(tolua_S,5,-1));
  int itemid = ((int)  tolua_tonumber(tolua_S,6,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAITaskSpecialAct'", NULL);
#endif
  {
   self->addAiTask<AISpecialAct>(iPriority,period,sleepticks,actorflag,itemid);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAITaskSpecialAct'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAITaskHungry of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAITaskHungry00
static int tolua_AITask_ClientMob_addAITaskHungry00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int period = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAITaskHungry'", NULL);
#endif
  {
   self->addAiTask<AIHungry>(iPriority,period);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAITaskHungry'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskFlyAttack of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskFlyAttack00
static int tolua_AITask_ClientMob_addAiTaskFlyAttack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  bool bTrace = ((bool)  tolua_toboolean(tolua_S,3,0));
  float speedMulty = ((float)  tolua_tonumber(tolua_S,4,0));
  int buffid = ((int)  tolua_tonumber(tolua_S,5,0));
  int bufflevel = ((int)  tolua_tonumber(tolua_S,6,1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskFlyAttack'", NULL);
#endif
  {
   self->addAiTask<AIFlyAttack>(iPriority,bTrace,speedMulty,buffid,bufflevel);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskFlyAttack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskRandFly of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskRandFly00
static int tolua_AITask_ClientMob_addAiTaskRandFly00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int interval = ((int)  tolua_tonumber(tolua_S,3,0));
  int maxheight = ((int)  tolua_tonumber(tolua_S,4,0));
  int maxrange = ((int)  tolua_tonumber(tolua_S,5,0));
  int randprob = ((int)  tolua_tonumber(tolua_S,6,100));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskRandFly'", NULL);
#endif
  {
   self->addAiTask<AIRandomFly>(iPriority,interval,maxheight,maxrange,randprob);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskRandFly'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskFlyBeg of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskFlyBeg00
static int tolua_AITask_ClientMob_addAiTaskFlyBeg00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iNeedFavoriteFood = ((int)  tolua_tonumber(tolua_S,3,0));
  int iDist = ((int)  tolua_tonumber(tolua_S,4,0));
  bool fearMotion = ((bool)  tolua_toboolean(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskFlyBeg'", NULL);
#endif
  {
   self->addAiTask<AIFlyBeg>(iPriority,iNeedFavoriteFood,iDist,fearMotion);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskFlyBeg'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskFlyLoveBlock of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskFlyLoveBlock00
static int tolua_AITask_ClientMob_addAiTaskFlyLoveBlock00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,3,0));
  int Prob = ((int)  tolua_tonumber(tolua_S,4,0));
  float speed = ((float)  tolua_tonumber(tolua_S,5,0));
  int lovetick = ((int)  tolua_tonumber(tolua_S,6,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskFlyLoveBlock'", NULL);
#endif
  {
   self->addAiTask<AIFlyLoveBlock>(iPriority,blockid,Prob,speed,lovetick);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskFlyLoveBlock'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskFlyPointToPoint of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskFlyPointToPoint00
static int tolua_AITask_ClientMob_addAiTaskFlyPointToPoint00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,11,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,12,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float startX = ((float)  tolua_tonumber(tolua_S,3,0));
  float startY = ((float)  tolua_tonumber(tolua_S,4,0));
  float startZ = ((float)  tolua_tonumber(tolua_S,5,0));
  float endX = ((float)  tolua_tonumber(tolua_S,6,0));
  float endY = ((float)  tolua_tonumber(tolua_S,7,0));
  float endZ = ((float)  tolua_tonumber(tolua_S,8,0));
  float speed = ((float)  tolua_tonumber(tolua_S,9,1.0f));
  int avoidanceRange = ((int)  tolua_tonumber(tolua_S,10,3));
  float avoidanceHeight = ((float)  tolua_tonumber(tolua_S,11,50.0f));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskFlyPointToPoint'", NULL);
#endif
  {
   WCoord startPos(startX, startY, startZ);
   WCoord endPos(endX, endY, endZ);
   self->addAiTask<AIFlyPointToPoint>(iPriority, startPos, endPos, speed, avoidanceRange, avoidanceHeight);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskFlyPointToPoint'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAITaskFearItem of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAITaskFearItem00
static int tolua_AITask_ClientMob_addAITaskFearItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int ItemID = ((int)  tolua_tonumber(tolua_S,4,0));
  int length = ((int)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAITaskFearItem'", NULL);
#endif
  {
   self->addAiTask<AIFearItem>(iPriority,speed,ItemID,length);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAITaskFearItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAITaskDissolvedByItem of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAITaskDissolvedByItem00
static int tolua_AITask_ClientMob_addAITaskDissolvedByItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int dietime = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAITaskDissolvedByItem'", NULL);
#endif
  {
   self->addAiTask<AIDissolvedByItem>(iPriority,speed,dietime);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAITaskDissolvedByItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskItemPanic of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskItemPanic00
static int tolua_AITask_ClientMob_addAiTaskItemPanic00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,9,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,1.4f));
  int range = ((int)  tolua_tonumber(tolua_S,4,1*BLOCK_SIZE));
  int panicTick = ((int)  tolua_tonumber(tolua_S,5,40));
  int intervalTick = ((int)  tolua_tonumber(tolua_S,6,40));
  int itemId1 = ((int)  tolua_tonumber(tolua_S,7,0));
  int itemId2 = ((int)  tolua_tonumber(tolua_S,8,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskItemPanic'", NULL);
#endif
  {
   self->addAiTask<AIItemPanic>(iPriority,speed,range,panicTick,intervalTick,itemId1,itemId2);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskItemPanic'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskPlayerPanic of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskPlayerPanic00
static int tolua_AITask_ClientMob_addAiTaskPlayerPanic00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,1.4f));
  int range = ((int)  tolua_tonumber(tolua_S,4,8*BLOCK_SIZE));
  int panicTick = ((int)  tolua_tonumber(tolua_S,5,40));
  int intervalTick = ((int)  tolua_tonumber(tolua_S,6,40));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskPlayerPanic'", NULL);
#endif
  {
   self->addAiTask<AIPlayerPanic>(iPriority,speed,range,panicTick,intervalTick);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskPlayerPanic'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskSeparate of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskSeparate00
static int tolua_AITask_ClientMob_addAiTaskSeparate00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int spawnMobDelayTick = ((int)  tolua_tonumber(tolua_S,3,30));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskSeparate'", NULL);
#endif
  {
   self->addAiTask<AISeparate>(iPriority,spawnMobDelayTick);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskSeparate'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskSeparatePanic of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskSeparatePanic00
static int tolua_AITask_ClientMob_addAiTaskSeparatePanic00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,1.4f));
  int panicTick = ((int)  tolua_tonumber(tolua_S,4,160));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskSeparatePanic'", NULL);
#endif
  {
   self->addAiTask<AISeparatePanic>(iPriority,speed,panicTick);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskSeparatePanic'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskCombine of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskCombine00
static int tolua_AITask_ClientMob_addAiTaskCombine00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,9,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,1.4f));
  int combineDistance = ((int)  tolua_tonumber(tolua_S,4,8*BLOCK_SIZE));
  int phase1CombineTick = ((int)  tolua_tonumber(tolua_S,5,200));
  int phase2CombineTick = ((int)  tolua_tonumber(tolua_S,6,200));
  float minHpRatio = ((float)  tolua_tonumber(tolua_S,7,0.5));
  float rotateYawDelta = ((float)  tolua_tonumber(tolua_S,8,22));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskCombine'", NULL);
#endif
  {
   self->addAiTask<AICombine>(iPriority,speed,combineDistance,phase1CombineTick,phase2CombineTick,minHpRatio,rotateYawDelta);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskCombine'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskWizardFly of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskWizardFly00
static int tolua_AITask_ClientMob_addAiTaskWizardFly00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int interval = ((int)  tolua_tonumber(tolua_S,3,0));
  int maxheight = ((int)  tolua_tonumber(tolua_S,4,0));
  int minheight = ((int)  tolua_tonumber(tolua_S,5,0));
  int maxrange = ((int)  tolua_tonumber(tolua_S,6,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskWizardFly'", NULL);
#endif
  {
   self->addAiTask<AIWizardFly>(iPriority,interval,maxheight,minheight,maxrange);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskWizardFly'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE



/* method: addAiTaskIceWizardFly of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskIceWizardFly00
static int tolua_AITask_ClientMob_addAiTaskIceWizardFly00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
	tolua_Error tolua_err;
	if (
		!tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 5, 1, &tolua_err) ||
		!tolua_isnumber(tolua_S, 6, 1, &tolua_err) ||
		!tolua_isnoobj(tolua_S, 7, &tolua_err)
		)
		goto tolua_lerror;
	else
#endif
	{
		ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
		int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
		int interval = ((int)tolua_tonumber(tolua_S, 3, 0));
		int maxheight = ((int)tolua_tonumber(tolua_S, 4, 0));
		int minheight = ((int)tolua_tonumber(tolua_S, 5, 0));
		int maxrange = ((int)tolua_tonumber(tolua_S, 6, 0));
#ifndef TOLUA_RELEASE
		if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAiTaskIceWizardFly'", NULL);
#endif
		{
			self->addAiTask<AIIceWizardFly>(iPriority, interval, maxheight, minheight, maxrange);
		}
	}
	return 0;
#ifndef TOLUA_RELEASE
	tolua_lerror :
	tolua_error(tolua_S, "#ferror in function 'addAiTaskIceWizardFly'.", &tolua_err);
	return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE


/* method: addAiTaskWizardAttack of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskWizardAttack00
static int tolua_AITask_ClientMob_addAiTaskWizardAttack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,11,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,12,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0.2f));
  int damage = ((int)  tolua_tonumber(tolua_S,4,5));
  int attackDistance = ((int)  tolua_tonumber(tolua_S,5,0));
  int phase1AttackTick = ((int)  tolua_tonumber(tolua_S,6,40));
  int phase2AttackTick = ((int)  tolua_tonumber(tolua_S,7,160));
  int phase3AttackTick = ((int)  tolua_tonumber(tolua_S,8,100));
  int buffid = ((int)  tolua_tonumber(tolua_S,9,FREEZING_BUFF));
  int bufflevel = ((int)  tolua_tonumber(tolua_S,10,1));
  int prob = ((int)  tolua_tonumber(tolua_S,11,10));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskWizardAttack'", NULL);
#endif
  {
   self->addAiTask<AIWizardAttack>(iPriority,speed,damage,attackDistance,phase1AttackTick,phase2AttackTick,phase3AttackTick,buffid,bufflevel,prob);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskWizardAttack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskWizardProjectileAttack of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskWizardProjectileAttack00
static int tolua_AITask_ClientMob_addAiTaskWizardProjectileAttack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,11,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float moveSpeed = ((float)  tolua_tonumber(tolua_S,3,0));
  int minAtkTime = ((int)  tolua_tonumber(tolua_S,4,0));
  int maxAtkTime = ((int)  tolua_tonumber(tolua_S,5,0));
  int atkrange = ((int)  tolua_tonumber(tolua_S,6,0));
  int projectileId = ((int)  tolua_tonumber(tolua_S,7,0));
  float power = ((float)  tolua_tonumber(tolua_S,8,0));
  int buffId = ((int)  tolua_tonumber(tolua_S,9,0));
  int count = ((int)  tolua_tonumber(tolua_S,10,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskWizardProjectileAttack'", NULL);
#endif
  {
   self->addAiTask<AIWizardProjectileAttack>(iPriority,moveSpeed,minAtkTime,maxAtkTime,atkrange,projectileId,power,buffId,count);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskWizardProjectileAttack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE



/* method: addAiTaskWizard of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskIceWizardFindActor00
static int tolua_AITask_ClientMob_addAiTaskIceWizardFindActor00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
	tolua_Error tolua_err;
	if (
		!tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
		!tolua_isnoobj(tolua_S, 4, &tolua_err)
		)
		goto tolua_lerror;
	else
#endif
	{
		ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
		int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
	    int rate = ((int)tolua_tonumber(tolua_S, 3, 0));
#ifndef TOLUA_RELEASE
		if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAiTaskIceWizardFindActor'", NULL);
#endif
		{
			self->addAiTask<AIIceWizardFindActor>(iPriority, rate);
		}
	}
	return 0;
#ifndef TOLUA_RELEASE
	tolua_lerror :
	tolua_error(tolua_S, "#ferror in function 'addAiTaskIceWizardFindActor'.", &tolua_err);
	return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE


/* method: addAiTaskIceWizardProjectileAttack of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskIceWizardProjectileAttack00
static int tolua_AITask_ClientMob_addAiTaskIceWizardProjectileAttack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
	tolua_Error tolua_err;
	if (
		!tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 5, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 6, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 7, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 8, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 9, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 10, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 11, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 12, 0, &tolua_err) ||
		!tolua_isnoobj(tolua_S, 13, &tolua_err)
		)
		goto tolua_lerror;
	else
#endif
	{
		ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
		int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
		float moveSpeed = ((float)tolua_tonumber(tolua_S, 3, 0));
		int minAtkTime = ((int)tolua_tonumber(tolua_S, 4, 0));
		int maxAtkTime = ((int)tolua_tonumber(tolua_S, 5, 0));
		int atkrange = ((int)tolua_tonumber(tolua_S, 6, 0));
		int projectileId = ((int)tolua_tonumber(tolua_S, 7, 0));
        int specialProjectileId = ((int)tolua_tonumber(tolua_S, 8, 0));
		float power = ((float)tolua_tonumber(tolua_S, 9, 0));
		int buffOneId = ((int)tolua_tonumber(tolua_S, 10, 0));
        int buffTwoId = ((int)tolua_tonumber(tolua_S, 11, 0));
		int count = ((int)tolua_tonumber(tolua_S, 12, 0));
#ifndef TOLUA_RELEASE
		if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAiTaskIceWizardProjectileAttack'", NULL);
#endif
		{
			self->addAiTask<AIIceWizardProjectileAttack>(iPriority, moveSpeed, minAtkTime, maxAtkTime, atkrange,
                                                         projectileId, specialProjectileId, power, buffOneId, buffTwoId, count);
		}
	}
	return 0;
#ifndef TOLUA_RELEASE
	tolua_lerror :
	tolua_error(tolua_S, "#ferror in function 'addAiTaskIceWizardProjectileAttack'.", &tolua_err);
	return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE


/* method: addAITaskBumpAttack of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAITaskBumpAttack00
static int tolua_AITask_ClientMob_addAITaskBumpAttack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,11,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iDist = ((int)  tolua_tonumber(tolua_S,3,0));
  int dizzyTick = ((int)  tolua_tonumber(tolua_S,4,0));
  int preTick = ((int)  tolua_tonumber(tolua_S,5,0));
  int prob = ((int)  tolua_tonumber(tolua_S,6,0));
  float speed = ((float)  tolua_tonumber(tolua_S,7,0));
  float up = ((float)  tolua_tonumber(tolua_S,8,0));
  float back = ((float)  tolua_tonumber(tolua_S,9,0));
  int mode = ((int)  tolua_tonumber(tolua_S,10,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAITaskBumpAttack'", NULL);
#endif
  {
   self->addAiTask<AIBumpAttack>(iPriority,iDist,dizzyTick,preTick,prob,speed,up,back,mode);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAITaskBumpAttack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIDigBlock of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIDigBlock00
static int tolua_AITask_ClientMob_addAIDigBlock00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,10,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iprob = ((int)  tolua_tonumber(tolua_S,3,0));
  int idist = ((int)  tolua_tonumber(tolua_S,4,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,5,0));
  float speed = ((float)  tolua_tonumber(tolua_S,6,0));
  int digdist = ((int)  tolua_tonumber(tolua_S,7,0));
  int food = ((int)  tolua_tonumber(tolua_S,8,0));
  float foodreduce = ((float)  tolua_tonumber(tolua_S,9,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIDigBlock'", NULL);
#endif
  {
   self->addAiTask<AIDigBlock>(iPriority,iprob,idist,blockid,speed,digdist,food,foodreduce);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror: 
 tolua_error(tolua_S,"#ferror in function 'addAIDigBlock'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIPickupItem of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIPickupItem00
static int tolua_AITask_ClientMob_addAIPickupItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,11,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iprob = ((int)  tolua_tonumber(tolua_S,3,0));
  int itemid = ((int)  tolua_tonumber(tolua_S,4,0));
  int num = ((int)  tolua_tonumber(tolua_S,5,0));
  int idist = ((int)  tolua_tonumber(tolua_S,6,0));
  float speed = ((float)  tolua_tonumber(tolua_S,7,0));
  int pickupdist = ((int)  tolua_tonumber(tolua_S,8,0));
  int food = ((int)  tolua_tonumber(tolua_S,9,0));
  float foodreduce = ((float)  tolua_tonumber(tolua_S,10,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIPickupItem'", NULL);
#endif
  {
   self->addAiTask<AIPickupItem>(iPriority,iprob,itemid,num,idist,speed,pickupdist,food,foodreduce);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIPickupItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIPickupItemEx of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIPickupItemEx00
static int tolua_AITask_ClientMob_addAIPickupItemEx00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,8,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,3,0));
  float fAddFood = ((float)  tolua_tonumber(tolua_S,4,0));
  float searchBlockDist = ((float)  tolua_tonumber(tolua_S,5,0));
  int nSeq = ((int)  tolua_tonumber(tolua_S,6,0));
  int nSeqTick = ((int)  tolua_tonumber(tolua_S,7,60));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIPickupItemEx'", NULL);
#endif
  {
   self->addAiTask<AIPickupItemEx>(iPriority,blockid,fAddFood,searchBlockDist,nSeq,nSeqTick);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIPickupItemEx'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIStoreItem of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIStoreItem00
static int tolua_AITask_ClientMob_addAIStoreItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,11,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,12,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,13,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iprob = ((int)  tolua_tonumber(tolua_S,3,0));
  int itemid = ((int)  tolua_tonumber(tolua_S,4,0));
  int num = ((int)  tolua_tonumber(tolua_S,5,0));
  int idist = ((int)  tolua_tonumber(tolua_S,6,0));
  int containertype = ((int)  tolua_tonumber(tolua_S,7,0));
  int containerid = ((int)  tolua_tonumber(tolua_S,8,0));
  float speed = ((float)  tolua_tonumber(tolua_S,9,0));
  int storedist = ((int)  tolua_tonumber(tolua_S,10,0));
  int food = ((int)  tolua_tonumber(tolua_S,11,0));
  float foodreduce = ((float)  tolua_tonumber(tolua_S,12,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIStoreItem'", NULL);
#endif
  {
   self->addAiTask<AIStoreItem>(iPriority,iprob,itemid,num,idist,containertype,containerid,speed,storedist,food,foodreduce);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIStoreItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAITakeItem of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAITakeItem00
static int tolua_AITask_ClientMob_addAITakeItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,11,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,12,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,13,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iprob = ((int)  tolua_tonumber(tolua_S,3,0));
  int itemid = ((int)  tolua_tonumber(tolua_S,4,0));
  int num = ((int)  tolua_tonumber(tolua_S,5,0));
  int idist = ((int)  tolua_tonumber(tolua_S,6,0));
  int containertype = ((int)  tolua_tonumber(tolua_S,7,0));
  int containerid = ((int)  tolua_tonumber(tolua_S,8,0));
  float speed = ((float)  tolua_tonumber(tolua_S,9,0));
  int takedist = ((int)  tolua_tonumber(tolua_S,10,0));
  int food = ((int)  tolua_tonumber(tolua_S,11,0));
  float foodreduce = ((float)  tolua_tonumber(tolua_S,12,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAITakeItem'", NULL);
#endif
  {
   self->addAiTask<AITakeItem>(iPriority,iprob,itemid,num,idist,containertype,containerid,speed,takedist,food,foodreduce);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAITakeItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAICraftItem of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAICraftItem00
static int tolua_AITask_ClientMob_addAICraftItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,8,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iprob = ((int)  tolua_tonumber(tolua_S,3,0));
  int craftid = ((int)  tolua_tonumber(tolua_S,4,0));
  float crafttime = ((float)  tolua_tonumber(tolua_S,5,0));
  int food = ((int)  tolua_tonumber(tolua_S,6,0));
  float foodreduce = ((float)  tolua_tonumber(tolua_S,7,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAICraftItem'", NULL);
#endif
  {
   self->addAiTask<AICraftItem>(iPriority,iprob,craftid,crafttime,food,foodreduce);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAICraftItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAINpcSleep of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAINpcSleep00
static int tolua_AITask_ClientMob_addAINpcSleep00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,9,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iprob = ((int)  tolua_tonumber(tolua_S,3,0));
  int time = ((int)  tolua_tonumber(tolua_S,4,0));
  float sleeptime = ((float)  tolua_tonumber(tolua_S,5,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,6,0));
  float speed = ((float)  tolua_tonumber(tolua_S,7,0));
  int dist = ((int)  tolua_tonumber(tolua_S,8,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAINpcSleep'", NULL);
#endif
  {
   self->addAiTask<AINpcSleep>(iPriority,iprob,time,sleeptime,blockid,speed,dist);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAINpcSleep'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIHunger of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIHunger00
static int tolua_AITask_ClientMob_addAIHunger00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,8,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iprob = ((int)  tolua_tonumber(tolua_S,3,0));
  int food = ((int)  tolua_tonumber(tolua_S,4,0));
  float time = ((float)  tolua_tonumber(tolua_S,5,0));
  int foodtype = ((int)  tolua_tonumber(tolua_S,6,0));
  int foodid = ((int)  tolua_tonumber(tolua_S,7,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIHunger'", NULL);
#endif
  {
   self->addAiTask<AIHunger>(iPriority,iprob,food,time,foodtype,foodid);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIHunger'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIEatFood of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIEatFood00
static int tolua_AITask_ClientMob_addAIEatFood00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,8,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iprob = ((int)  tolua_tonumber(tolua_S,3,0));
  int time = ((int)  tolua_tonumber(tolua_S,4,0));
  int food = ((int)  tolua_tonumber(tolua_S,5,0));
  int foodtype = ((int)  tolua_tonumber(tolua_S,6,0));
  int foodid = ((int)  tolua_tonumber(tolua_S,7,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIEatFood'", NULL);
#endif
  {
   self->addAiTask<AIEatFood>(iPriority,iprob,time,food,foodtype,foodid);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIEatFood'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIEatThenMutate of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIEatThenMutate00
static int tolua_AITask_ClientMob_addAIEatThenMutate00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,10,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,3,0));
  int prob = ((int)  tolua_tonumber(tolua_S,4,0));
  float speed = ((float)  tolua_tonumber(tolua_S,5,0));
  int eatTick = ((int)  tolua_tonumber(tolua_S,6,0));
  int dieTick = ((int)  tolua_tonumber(tolua_S,7,0));
  int buffid = ((int)  tolua_tonumber(tolua_S,8,0));
  int bufftick = ((int)  tolua_tonumber(tolua_S,9,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIEatThenMutate'", NULL);
#endif
  {
   self->addAiTask<AIEatThenMutate>(iPriority,blockid,prob,speed,eatTick,dieTick,buffid,bufftick);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIEatThenMutate'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIMutateFly of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIMutateFly00
static int tolua_AITask_ClientMob_addAIMutateFly00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,3,0));
  int prob = ((int)  tolua_tonumber(tolua_S,4,0));
  float speed = ((float)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIMutateFly'", NULL);
#endif
  {
   self->addAiTask<AIMutateFly>(iPriority,blockid,prob,speed);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIMutateFly'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIPatrolOnBlock of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIPatrolOnBlock00
static int tolua_AITask_ClientMob_addAIPatrolOnBlock00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,3,0));
  int Prob = ((int)  tolua_tonumber(tolua_S,4,0));
  float speed = ((float)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIPatrolOnBlock'", NULL);
#endif
  {
   self->addAiTask<AIPatrolOnBlock>(iPriority,blockid,Prob,speed);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIPatrolOnBlock'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIPlant of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIPlant00
static int tolua_AITask_ClientMob_addAIPlant00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,11,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int prob = ((int)  tolua_tonumber(tolua_S,3,0));
  int idist = ((int)  tolua_tonumber(tolua_S,4,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,5,0));
  int itemid = ((int)  tolua_tonumber(tolua_S,6,0));
  float speed = ((float)  tolua_tonumber(tolua_S,7,0));
  int plantdist = ((int)  tolua_tonumber(tolua_S,8,0));
  int food = ((int)  tolua_tonumber(tolua_S,9,0));
  float foodreduce = ((float)  tolua_tonumber(tolua_S,10,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIPlant'", NULL);
#endif
  {
	self->addAiTask<AIPlant>(iPriority,prob,idist,blockid,itemid,speed,plantdist,food,foodreduce);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIPlant'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAITargetScream of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAITargetScream00
static int tolua_AITask_ClientMob_addAITargetScream00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int soundtick = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAITargetScream'", NULL);
#endif
  {
   self->addAiTask<AITargetScream>(iPriority,soundtick);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAITargetScream'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAITargetPanicBuff of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAITargetPanicBuff00
static int tolua_AITask_ClientMob_addAITargetPanicBuff00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int buffid = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAITargetPanicBuff'", NULL);
#endif
  {
   self->addAiTask<AIPanicBuff>(iPriority,speed,buffid);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAITargetPanicBuff'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskGhostBombAttack of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskGhostBombAttack00
static int tolua_AITask_ClientMob_addAiTaskGhostBombAttack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,11,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float moveSpeed = ((float)  tolua_tonumber(tolua_S,3,0));
  int minAtkTime = ((int)  tolua_tonumber(tolua_S,4,0));
  int maxAtkTime = ((int)  tolua_tonumber(tolua_S,5,0));
  int atkrange = ((int)  tolua_tonumber(tolua_S,6,0));
  int projectileId = ((int)  tolua_tonumber(tolua_S,7,0));
  float power = ((float)  tolua_tonumber(tolua_S,8,0));
  int buffId = ((int)  tolua_tonumber(tolua_S,9,0));
  int count = ((int)  tolua_tonumber(tolua_S,10,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskGhostBombAttack'", NULL);
#endif
  {
   self->addAiTask<AIGhostBombAttack>(iPriority,moveSpeed,minAtkTime,maxAtkTime,atkrange,projectileId,power,buffId,count);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskGhostBombAttack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskGhostIceAttack of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskGhostIceAttack00
static int tolua_AITask_ClientMob_addAiTaskGhostIceAttack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,11,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float moveSpeed = ((float)  tolua_tonumber(tolua_S,3,0));
  int minAtkTime = ((int)  tolua_tonumber(tolua_S,4,0));
  int maxAtkTime = ((int)  tolua_tonumber(tolua_S,5,0));
  int atkrange = ((int)  tolua_tonumber(tolua_S,6,0));
  int projectileId = ((int)  tolua_tonumber(tolua_S,7,0));
  float power = ((float)  tolua_tonumber(tolua_S,8,0));
  int buffId = ((int)  tolua_tonumber(tolua_S,9,0));
  int count = ((int)  tolua_tonumber(tolua_S,10,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskGhostIceAttack'", NULL);
#endif
  {
   self->addAiTask<AIGhostIceAttack>(iPriority,moveSpeed,minAtkTime,maxAtkTime,atkrange,projectileId,power,buffId,count);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskGhostIceAttack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskGhostBumpAttack of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskGhostBumpAttack00
static int tolua_AITask_ClientMob_addAiTaskGhostBumpAttack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,11,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iDist = ((int)  tolua_tonumber(tolua_S,3,0));
  int dizzyTick = ((int)  tolua_tonumber(tolua_S,4,0));
  int preTick = ((int)  tolua_tonumber(tolua_S,5,0));
  int prob = ((int)  tolua_tonumber(tolua_S,6,0));
  float speed = ((float)  tolua_tonumber(tolua_S,7,0));
  float up = ((float)  tolua_tonumber(tolua_S,8,0));
  float back = ((float)  tolua_tonumber(tolua_S,9,0));
  int mode = ((int)  tolua_tonumber(tolua_S,10,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskGhostBumpAttack'", NULL);
#endif
  {
   self->addAiTask<AIGhostBumpAttack>(iPriority,iDist,dizzyTick,preTick,prob,speed,up,back,mode);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskGhostBumpAttack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskLavaGrab of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskLavaGrab00
static int tolua_AITask_ClientMob_addAiTaskLavaGrab00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskLavaGrab'", NULL);
#endif
  {
   self->addAiTask<AILavaCrab>(iPriority,speed);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskLavaGrab'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskOriole of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskOriole00
static int tolua_AITask_ClientMob_addAiTaskOriole00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskOriole'", NULL);
#endif
  {
   self->addAiTask<AIOriole>(iPriority,speed);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskOriole'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskChangeBlock of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskChangeBlock00
static int tolua_AITask_ClientMob_addAiTaskChangeBlock00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,9,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int attackBlockID = ((int)  tolua_tonumber(tolua_S,3,0));
  int toChangeBlockID = ((int)  tolua_tonumber(tolua_S,4,0));
  int triggerDis = ((int)  tolua_tonumber(tolua_S,5,0));
  float triggerProb = ((float)  tolua_tonumber(tolua_S,6,0));
  int runCurAICDMax = ((int)  tolua_tonumber(tolua_S,7,0));
  int runCurAICDMin = ((int)  tolua_tonumber(tolua_S,8,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskChangeBlock'", NULL);
#endif
  {
   self->addAiTask<AIChangeBlock>(iPriority,attackBlockID,toChangeBlockID,triggerDis,triggerProb,runCurAICDMax,runCurAICDMin);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskChangeBlock'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskStayFlower of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskStayFlower00
static int tolua_AITask_ClientMob_addAiTaskStayFlower00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,9,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int triggerDis = ((int)  tolua_tonumber(tolua_S,3,0));
  float triggerProb = ((float)  tolua_tonumber(tolua_S,4,0));
  int stayTimeMax = ((int)  tolua_tonumber(tolua_S,5,0));
  int stayTimeMin = ((int)  tolua_tonumber(tolua_S,6,0));
  int runCurAICDMax = ((int)  tolua_tonumber(tolua_S,7,0));
  int runCurAICDMin = ((int)  tolua_tonumber(tolua_S,8,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskStayFlower'", NULL);
#endif
  {
   self->addAiTask<AIFlyStayFlower>(iPriority,triggerDis,triggerProb,stayTimeMax,stayTimeMin,runCurAICDMax,runCurAICDMin);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskStayFlower'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIAttractBlock of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIAttractBlock00
static int tolua_AITask_ClientMob_addAIAttractBlock00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,3,0));
  int Prob = ((int)  tolua_tonumber(tolua_S,4,0));
  float speed = ((float)  tolua_tonumber(tolua_S,5,0));
  int dist = ((int)  tolua_tonumber(tolua_S,6,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIAttractBlock'", NULL);
#endif
  {
   self->addAiTask<AIAttractBlock>(iPriority,blockid,Prob,speed,dist);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIAttractBlock'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAISavageSleep of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAISavageSleep00
static int tolua_AITask_ClientMob_addAISavageSleep00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAISavageSleep'", NULL);
#endif
  {
   self->addAiTask<AISavageSleep>(iPriority);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAISavageSleep'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAISavageStandSleep of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAISavageStandSleep00
static int tolua_AITask_ClientMob_addAISavageStandSleep00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,8,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int minTick = ((int)  tolua_tonumber(tolua_S,3,50));
  int maxTick = ((int)  tolua_tonumber(tolua_S,4,100));
  float brightNess = ((float)  tolua_tonumber(tolua_S,5,0.5f));
  int startSleepHour = ((int)  tolua_tonumber(tolua_S,6,6));
  int endSleepHour = ((int)  tolua_tonumber(tolua_S,7,20));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAISavageStandSleep'", NULL);
#endif
  {
   self->addAiTask<AISavageStandSleep>(iPriority,minTick,maxTick,brightNess,startSleepHour,endSleepHour);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAISavageStandSleep'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIWarning of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIWarning00
static int tolua_AITask_ClientMob_addAIWarning00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iProb = ((int)  tolua_tonumber(tolua_S,3,0));
  int iSearchEnemyTicks = ((int)  tolua_tonumber(tolua_S,4,0));
  int iPanicTicks = ((int)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIWarning'", NULL);
#endif
  {
   self->addAiTask<AIWarning>(iPriority,iProb,iSearchEnemyTicks,iPanicTicks);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIWarning'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIThief of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIThief00
static int tolua_AITask_ClientMob_addAIThief00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int iProb = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIThief'", NULL);
#endif
  {
   self->addAiTask<AIThief>(iPriority,speed,iProb);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIThief'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIBananaFan of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIBananaFan00
static int tolua_AITask_ClientMob_addAIBananaFan00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int iProb = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIBananaFan'", NULL);
#endif
  {
   self->addAiTask<AIBananaFan>(iPriority,speed,iProb);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIBananaFan'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIBananaFanTamed of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIBananaFanTamed00
static int tolua_AITask_ClientMob_addAIBananaFanTamed00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int iProb = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIBananaFanTamed'", NULL);
#endif
  {
   self->addAiTask<AIBananaFanTamed>(iPriority,speed,iProb);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIBananaFanTamed'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIClimbTree of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIClimbTree00
static int tolua_AITask_ClientMob_addAIClimbTree00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int iProb = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIClimbTree'", NULL);
#endif
  {
   self->addAiTask<AIClimbTree>(iPriority,speed,iProb);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIClimbTree'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAILeopardAtk of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAILeopardAtk00
static int tolua_AITask_ClientMob_addAILeopardAtk00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,10,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  bool trace = ((bool)  tolua_toboolean(tolua_S,3,0));
  int iProb = ((int)  tolua_tonumber(tolua_S,4,0));
  int iDistance = ((int)  tolua_tonumber(tolua_S,5,0));
  int iBuffId = ((int)  tolua_tonumber(tolua_S,6,0));
  float speed = ((float)  tolua_tonumber(tolua_S,7,0));
  float runSpeed = ((float)  tolua_tonumber(tolua_S,8,0));
  float jumpSpeed = ((float)  tolua_tonumber(tolua_S,9,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAILeopardAtk'", NULL);
#endif
  {
   self->addAiTask<AILeopardAtk>(iPriority,trace,iProb,iDistance,iBuffId,speed,runSpeed,jumpSpeed);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAILeopardAtk'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIEvade of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIEvade00
static int tolua_AITask_ClientMob_addAIEvade00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float evadeSpeed = ((float)  tolua_tonumber(tolua_S,3,0));
  int iColdDown = ((int)  tolua_tonumber(tolua_S,4,0));
  int iDistance = ((int)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIEvade'", NULL);
#endif
  {
   self->addAiTask<AIEvade>(iPriority,evadeSpeed,iColdDown,iDistance);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIEvade'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIPosWander of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIPosWander00
static int tolua_AITask_ClientMob_addAIPosWander00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,8,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int x = ((int)  tolua_tonumber(tolua_S,3,0));
  int z = ((int)  tolua_tonumber(tolua_S,4,0));
  int width = ((int)  tolua_tonumber(tolua_S,5,0));
  float speed = ((float)  tolua_tonumber(tolua_S,6,0));
  int prob = ((int)  tolua_tonumber(tolua_S,7,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIPosWander'", NULL);
#endif
  {
   self->addAiTask<AIPosWander>(iPriority,x,z,width,speed,prob);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIPosWander'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIHungryStatus of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIHungryStatus00
static int tolua_AITask_ClientMob_addAIHungryStatus00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float fFoodLmt = ((float)  tolua_tonumber(tolua_S,3,0));
  float fFoodLmtMax = ((float)  tolua_tonumber(tolua_S,4,0));
  int nSeq = ((int)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIHungryStatus'", NULL);
#endif
  {
   self->addAiTask<AIHungryStatus>(iPriority,fFoodLmt,fFoodLmtMax,nSeq);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIHungryStatus'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIHungryAtkTarget of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIHungryAtkTarget00
static int tolua_AITask_ClientMob_addAIHungryAtkTarget00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iAtkType = ((int)  tolua_tonumber(tolua_S,3,0));
  int iChance = ((int)  tolua_tonumber(tolua_S,4,0));
  int nSearchDist = ((int)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIHungryAtkTarget'", NULL);
#endif
  {
   self->addAiTask<AIHungryAtkTarget>(iPriority,iAtkType,iChance,nSearchDist);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIHungryAtkTarget'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIHungryFollowPlayer of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIHungryFollowPlayer00
static int tolua_AITask_ClientMob_addAIHungryFollowPlayer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,8,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int prob = ((int)  tolua_tonumber(tolua_S,3,0));
  bool iCheckSight = ((bool)  tolua_toboolean(tolua_S,4,0));
  int speed = ((int)  tolua_tonumber(tolua_S,5,0));
  int nSearchDist = ((int)  tolua_tonumber(tolua_S,6,0));
  int nPerformAtkTick = ((int)  tolua_tonumber(tolua_S,7,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIHungryFollowPlayer'", NULL);
#endif
  {
   self->addAiTask<AIHungryFollowPlayer>(iPriority,prob,iCheckSight,speed,nSearchDist,nPerformAtkTick);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIHungryFollowPlayer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIHomeRankWander of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIHomeRankWander00
static int tolua_AITask_ClientMob_addAIHomeRankWander00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int prob = ((int)  tolua_tonumber(tolua_S,4,120));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAIHomeRankWander'", NULL);
#endif
  {
   self->addAiTask<AIRanchWander>(iPriority,speed,prob);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAIHomeRankWander'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskFlyAttract of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskFlyAttract00
static int tolua_AITask_ClientMob_addAiTaskFlyAttract00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,8,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int iDist = ((int)  tolua_tonumber(tolua_S,3,0));
  int iFollowDist = ((int)  tolua_tonumber(tolua_S,4,0));
  int iTimeOut = ((int)  tolua_tonumber(tolua_S,5,0));
  int followCnt = ((int)  tolua_tonumber(tolua_S,6,3));
  int height = ((int)  tolua_tonumber(tolua_S,7,1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskFlyAttract'", NULL);
#endif
  {
   self->addAiTask<AIFlyAttract>(iPriority,iDist,iFollowDist,iTimeOut,followCnt,height);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskFlyAttract'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskEarthCoreManLash of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskEarthCoreManLash00
static int tolua_AITask_ClientMob_addAiTaskEarthCoreManLash00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int flyLashMinDistance = ((int)  tolua_tonumber(tolua_S,3,200));
  int flyLashMaxDistance = ((int)  tolua_tonumber(tolua_S,4,1000));
  int flyLashProb = ((int)  tolua_tonumber(tolua_S,5,3));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskEarthCoreManLash'", NULL);
#endif
  {
   self->addAiTask<AIEarthCoreManLash>(iPriority,flyLashMinDistance,flyLashMaxDistance,flyLashProb);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskEarthCoreManLash'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskEarthCoreManRain of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskEarthCoreManRain00
static int tolua_AITask_ClientMob_addAiTaskEarthCoreManRain00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int seekRange = ((int)  tolua_tonumber(tolua_S,3,8));
  int interruptRange = ((int)  tolua_tonumber(tolua_S,4,16));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskEarthCoreManRain'", NULL);
#endif
  {
   self->addAiTask<AIEarthCoreManRain>(iPriority,seekRange,interruptRange);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskEarthCoreManRain'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskEarthCoreManSteal of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskEarthCoreManSteal00
static int tolua_AITask_ClientMob_addAiTaskEarthCoreManSteal00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,1,&tolua_err) ||
     !tolua_isboolean(tolua_S,9,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,10,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  int stealCDInterval = ((int)  tolua_tonumber(tolua_S,3,100));
  int startStealDistance = ((int)  tolua_tonumber(tolua_S,4,400));
  int stealRange = ((int)  tolua_tonumber(tolua_S,5,1000));
  int stealPerNum = ((int)  tolua_tonumber(tolua_S,6,5));
  int stealPerInterval = ((int)  tolua_tonumber(tolua_S,7,3));
  float brightness = ((float)  tolua_tonumber(tolua_S,8,1.0));
  bool needSunlight = ((bool)  tolua_toboolean(tolua_S,9,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskEarthCoreManSteal'", NULL);
#endif
  {
   self->addAiTask<AIEarthCoreManSteal>(iPriority,stealCDInterval,startStealDistance,stealRange,stealPerNum,stealPerInterval,brightness,needSunlight);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskEarthCoreManSteal'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiTaskFlyFollow of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiTaskFlyFollow00
static int tolua_AITask_ClientMob_addAiTaskFlyFollow00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int iMinDist = ((int)  tolua_tonumber(tolua_S,4,0));
  int iMaxDist = ((int)  tolua_tonumber(tolua_S,5,0));
  int iTeleport = ((int)  tolua_tonumber(tolua_S,6,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiTaskFlyFollow'", NULL);
#endif
  {
   self->addAiTask<AIFlyFollow>(iPriority,speed,iMinDist,iMaxDist,iTeleport);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiTaskFlyFollow'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAiStayBlock of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiStayBlock00
static int tolua_AITask_ClientMob_addAiStayBlock00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientMob",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientMob* self = (ClientMob*)  tolua_tousertype(tolua_S,1,0);
  int iPriority = ((int)  tolua_tonumber(tolua_S,2,0));
  float speed = ((float)  tolua_tonumber(tolua_S,3,0));
  int stayDist = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAiStayBlock'", NULL);
#endif
  {
   self->addAiTask<AIStayBlock>(iPriority,speed,stayDist);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAiStayBlock'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIBatAttack of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIBatAttack00
static int tolua_AITask_ClientMob_addAIBatAttack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
	tolua_Error tolua_err;
	if (
		!tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 5, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 6, 0, &tolua_err) ||
		!tolua_isnoobj(tolua_S, 7, &tolua_err)
		)
		goto tolua_lerror;
	else
#endif
	{
		ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
		int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
		float speed = ((float)tolua_tonumber(tolua_S, 3, 0));
		int buffid = ((int)tolua_tonumber(tolua_S, 4, 0));
		int bufflv = ((int)tolua_tonumber(tolua_S, 5, 0));
        int buffper = ((int)tolua_tonumber(tolua_S, 6, 0));
#ifndef TOLUA_RELEASE
		if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAIBatAttack00'", NULL);
#endif
		{
			self->addAiTask<AIBatAttack>(iPriority, speed, buffid, bufflv, buffper);
		}
	}
	return 0;
#ifndef TOLUA_RELEASE
	tolua_lerror :
	tolua_error(tolua_S, "#ferror in function 'addAIBatAttack00'.", &tolua_err);
	return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIBatIdle of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIBatIdle00
static int tolua_AITask_ClientMob_addAIBatIdle00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
	tolua_Error tolua_err;
	if (
		!tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
		!tolua_isnoobj(tolua_S, 4, &tolua_err)
		)
		goto tolua_lerror;
	else
#endif
	{
		ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
		int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
		int maxLight = ((int)tolua_tonumber(tolua_S, 3, 0));
#ifndef TOLUA_RELEASE
		if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAIBatIdle00'", NULL);
#endif
		{
			self->addAiTask<AIBatIdle>(iPriority, maxLight);
		}
	}
	return 0;
#ifndef TOLUA_RELEASE
	tolua_lerror :
	tolua_error(tolua_S, "#ferror in function 'addAIBatIdle00'.", &tolua_err);
	return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE


/* method: addAIFishSwarm of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIFishSwarm00
static int tolua_AITask_ClientMob_addAIFishSwarm00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
    tolua_Error tolua_err;
    if (
        !tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
        //!tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
        !tolua_isnoobj(tolua_S, 5, &tolua_err)
        )
        goto tolua_lerror;
    else
#endif
    {
        ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
        int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
        float interval = ((float)tolua_tonumber(tolua_S, 3, 0));
        //int stayDist = ((int)tolua_tonumber(tolua_S, 4, 0));
#ifndef TOLUA_RELEASE
        if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAIFishSwarm'", NULL);
#endif
        {
            self->addAiTask<AIFishSwarm>(iPriority, interval);
        }
    }
    return 0;
#ifndef TOLUA_RELEASE
    tolua_lerror :
    tolua_error(tolua_S, "#ferror in function 'addAIFishSwarm'.", &tolua_err);
    return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE


/* method: addAIFishSwimToSurface of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIFishSwimToSurface00
static int tolua_AITask_ClientMob_addAIFishSwimToSurface00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
    tolua_Error tolua_err;
    if (
        !tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
        //!tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
        !tolua_isnoobj(tolua_S, 5, &tolua_err)
        )
        goto tolua_lerror;
    else
#endif
    {
        ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
        int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
        float interval = ((float)tolua_tonumber(tolua_S, 3, 0));
        //int stayDist = ((int)tolua_tonumber(tolua_S, 4, 0));
#ifndef TOLUA_RELEASE
        if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAIFishSwimToSurface'", NULL);
#endif
        {
            self->addAiTask<AIFishSwimToSurface>(iPriority, interval);
        }
    }
    return 0;
#ifndef TOLUA_RELEASE
    tolua_lerror :
    tolua_error(tolua_S, "#ferror in function 'addAIFishSwimToSurface'.", &tolua_err);
    return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIFishHoveringAround of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIFishHoveringAround00
static int tolua_AITask_ClientMob_addAIFishHoveringAround00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
    tolua_Error tolua_err;
    if (
        !tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
        //!tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
        !tolua_isnoobj(tolua_S, 5, &tolua_err)
        )
        goto tolua_lerror;
    else
#endif
    {
        ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
        int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
        float interval = ((float)tolua_tonumber(tolua_S, 3, 0));
        //int stayDist = ((int)tolua_tonumber(tolua_S, 4, 0));
#ifndef TOLUA_RELEASE
        if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAIFishHoveringAround'", NULL);
#endif
        {
            self->addAiTask<AIFishHoveringAround>(iPriority, interval);
        }
    }
    return 0;
#ifndef TOLUA_RELEASE
    tolua_lerror :
    tolua_error(tolua_S, "#ferror in function 'addAIFishHoveringAround'.", &tolua_err);
    return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIFishFly of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIFishFly00
static int tolua_AITask_ClientMob_addAIFishFly00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
    tolua_Error tolua_err;
    if (
        !tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 5, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 6, 0, &tolua_err) ||
        !tolua_isnoobj(tolua_S, 7, &tolua_err)
        )
        goto tolua_lerror;
    else
#endif
    {
        ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
        int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
        float interval = ((float)tolua_tonumber(tolua_S, 3, 0));
        float height = ((float)tolua_tonumber(tolua_S, 4, 0));
        float upSpeed = ((float)tolua_tonumber(tolua_S, 5, 0));
        float downSpeed = ((float)tolua_tonumber(tolua_S, 6, 0));
#ifndef TOLUA_RELEASE
        if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAIFishFly'", NULL);
#endif
        {
            self->addAiTask<AIFishFly>(iPriority, interval, height, upSpeed, downSpeed);
        }
    }
    return 0;
#ifndef TOLUA_RELEASE
    tolua_lerror :
    tolua_error(tolua_S, "#ferror in function 'addAIFishFly'.", &tolua_err);
    return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAIBirdFishing of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAIBirdFishing00
static int tolua_AITask_ClientMob_addAIBirdFishing00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
    tolua_Error tolua_err;
    if (
        !tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 3, 1, &tolua_err) ||
        !tolua_isnumber(tolua_S, 4, 1, &tolua_err) ||
        !tolua_isnoobj(tolua_S, 5, &tolua_err)
        )
        goto tolua_lerror;
    else
#endif
    {
        ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
        int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
        float interval = ((float)tolua_tonumber(tolua_S, 3, 1.4f));
        int flySpeed = ((int)tolua_tonumber(tolua_S, 4, 3));
#ifndef TOLUA_RELEASE
        if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAIBirdFishing'", NULL);
#endif
        {
            self->addAiTask<AIBirdFishing>(iPriority, interval, flySpeed);
        }
    }
    return 0;
#ifndef TOLUA_RELEASE
    tolua_lerror :
    tolua_error(tolua_S, "#ferror in function 'addAIBirdFishing'.", &tolua_err);
    return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

static int tolua_AITask_ClientMob_addAIWanderAmphibious(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
    tolua_Error tolua_err;
    if (
        !tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
        !tolua_isnoobj(tolua_S, 5, &tolua_err)
        )
        goto tolua_lerror;
    else
#endif
    {
        ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
        int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
        float speed = ((float)tolua_tonumber(tolua_S, 3, 0));
        int prob = ((int)tolua_tonumber(tolua_S, 4, 0));
#ifndef TOLUA_RELEASE
        if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAIWanderAmphibious'", NULL);
#endif
        {
            self->addAiTask<AIWanderAmphibious>(iPriority, speed, prob);
        }
    }
    return 0;
#ifndef TOLUA_RELEASE
    tolua_lerror :
    tolua_error(tolua_S, "#ferror in function 'addAIWanderAmphibious'.", &tolua_err);
    return 0;
#endif
}

static int tolua_AITask_ClientMob_addAIJumpGlissadeAmphibious(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
    tolua_Error tolua_err;
    if (
        !tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 5, 0, &tolua_err) ||
        !tolua_isnoobj(tolua_S, 6, &tolua_err)
        )
        goto tolua_lerror;
    else
#endif
    {
        ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
        int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
        int frequency = ((int)tolua_tonumber(tolua_S, 3, 0));
        int probJump = ((int)tolua_tonumber(tolua_S, 4, 0));
        int probGlissade = ((int)tolua_tonumber(tolua_S, 5, 0));
#ifndef TOLUA_RELEASE
        if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAIJumpGlissadeAmphibious'", NULL);
#endif
        {
            self->addAiTask<AIJumpGlissadeAmphibious>(iPriority, frequency, probJump, probGlissade);
        }
    }
    return 0;
#ifndef TOLUA_RELEASE
    tolua_lerror :
    tolua_error(tolua_S, "#ferror in function 'addAIJumpGlissadeAmphibious'.", &tolua_err);
    return 0;
#endif
}

static int tolua_AITask_ClientMob_addAiThrob00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
	tolua_Error tolua_err;
	if (
		!tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
        !tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
		!tolua_isnoobj(tolua_S, 5, &tolua_err)
		)
		goto tolua_lerror;
	else
#endif
	{
		ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
		int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
		int prob = ((int)tolua_tonumber(tolua_S, 3, 0));
        float aniTime = ((float)tolua_tonumber(tolua_S, 4, 0));
#ifndef TOLUA_RELEASE
		if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAiThrob'", NULL);
#endif
		{
			self->addAiTask<AIThrob>(iPriority, prob, aniTime);
		}
	}
	return 0;
#ifndef TOLUA_RELEASE
	tolua_lerror :
	tolua_error(tolua_S, "#ferror in function addAiThrob'.", &tolua_err);
	return 0;
#endif
}

static int tolua_AITask_ClientMob_addAiClean00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
	tolua_Error tolua_err;
	if (
		!tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
		!tolua_isnoobj(tolua_S, 5, &tolua_err)
		)
		goto tolua_lerror;
	else
#endif
	{
		ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
		int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
		int prob = ((int)tolua_tonumber(tolua_S, 3, 0));
		float aniTime = ((float)tolua_tonumber(tolua_S, 4, 0));
#ifndef TOLUA_RELEASE
		if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAiClean'", NULL);
#endif
		{
			self->addAiTask<AIClean>(iPriority, prob, aniTime);
		}
	}
	return 0;
#ifndef TOLUA_RELEASE
	tolua_lerror :
	tolua_error(tolua_S, "#ferror in function addAiClean'.", &tolua_err);
	return 0;
#endif
}

static int tolua_AITask_ClientMob_addAiFierce00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
	tolua_Error tolua_err;
	if (
		!tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 3, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 4, 0, &tolua_err) ||
		!tolua_isnoobj(tolua_S, 5, &tolua_err)
		)
		goto tolua_lerror;
	else
#endif
	{
		ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
		int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
		int prob = ((int)tolua_tonumber(tolua_S, 3, 0));
		float aniTime = ((float)tolua_tonumber(tolua_S, 4, 0));
#ifndef TOLUA_RELEASE
		if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAiFierce'", NULL);
#endif
		{
			self->addAiTask<AIFierce>(iPriority, prob, aniTime);
		}
	}
	return 0;
#ifndef TOLUA_RELEASE
	tolua_lerror :
	tolua_error(tolua_S, "#ferror in function addAiFierce'.", &tolua_err);
	return 0;
#endif
}

/* method: addAiMate of class  ClientMob */
#ifndef TOLUA_DISABLE_tolua_AITask_ClientMob_addAiMate00
static int tolua_AITask_ClientMob_addAICreateVacantVortex00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
	tolua_Error tolua_err;
	if (
		!tolua_isusertype(tolua_S, 1, "ClientMob", 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 2, 0, &tolua_err) ||
		!tolua_isnumber(tolua_S, 3, 0, &tolua_err)
		)
		goto tolua_lerror;
	else
#endif
	{
		ClientMob* self = (ClientMob*)tolua_tousertype(tolua_S, 1, 0);
		int iPriority = ((int)tolua_tonumber(tolua_S, 2, 0));
		int prob = ((int)tolua_tonumber(tolua_S, 3, 10));
#ifndef TOLUA_RELEASE
		if (!self) tolua_error(tolua_S, "invalid 'self' in function 'addAICreateVacantVortex'", NULL);
#endif
		{
			self->addAiTask<AICreateVacantVortex>(iPriority, prob);
		}
	}
	return 0;
#ifndef TOLUA_RELEASE
	tolua_lerror :
	tolua_error(tolua_S, "#ferror in function 'addAICreateVacantVortex'.", &tolua_err);
	return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* Open function */
TOLUA_API int tolua_AITask_open_manual (lua_State* tolua_S)
{
 tolua_open(tolua_S);
 tolua_reg_types(tolua_S);
 tolua_module(tolua_S,NULL,0);
 tolua_beginmodule(tolua_S,NULL);
  tolua_cclass(tolua_S,"ClientMob","ClientMob","ActorLiving",NULL);
  tolua_beginmodule(tolua_S,"ClientMob");
   tolua_function(tolua_S,"addAiTaskSwimming",tolua_AITask_ClientMob_addAiTaskSwimming00);
   tolua_function(tolua_S,"addAiTaskRandSwim",tolua_AITask_ClientMob_addAiTaskRandSwim00);
   tolua_function(tolua_S,"addAiTaskFearPlayer",tolua_AITask_ClientMob_addAiTaskFearPlayer00);
   tolua_function(tolua_S,"addAiTaskFishBeg",tolua_AITask_ClientMob_addAiTaskFishBeg00);
   tolua_function(tolua_S,"addAiTaskFishAttack",tolua_AITask_ClientMob_addAiTaskFishAttack00);
   tolua_function(tolua_S,"addAiTaskTransfiguration",tolua_AITask_ClientMob_addAiTaskTransfiguration00);
   tolua_function(tolua_S,"addAiTaskSit",tolua_AITask_ClientMob_addAiTaskSit00);
   tolua_function(tolua_S,"addAiTaskAtk",tolua_AITask_ClientMob_addAiTaskAtk00);
   tolua_function(tolua_S,"addAiTaskAtk_TianGou",tolua_AITask_ClientMob_addAiTaskAtk_TianGou00);
   tolua_function(tolua_S,"addAiTaskArrowAttack",tolua_AITask_ClientMob_addAiTaskArrowAttack00);
   tolua_function(tolua_S,"addAiTaskProjectileAttack",tolua_AITask_ClientMob_addAiTaskProjectileAttack00);
   tolua_function(tolua_S,"addAiTaskPetFollowOwner",tolua_AITask_ClientMob_addAiTaskPetFollowOwner00);
   tolua_function(tolua_S,"addAiTaskFollowOwner",tolua_AITask_ClientMob_addAiTaskFollowOwner00);
   tolua_function(tolua_S,"addAiTaskWander",tolua_AITask_ClientMob_addAiTaskWander00);
   tolua_function(tolua_S,"addAiTaskPetWander",tolua_AITask_ClientMob_addAiTaskPetWander00);
   tolua_function(tolua_S,"addAiTaskBeg",tolua_AITask_ClientMob_addAiTaskBeg00);
   tolua_function(tolua_S,"addAiTaskBegEx",tolua_AITask_ClientMob_addAiTaskBegEx00);
   tolua_function(tolua_S,"addAiTaskWatchClosest",tolua_AITask_ClientMob_addAiTaskWatchClosest00);
   tolua_function(tolua_S,"addAiTaskLookIdle",tolua_AITask_ClientMob_addAiTaskLookIdle00);
   tolua_function(tolua_S,"addAiTaskTargetOnwnerHurtee",tolua_AITask_ClientMob_addAiTaskTargetOnwnerHurtee00);
   tolua_function(tolua_S,"addAiTaskTargetOnwnerHurter",tolua_AITask_ClientMob_addAiTaskTargetOnwnerHurter00);
   tolua_function(tolua_S,"addAiTaskTargetHurtee",tolua_AITask_ClientMob_addAiTaskTargetHurtee00);
   tolua_function(tolua_S,"addAiTaskTargetNonTamed",tolua_AITask_ClientMob_addAiTaskTargetNonTamed00);
   tolua_function(tolua_S,"addAiTaskTargetSpecificEntity",tolua_AITask_ClientMob_addAiTaskTargetSpecificEntity00);
   tolua_function(tolua_S,"addAiTaskRestrictSun",tolua_AITask_ClientMob_addAiTaskRestrictSun00);
   tolua_function(tolua_S,"addAiTaskFleeSun",tolua_AITask_ClientMob_addAiTaskFleeSun00);
   tolua_function(tolua_S,"addAiTaskTargetNearest",tolua_AITask_ClientMob_addAiTaskTargetNearest00);
   tolua_function(tolua_S,"addAiTaskBreakDoor",tolua_AITask_ClientMob_addAiTaskBreakDoor00);
   tolua_function(tolua_S,"addAiTaskMoveTowardsRestriction",tolua_AITask_ClientMob_addAiTaskMoveTowardsRestriction00);
   tolua_function(tolua_S,"addAiTaskPanic",tolua_AITask_ClientMob_addAiTaskPanic00);
   tolua_function(tolua_S,"addAiLeapAtTarget", tolua_AITask_ClientMob_addAiLeapAtTarget00);
   tolua_function(tolua_S,"addAiTaskFlyPanic",tolua_AITask_ClientMob_addAiTaskFlyPanic00);
   tolua_function(tolua_S,"addAiMate",tolua_AITask_ClientMob_addAiMate00);
   tolua_function(tolua_S,"addAiTaskFollowParent",tolua_AITask_ClientMob_addAiTaskFollowParent00);
   tolua_function(tolua_S,"addAiTaskBoomAtk",tolua_AITask_ClientMob_addAiTaskBoomAtk00);
   tolua_function(tolua_S,"addAiTaskEatGrass",tolua_AITask_ClientMob_addAiTaskEatGrass00);
   tolua_function(tolua_S,"addAiTaskFollowDirection",tolua_AITask_ClientMob_addAiTaskFollowDirection00);
   tolua_function(tolua_S,"addAiTaskClosestDance",tolua_AITask_ClientMob_addAiTaskClosestDance00);
   tolua_function(tolua_S,"addAiTargetMutate",tolua_AITask_ClientMob_addAiTargetMutate00);
   tolua_function(tolua_S,"addAITaskToppleOver",tolua_AITask_ClientMob_addAITaskToppleOver00);
   tolua_function(tolua_S,"addAITaskSitbyItem",tolua_AITask_ClientMob_addAITaskSitbyItem00);
   tolua_function(tolua_S,"addAiTaskEatLeaf",tolua_AITask_ClientMob_addAiTaskEatLeaf00);
   tolua_function(tolua_S,"addAITaskEatFlower",tolua_AITask_ClientMob_addAITaskEatFlower00);
   tolua_function(tolua_S,"addAiTaskRide",tolua_AITask_ClientMob_addAiTaskRide00);
   tolua_function(tolua_S,"addAiTaskKickAway",tolua_AITask_ClientMob_addAiTaskKickAway00);
   tolua_function(tolua_S,"addAiTaskMakeTrouble",tolua_AITask_ClientMob_addAiTaskMakeTrouble00);
   tolua_function(tolua_S,"addAIGetSpecialAttackattr",tolua_AITask_ClientMob_addAIGetSpecialAttackattr00);
   tolua_function(tolua_S,"addAISitBlock",tolua_AITask_ClientMob_addAISitBlock00);
   tolua_function(tolua_S,"addAILoveBlock",tolua_AITask_ClientMob_addAILoveBlock00);
   tolua_function(tolua_S,"addAITargetFollowingPlayer",tolua_AITask_ClientMob_addAITargetFollowingPlayer00);
   tolua_function(tolua_S,"addAIHoldMonster",tolua_AITask_ClientMob_addAIHoldMonster00);
   tolua_function(tolua_S,"addAICeilingAtk",tolua_AITask_ClientMob_addAICeilingAtk00);
   tolua_function(tolua_S,"addAIGoCeiling",tolua_AITask_ClientMob_addAIGoCeiling00);
   tolua_function(tolua_S,"addAiTaskSleep",tolua_AITask_ClientMob_addAiTaskSleep00);
   tolua_function(tolua_S,"addAiLoggerHeads",tolua_AITask_ClientMob_addAiLoggerHeads00);
   tolua_function(tolua_S,"addAiTaskLayEggs",tolua_AITask_ClientMob_addAiTaskLayEggs00);
   tolua_function(tolua_S,"addAiTaskLayEggInNest",tolua_AITask_ClientMob_addAiTaskLayEggInNest00);
   tolua_function(tolua_S,"addAiTaskHatch",tolua_AITask_ClientMob_addAiTaskHatch00);
   tolua_function(tolua_S,"addAiTaskEatFeedBlock",tolua_AITask_ClientMob_addAiTaskEatFeedBlock00);
   tolua_function(tolua_S,"addAiTaskMilking",tolua_AITask_ClientMob_addAiTaskMilking00);
   tolua_function(tolua_S,"addAiTaskPetPlayToPlayer",tolua_AITask_ClientMob_addAiTaskPetPlayToPlayer00);
   tolua_function(tolua_S,"addAiTaskPetDanceToPlayer",tolua_AITask_ClientMob_addAiTaskPetDanceToPlayer00);
   tolua_function(tolua_S,"addAITaskSpecialAct",tolua_AITask_ClientMob_addAITaskSpecialAct00);
   tolua_function(tolua_S,"addAITaskHungry",tolua_AITask_ClientMob_addAITaskHungry00);
   tolua_function(tolua_S,"addAiTaskFlyAttack",tolua_AITask_ClientMob_addAiTaskFlyAttack00);
   tolua_function(tolua_S,"addAiTaskRandFly",tolua_AITask_ClientMob_addAiTaskRandFly00);
   tolua_function(tolua_S,"addAiTaskFlyBeg",tolua_AITask_ClientMob_addAiTaskFlyBeg00);
   tolua_function(tolua_S,"addAiTaskFlyLoveBlock",tolua_AITask_ClientMob_addAiTaskFlyLoveBlock00);
   tolua_function(tolua_S,"addAiTaskFlyPointToPoint",tolua_AITask_ClientMob_addAiTaskFlyPointToPoint00);
   tolua_function(tolua_S,"addAITaskFearItem",tolua_AITask_ClientMob_addAITaskFearItem00);
   tolua_function(tolua_S,"addAITaskDissolvedByItem",tolua_AITask_ClientMob_addAITaskDissolvedByItem00);
   tolua_function(tolua_S,"addAiTaskItemPanic",tolua_AITask_ClientMob_addAiTaskItemPanic00);
   tolua_function(tolua_S,"addAiTaskPlayerPanic",tolua_AITask_ClientMob_addAiTaskPlayerPanic00);
   tolua_function(tolua_S,"addAiTaskSeparate",tolua_AITask_ClientMob_addAiTaskSeparate00);
   tolua_function(tolua_S,"addAiTaskSeparatePanic",tolua_AITask_ClientMob_addAiTaskSeparatePanic00);
   tolua_function(tolua_S,"addAiTaskCombine",tolua_AITask_ClientMob_addAiTaskCombine00);
   tolua_function(tolua_S,"addAiTaskWizardFly",tolua_AITask_ClientMob_addAiTaskWizardFly00);
   tolua_function(tolua_S,"addAiTaskIceWizardFly",tolua_AITask_ClientMob_addAiTaskIceWizardFly00);
   tolua_function(tolua_S,"addAiTaskWizardAttack",tolua_AITask_ClientMob_addAiTaskWizardAttack00);
   tolua_function(tolua_S,"addAiTaskWizardProjectileAttack",tolua_AITask_ClientMob_addAiTaskWizardProjectileAttack00);
   tolua_function(tolua_S,"addAiTaskIceWizardFindActor", tolua_AITask_ClientMob_addAiTaskIceWizardFindActor00);
   tolua_function(tolua_S,"addAiTaskIceWizardProjectileAttack",tolua_AITask_ClientMob_addAiTaskIceWizardProjectileAttack00);
   tolua_function(tolua_S,"addAITaskBumpAttack",tolua_AITask_ClientMob_addAITaskBumpAttack00);
   tolua_function(tolua_S,"addAIDigBlock",tolua_AITask_ClientMob_addAIDigBlock00);
   tolua_function(tolua_S,"addAIPickupItem",tolua_AITask_ClientMob_addAIPickupItem00);
   tolua_function(tolua_S,"addAIPickupItemEx",tolua_AITask_ClientMob_addAIPickupItemEx00);
   tolua_function(tolua_S,"addAIStoreItem",tolua_AITask_ClientMob_addAIStoreItem00);
   tolua_function(tolua_S,"addAITakeItem",tolua_AITask_ClientMob_addAITakeItem00);
   tolua_function(tolua_S,"addAICraftItem",tolua_AITask_ClientMob_addAICraftItem00);
   tolua_function(tolua_S,"addAINpcSleep",tolua_AITask_ClientMob_addAINpcSleep00);
   tolua_function(tolua_S,"addAIHunger",tolua_AITask_ClientMob_addAIHunger00);
   tolua_function(tolua_S,"addAIEatFood",tolua_AITask_ClientMob_addAIEatFood00);
   tolua_function(tolua_S,"addAIEatThenMutate",tolua_AITask_ClientMob_addAIEatThenMutate00);
   tolua_function(tolua_S,"addAIMutateFly",tolua_AITask_ClientMob_addAIMutateFly00);
   tolua_function(tolua_S,"addAIPatrolOnBlock",tolua_AITask_ClientMob_addAIPatrolOnBlock00);
   tolua_function(tolua_S,"addAIPlant",tolua_AITask_ClientMob_addAIPlant00);
   tolua_function(tolua_S,"addAITargetScream",tolua_AITask_ClientMob_addAITargetScream00);
   tolua_function(tolua_S,"addAITargetPanicBuff",tolua_AITask_ClientMob_addAITargetPanicBuff00);
   tolua_function(tolua_S,"addAiTaskGhostBombAttack",tolua_AITask_ClientMob_addAiTaskGhostBombAttack00);
   tolua_function(tolua_S,"addAiTaskGhostIceAttack",tolua_AITask_ClientMob_addAiTaskGhostIceAttack00);
   tolua_function(tolua_S,"addAiTaskGhostBumpAttack",tolua_AITask_ClientMob_addAiTaskGhostBumpAttack00);
   tolua_function(tolua_S,"addAiTaskLavaGrab",tolua_AITask_ClientMob_addAiTaskLavaGrab00);
   tolua_function(tolua_S,"addAiTaskOriole",tolua_AITask_ClientMob_addAiTaskOriole00);
   tolua_function(tolua_S,"addAiTaskChangeBlock",tolua_AITask_ClientMob_addAiTaskChangeBlock00);
   tolua_function(tolua_S,"addAiTaskStayFlower",tolua_AITask_ClientMob_addAiTaskStayFlower00);
   tolua_function(tolua_S,"addAIAttractBlock",tolua_AITask_ClientMob_addAIAttractBlock00);
   tolua_function(tolua_S,"addAISavageSleep",tolua_AITask_ClientMob_addAISavageSleep00);
   tolua_function(tolua_S,"addAISavageStandSleep",tolua_AITask_ClientMob_addAISavageStandSleep00);
   tolua_function(tolua_S,"addAIWarning",tolua_AITask_ClientMob_addAIWarning00);
   tolua_function(tolua_S,"addAIThief",tolua_AITask_ClientMob_addAIThief00);
   tolua_function(tolua_S,"addAIBananaFan",tolua_AITask_ClientMob_addAIBananaFan00);
   tolua_function(tolua_S,"addAIBananaFanTamed",tolua_AITask_ClientMob_addAIBananaFanTamed00);
   tolua_function(tolua_S,"addAIClimbTree",tolua_AITask_ClientMob_addAIClimbTree00);
   tolua_function(tolua_S,"addAILeopardAtk",tolua_AITask_ClientMob_addAILeopardAtk00);
   tolua_function(tolua_S,"addAIEvade",tolua_AITask_ClientMob_addAIEvade00);
   tolua_function(tolua_S,"addAIPosWander",tolua_AITask_ClientMob_addAIPosWander00);
   tolua_function(tolua_S,"addAIHungryStatus",tolua_AITask_ClientMob_addAIHungryStatus00);
   tolua_function(tolua_S,"addAIHungryAtkTarget",tolua_AITask_ClientMob_addAIHungryAtkTarget00);
   tolua_function(tolua_S,"addAIHungryFollowPlayer",tolua_AITask_ClientMob_addAIHungryFollowPlayer00);
   tolua_function(tolua_S,"addAIHomeRankWander",tolua_AITask_ClientMob_addAIHomeRankWander00);
   tolua_function(tolua_S,"addAiTaskFlyAttract",tolua_AITask_ClientMob_addAiTaskFlyAttract00);
   tolua_function(tolua_S,"addAiTaskEarthCoreManLash",tolua_AITask_ClientMob_addAiTaskEarthCoreManLash00);
   tolua_function(tolua_S,"addAiTaskEarthCoreManRain",tolua_AITask_ClientMob_addAiTaskEarthCoreManRain00);
   tolua_function(tolua_S,"addAiTaskEarthCoreManSteal",tolua_AITask_ClientMob_addAiTaskEarthCoreManSteal00);
   tolua_function(tolua_S,"addAiTaskFlyFollow",tolua_AITask_ClientMob_addAiTaskFlyFollow00);
   tolua_function(tolua_S,"addAiStayBlock",tolua_AITask_ClientMob_addAiStayBlock00);
   tolua_function(tolua_S, "addAIAtkDestroyBlockTarget", tolua_AITask_ClientMob_addAIAtkDestroyBlockTarget00);
   tolua_function(tolua_S, "AITaskTargetSimilar", tolua_AITask_ClientMob_AITaskTargetSimilar00);
   tolua_function(tolua_S, "addAIBatAttack", tolua_AITask_ClientMob_addAIBatAttack00);
   tolua_function(tolua_S, "addAIBatIdle", tolua_AITask_ClientMob_addAIBatIdle00);
   tolua_function(tolua_S, "addAIFishSwarm", tolua_AITask_ClientMob_addAIFishSwarm00);
   tolua_function(tolua_S, "addAIFishSwimToSurface", tolua_AITask_ClientMob_addAIFishSwimToSurface00);
   tolua_function(tolua_S, "addAIFishHoveringAround", tolua_AITask_ClientMob_addAIFishHoveringAround00);
   tolua_function(tolua_S, "addAIFishFly", tolua_AITask_ClientMob_addAIFishFly00);
   tolua_function(tolua_S, "addAIBirdFishing", tolua_AITask_ClientMob_addAIBirdFishing00);
   tolua_function(tolua_S, "addAiTaskHungryDigEgg", tolua_AITask_ClientMob_addAiTaskHungryDigEgg00);
   tolua_function(tolua_S, "addAiTaskWanderAmphibious", tolua_AITask_ClientMob_addAIWanderAmphibious);
   tolua_function(tolua_S, "addAiTaskJumpGlissadeAmphibious", tolua_AITask_ClientMob_addAIJumpGlissadeAmphibious);
   tolua_function(tolua_S, "addAiLieAndRest", tolua_AITask_ClientMob_addAiLieAndRest00);
   tolua_function(tolua_S, "addAiThrob", tolua_AITask_ClientMob_addAiThrob00);
   tolua_function(tolua_S, "addAiClean", tolua_AITask_ClientMob_addAiClean00);
   tolua_function(tolua_S, "addAiFierce", tolua_AITask_ClientMob_addAiFierce00);
   tolua_function(tolua_S, "addAICreateVacantVortex", tolua_AITask_ClientMob_addAICreateVacantVortex00);
  tolua_endmodule(tolua_S);
 tolua_endmodule(tolua_S);
 return 1;
}


#if defined(LUA_VERSION_NUM) && LUA_VERSION_NUM >= 501
 TOLUA_API int luaopen_AITask (lua_State* tolua_S) {
 return tolua_AITask_open_manual(tolua_S);
};
#endif

