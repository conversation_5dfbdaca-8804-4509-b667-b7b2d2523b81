# AIFlyPointToPoint Lua API 使用说明

## 函数签名

```lua
ClientMob:addAiTaskFlyPointToPoint(priority, startX, startY, startZ, endX, endY, endZ, speed, avoidanceRange, avoidanceHeight)
```

## 参数说明

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `priority` | number | ✅ | - | AI任务优先级 |
| `startX` | number | ✅ | - | 起始位置X坐标 |
| `startY` | number | ✅ | - | 起始位置Y坐标 |
| `startZ` | number | ✅ | - | 起始位置Z坐标 |
| `endX` | number | ✅ | - | 目标位置X坐标 |
| `endY` | number | ✅ | - | 目标位置Y坐标 |
| `endZ` | number | ✅ | - | 目标位置Z坐标 |
| `speed` | number | ❌ | 1.0 | 飞行速度倍率 |
| `avoidanceRange` | number | ❌ | 3 | 避障检测范围(方块) |
| `avoidanceHeight` | number | ❌ | 50.0 | 避障高度 |

## 使用示例

### 基本用法

```lua
-- 让飞行怪物从当前位置飞到指定位置
local mob = getMobByID(12345)
if mob then
    -- 从 (0, 100, 0) 飞到 (2000, 150, 3000)
    mob:addAiTaskFlyPointToPoint(
        10,     -- 优先级
        0,      -- 起始X
        100,    -- 起始Y  
        0,      -- 起始Z
        2000,   -- 目标X
        150,    -- 目标Y
        3000    -- 目标Z
    )
end
```

### 高级用法 - 自定义参数

```lua
-- 高速飞行，大范围避障
local mob = getMobByID(12345)
if mob then
    mob:addAiTaskFlyPointToPoint(
        15,     -- 高优先级
        500,    -- 起始X
        200,    -- 起始Y
        1000,   -- 起始Z
        -500,   -- 目标X
        250,    -- 目标Y
        -1000,  -- 目标Z
        2.5,    -- 2.5倍速度
        5,      -- 5个方块避障范围
        80.0    -- 80单位避障高度
    )
end
```

### 任务优先级示例

```lua
-- 多个AI任务的优先级设置
local mob = getMobByID(12345)
if mob then
    -- 优先级1: 攻击行为
    mob:addAiTaskFlyAttack(1, true, 1.5, 0, 0)
    
    -- 优先级5: 巡逻飞行
    mob:addAiTaskFlyPointToPoint(5, 0, 100, 0, 1000, 120, 500)
    
    -- 优先级10: 随机飞行
    mob:addAiTaskRandFly(10, 100, 5, 8, 50)
end
```

### 实际场景应用

#### 1. NPC快递员路线

```lua
-- 快递员飞行路线：从邮局到目标建筑
function setupDeliveryRoute(mobId, targetX, targetY, targetZ)
    local mob = getMobByID(mobId)
    if mob then
        -- 从邮局 (1000, 150, 2000) 飞到目标地点
        mob:addAiTaskFlyPointToPoint(
            8,      -- 中等优先级
            1000,   -- 邮局X
            150,    -- 邮局Y  
            2000,   -- 邮局Z
            targetX, targetY, targetZ,
            1.8,    -- 较快速度
            4,      -- 较大避障范围
            60.0    -- 适中避障高度
        )
    end
end
```

#### 2. 巡逻兵固定路线

```lua
-- 设置巡逻兵在城墙上的巡逻路线
function setupPatrolRoute(mobId)
    local mob = getMobByID(mobId)
    if mob then
        -- 巡逻路线：城墙西端到东端
        mob:addAiTaskFlyPointToPoint(
            6,      -- 巡逻优先级
            -500,   -- 西端X
            180,    -- 城墙高度
            800,    -- 城墙Z
            500,    -- 东端X
            180,    -- 城墙高度
            800,    -- 城墙Z
            1.2,    -- 正常速度
            2,      -- 小范围避障
            30.0    -- 低避障高度
        )
    end
end
```

#### 3. 动态目标追踪

```lua
-- 让飞行宠物跟随玩家到指定位置
function flyPetToLocation(petId, playerId, targetX, targetY, targetZ)
    local pet = getMobByID(petId)
    local player = getPlayer(playerId)
    
    if pet and player then
        local px, py, pz = player:getPosition()
        
        pet:addAiTaskFlyPointToPoint(
            12,     -- 高优先级跟随
            px, py + 50, pz,  -- 从玩家上方开始
            targetX, targetY, targetZ,
            2.0,    -- 快速飞行
            6,      -- 大范围避障
            100.0   -- 高避障高度
        )
    end
end
```

## 注意事项

1. **坐标系统**：使用游戏世界坐标系统，Y轴为高度
2. **优先级范围**：建议使用1-20，数字越小优先级越高
3. **性能考虑**：避障范围过大会影响性能
4. **适用对象**：仅适用于飞行类型的Mob

## 与其他飞行AI的对比

| 函数名 | 用途 | 目标设定 | 避障功能 |
|--------|------|----------|----------|
| `addAiTaskFlyPointToPoint` | 精确导航 | ✅ 指定坐标 | ✅ 智能避障 |
| `addAiTaskRandFly` | 随机飞行 | ❌ 随机生成 | ❌ 无避障 |
| `addAiTaskFlyLoveBlock` | 物品吸引 | ❌ 搜索方块 | ❌ 无避障 |
| `addAiTaskFlyAttack` | 攻击行为 | ❌ 攻击目标 | ❌ 无避障 |

## 常见问题

**Q: 怪物卡在建筑中怎么办？**
A: 增加`avoidanceRange`和`avoidanceHeight`参数值

**Q: 飞行速度太慢？**
A: 调整`speed`参数，建议范围0.5-3.0

**Q: AI不执行？**
A: 检查优先级设置，确保比其他AI优先级更高