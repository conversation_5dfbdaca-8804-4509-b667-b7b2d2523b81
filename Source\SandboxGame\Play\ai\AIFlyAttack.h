#pragma once
#include "AIBase.h"


class LivingLocoMotion;
//飞行时攻击
class AIFlyAttack :public AIBase //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	AIFlyAttack(ClientMob *pActor, bool trace, float speedMulty, int buffid, int bufflevel);
	~AIFlyAttack();
	virtual bool willRun();
	void start();
	bool continueRun();
	void reset();
	virtual void update();

	bool atkDist(ClientActor *pActor);

	virtual bool canInterruptedByInteract() { return false; }
	virtual bool canInterruptInteract() { return true; }
	//tolua_end
private:
	float m_SpeedMulty;
	LivingLocoMotion * m_pLivingLocomotion;
	bool m_IsAquatic;
	float m_FearDist;
	bool m_Trace;
	int m_AttackTick;
	int m_TraceTimer;
	int m_nBuffid;
	int m_nBufflevel;
}; //tolua_exports