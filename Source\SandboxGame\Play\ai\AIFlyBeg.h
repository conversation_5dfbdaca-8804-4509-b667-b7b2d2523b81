#pragma once
#include "AIBase.h"
#include "world_types.h"

class LivingLocoMotion;
class ClientPlayer;
//飞行乞讨AI
class AIFlyBeg :public AIBase //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	AIFlyBeg(ClientMob *pActor, int iNeedFavoriteFood, int iDist, bool fearMotion);
	~AIFlyBeg();
	virtual bool willRun();
	void start();
	bool continueRun();
	void reset();
	virtual void update();

	bool canAttrackMe(ClientPlayer *player);
	virtual AI_MOTION_TYPE getMotionType() { return FKY_ITEM_ATTRACTED; }
	//tolua_end
private:
	float m_FleeSpeed;
	LivingLocoMotion * m_pLivingLocomotion;
	float m_BegDist;
	int m_FavoriteFoodID;
	bool m_FearMotion;
	int m_FearTick;
	WORLD_ID m_TargetID;
	int m_StopDist;
	bool m_HasArrive;
	int m_FlyTick;
}; //tolua_exports