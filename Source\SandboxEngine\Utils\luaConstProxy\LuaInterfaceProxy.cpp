#include "LuaInterfaceProxy.h"
#include "OgreScriptLuaVM.h"
//#include "OgreFileSystem.h"

#include "Platforms/PlatformInterface.h"
#include "OgreUtils.h"

#include "OgreStringUtil.h"
#include "OgreMD5.h"
//#include "GameEvent.h"
#include <algorithm>
#include <cctype>
#include <string>
#include "Aes.h"
#include <regex>
#include "Utilities/utfcpp/utf8.h"

#include "CustomModelMgr.h"
#include "Pkgs/PkgUtils.h"
#include "Utilities/UnicodeString.h"
#include "GameStatic.h"
#include "File/FileManager.h"
#include "Jobs/MainThreadJob.h"
#include "ModuleInterface/IClientInfo.h"
#include "WorldManager.h"

#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
#include <tchar.h>
#endif
//#include "threadtask/OgreThreadTask.h"

#if PLATFORM_ANDROID
    #include <android/log.h>
    #include <arpa/inet.h>
    #include <openssl/bio.h>
    #include <openssl/buffer.h>
    #include <openssl/evp.h>
    #include <openssl/sha.h>
    #include "Platforms/Android/AppPlayJNI.h"
    #include "Platforms/Android/AppPlayBaseActivity.h"
#elif PLATFORM_OHOS
    #include <arpa/inet.h>
    #include <openssl/bio.h>
    #include <openssl/buffer.h>
    #include <openssl/evp.h>
    #include <openssl/sha.h>
#elif PLATFORM_WIN
    #include <io.h>
    #include "Platforms/Win/WinUnicode.h"
    #include <WinSock2.h>
#endif

#if !defined(_DEBUG)
#include "GlobalFunctions.h"
#endif
#if PLATFORM_LINUX
#include <arpa/inet.h>
#include <unistd.h>
#endif
using namespace MINIW;

extern "C" {
	//{{{
#include "lua.h"
#include "miniz.h" 



	static int lua_xxtea_encrypt(lua_State* L)
	{
		//{{{
		const char* s;
		unsigned int len, need_size;
		unsigned char* buff;
		unsigned int* block;
		char* b64;
		if (lua_isstring(L, 1) && lua_isnumber(L, 2)) {
			s = lua_tostring(L, 1);
			len = (int)lua_tonumber(L, 2);

			// 长度等于0，不加密
			if (len == 0)
			{
				lua_pushstring(L, s);
				return 1;
			}

			need_size = xxtea_need_size(len);
			buff = (unsigned char*)malloc(need_size);
			block = (unsigned int*)buff;
			block[0] = htonl(len);
			memcpy((void*)(buff + sizeof(unsigned int)), s, len);
			xxtea_encrypt(block, need_size / 4, getXXTeaKey());
			if (lua_isstring(L, 3)) {
				// b64??{{{
				b64 = b64_encode((const unsigned char*)block, need_size);
				lua_pushstring(L, b64);
				free(b64);
				// }}}
			}
			else {
				lua_pushlstring(L, (const char*)block, need_size);
			}
			free(buff);
			return 1;
		}
		else {
			return 0;
		}
		//}}}
	}
	static int lua_xxtea_decrypt(lua_State* L)
	{
		//{{{
		const char* s;
		int len;
		unsigned realsize;
		size_t b64size;
		unsigned char* buff;
		int ret = 0;
		if (lua_isstring(L, 1) && lua_isnumber(L, 2)) {
			s = lua_tostring(L, 1);
			len = (int)lua_tonumber(L, 2);

			// 长度小于等于0，不解密
			if (len <= 0)
			{
				lua_pushstring(L, s);
				return 1;
			}

			if (lua_isstring(L, 3)) {
				//{{{
				buff = b64_decode_ex(s, len, &b64size); // 有分配内存

				xxtea_decrypt((unsigned int*)buff, b64size / 4, getXXTeaKey());
                //auto buff_len = strlen((char *)buff);
                if(b64size < sizeof(unsigned int))	// same as b64size / 4 <= 0
                {
                    realsize = 0;//ntohl(((unsigned int*)buff)[0]);
                }
                else
                {
                    realsize = ntohl(((unsigned int*)buff)[0]);
                }
                
                if (realsize + sizeof(unsigned int) <= b64size) // 解码后长度不可能超出缓存
				{
					lua_pushlstring(L, (const char*)(buff + sizeof(unsigned int)), realsize);
					ret = 1;
				}
				else
				{
					ret = 0;
				}

				free(buff); //释放b64分配的内存
				return ret;
				//}}}
			}
			else {
				//{{{
					// 2021/10/11 内存申请和释放函数要匹配 codeby:xiehaijiao
				buff = (unsigned char*)malloc(len);
				memcpy(buff, s, len);
				xxtea_decrypt((unsigned int*)buff, len / 4, getXXTeaKey());
				realsize = ntohl(((unsigned int*)buff)[0]);
				if (realsize > (unsigned int)len) {
					free(buff);
					return 0;
				}

				lua_pushlstring(L, (const char*)(buff + sizeof(unsigned int)), realsize);
				free(buff);
				return 1;
				//}}}
			}
		}
		else {
			return 0;
		}
		//}}}
	}
	// }}}

	// zip {{{
	//压缩 -> 加密 -> b64
	static int lua_xxtea_encrypt_zip(lua_State* L)
	{
		//{{{
		const char* s;
		unsigned int len, need_size;
		unsigned int* block;
		char* b64;
		unsigned long zip_len = 1024 * 1024 * 4;  // 1M已经不够用了,导致部分表解压失败，现扩容为2M codeby: fym
		unsigned char* zip_buff; //足够大的内存, 超过1m就不支持了, 因为不知道需要多大内存, 所以,就搞大一点
		int code = 0;
		if (lua_isstring(L, 1) && lua_isnumber(L, 2)) {
			zip_buff = (unsigned char*)malloc(zip_len);
			s = lua_tostring(L, 1);
			len = (int)lua_tonumber(L, 2);
			//压缩 前四个字节留着放长度
			code = mz_compress((unsigned char*)(zip_buff + sizeof(unsigned int)), &zip_len, (const unsigned char*)s, (unsigned long)len);
			if (code != 0) {
				free(zip_buff);
				return 0;
			}

			need_size = xxtea_need_size(zip_len); //长度必须要4的倍数
			block = (unsigned int*)zip_buff;
			block[0] = htonl(zip_len); //存储压缩后的长度, 解密的时候用
			xxtea_encrypt(block, need_size / 4, getXXTeaKey()); //加密操作
			if (lua_isstring(L, 3)) {
				// b64??{{{
				b64 = b64_encode((const unsigned char*)block, need_size);
				lua_pushstring(L, b64);
				free(b64);
				// }}}
			}
			else {
				free(zip_buff);
				return 0;
			}
			free(zip_buff);
			return 1;
		}
		else {
			return 0;
		}
		//}}}
	}
	// b64->解密->解压
	//本函数仅在加载lua代码的时候使用, 频度较低,因此解压所需内存不做重用处理.
	//本函数主要目的热更新的时候, 下载压缩后的加密数据, 节省流量.
	static int lua_xxtea_decrypt_unzip(lua_State* L)
	{
		//{{{
		const char* s;
		int len;
		unsigned realsize;
		size_t b64size;
		unsigned char* buff;
		unsigned char* unzip_buff;//[1024*1024]; //足够大的内存, 超过1m就不支持了
		unsigned long unzip_len = 1024 * 1024 * 4; //1M已经不够用了, 导致部分表解压失败，现扩容为2M codeby : fym
		int code = 0;
		if (lua_isstring(L, 1) && lua_isnumber(L, 2) && lua_isstring(L, 3)) {
			unzip_buff = (unsigned char*)malloc(unzip_len);
			s = lua_tostring(L, 1);
			len = (int)lua_tonumber(L, 2);
			//{{{
			buff = b64_decode_ex(s, len, &b64size); // 有分配内存
			//解密 
			xxtea_decrypt((unsigned int*)buff, b64size / 4, getXXTeaKey());
			realsize = ntohl(((unsigned int*)buff)[0]);
			//解压
			code = mz_uncompress(unzip_buff, &unzip_len, (const unsigned char*)(buff + sizeof(unsigned int)), (unsigned long)realsize);
			free(buff); //释放b64分配的内存
			if (code != 0) {
				free(unzip_buff);
				return 0;
			}
			lua_pushlstring(L, (const char*)(unzip_buff), unzip_len);
			free(unzip_buff);
			return 1;
			//}}}

		}
		else {
			return 0;
		}
		//}}}
	}
	// }}}


	// md5 {{{
	//inline void Md5Calc(char output[16], const char *input, size_t length)

	static char hexlua[33];
	static int lua_md5_sum(lua_State* L)
	{
		//{{{
		const char* s;
		size_t len;
		char output[16];
		if (lua_isstring(L, 1) && lua_isnumber(L, 2)) {
			s = lua_tostring(L, 1);
			len = (size_t)lua_tonumber(L, 2);
			MINIW::Md5Calc(output, s, len);
			MINIW::Md5ToHex(hexlua, output);
			lua_pushlstring(L, (const char*)hexlua, 32);
			return 1;
		}

		return 0;
		//}}}
	}

	//CalShopCheckData((char *)clt.ShopData.ShopComm.AddCoinInfo.CheckData, m_PayDynCode, MINIW::FileManager::getSingleton().m_FilesDigest1, MINIW::FileManager::getSingleton().m_FilesDigest2);

	// md5验证, 返回值都是hex
	static int lua_md5_get_file_digest(lua_State* L)
	{
		//{{{		
		// apiid, cltversion
		if (lua_isnumber(L, 1) && lua_isnumber(L, 2)) {

			const char* hexlua = Rainbow::PkgUtils::GetFilesDigestMd5((UInt32)lua_tonumber(L, 1), (UInt32)lua_tonumber(L, 2));
			lua_pushlstring(L, (const char*)hexlua, 32);
			return 1;
		}
		else {
			return 0;
		}
		//}}}
	}
	// 参数 是dyncode
	static int lua_md5_shop_checkdata(lua_State* L)
	{
		//{{{
		
		int dyncode;	
		if (lua_isnumber(L, 1)) {
			dyncode = (UInt32)lua_tonumber(L, 1);
			const char* hexlua = Rainbow::PkgUtils::GetShopDataMd5(dyncode);
			lua_pushlstring(L, hexlua, 32);
			return 1;
		}
		else {
			return 0;
		}
		//}}}
	}

	// }}}

	//}}}
}

#define mix(a,b,c) \
			{ \
			a -= b; a -= c; a ^= (c>>13); \
			b -= c; b -= a; b ^= (a<<8); \
			c -= a; c -= b; c ^= (b>>13); \
			a -= b; a -= c; a ^= (c>>12);  \
			b -= c; b -= a; b ^= (a<<16); \
			c -= a; c -= b; c ^= (b>>5); \
			a -= b; a -= c; a ^= (c>>3);  \
			b -= c; b -= a; b ^= (a<<10); \
			c -= a; c -= b; c ^= (b>>15); \
}

static LuaInterfaceProxy* s_instance = nullptr;
LuaInterfaceProxy::LuaInterfaceProxy()
{
	s_instance = this;
	ScriptVM* scriptvm = ScriptVM::game();
	//{{{
	m_VM = scriptvm;

	m_seed = 0;
	//for debug {{{
	int size = 0;
	this->m_FP = -1; //for debug
	m_FP = open("./debug.fifo", O_RDONLY);
	if (m_FP < 0) { m_FP = -1; }
	if (m_FP >= 0)
	{
		while (1)
		{
			size = read(m_FP, m_buffer, 1024);
			if (size == 0)
			{
				break;
			}
		}
	}
	//for debug}}} 

	lua_State* L = scriptvm->getLuaState();

	int n = lua_gettop(L);
	//{{{
	lua_getglobal(L, "xxtea");
	{
		if (lua_isnil(L, -1))
		{
			lua_newtable(L);
			lua_setglobal(L, "xxtea");
			lua_getglobal(L, "xxtea");
		}
	} //[xxtea]

	{
		lua_pushstring(L, "encrypt");
		lua_pushcfunction(L, lua_xxtea_encrypt);
		lua_settable(L, -3);

		lua_pushstring(L, "decrypt");
		lua_pushcfunction(L, lua_xxtea_decrypt);
		lua_settable(L, -3);

		lua_pushstring(L, "encrypt_zip");
		lua_pushcfunction(L, lua_xxtea_encrypt_zip);
		lua_settable(L, -3);

		lua_pushstring(L, "decrypt_unzip");
		lua_pushcfunction(L, lua_xxtea_decrypt_unzip);
		lua_settable(L, -3);
	}

	lua_settop(L, n);
	//}}}

	// cpp_const {{{
	lua_getglobal(L, "cpp_const");
	{
		if (lua_isnil(L, -1))
		{
			lua_newtable(L);
			lua_setglobal(L, "cpp_const");
			lua_getglobal(L, "cpp_const");
		}
	} // [cpp_const]

	{
		lua_pushstring(L, "MAX_ITEM_INFO_NUM");
		lua_pushnumber(L, MAX_ITEM_INFO_NUM);
		lua_settable(L, -3);

		lua_pushstring(L, "MAX_UNLOCK_ROLE_INFO_NUM");
		lua_pushnumber(L, MAX_UNLOCK_ROLE_INFO_NUM);
		lua_settable(L, -3);

		lua_pushstring(L, "MAX_ROLE_SKIN_NUM");
		lua_pushnumber(L, MAX_ROLE_SKIN_NUM);
		lua_settable(L, -3);
	}
	{
		lua_pushstring(L, "ITEM_ID_MINI_BEAN");
		lua_pushnumber(L, ITEM_ID_MINI_BEAN);
		lua_settable(L, -3);
		lua_pushstring(L, "ITEM_ID_TREE_EXP");
		lua_pushnumber(L, ITEM_ID_TREE_EXP);
		lua_settable(L, -3);
	}

	{
		lua_pushstring(L, "ITEM_UNLOCK_COST_ITEM");
		lua_pushnumber(L, ITEM_UNLOCK_COST_ITEM);
		lua_settable(L, -3);

		lua_pushstring(L, "ITEM_UNLOCK_COST_BEAN");
		lua_pushnumber(L, ITEM_UNLOCK_COST_BEAN);
		lua_settable(L, -3);

		lua_pushstring(L, "ITEM_UNLOCK_COST_BOTH");
		lua_pushnumber(L, ITEM_UNLOCK_COST_BOTH);
		lua_settable(L, -3);
	}

	{
		lua_pushstring(L, "CHEST_OP_OPEN");
		lua_pushnumber(L, CHEST_OP_OPEN);
		lua_settable(L, -3);

		lua_pushstring(L, "CHEST_OP_SPEED");
		lua_pushnumber(L, CHEST_OP_SPEED);
		lua_settable(L, -3);

		lua_pushstring(L, "CHEST_OP_GIFT");
		lua_pushnumber(L, CHEST_OP_GIFT);
		lua_settable(L, -3);

		lua_pushstring(L, "CHEST_OP_TEN_GIFT");
		lua_pushnumber(L, CHEST_OP_TEN_GIFT);
		lua_settable(L, -3);
	}

	{
		//CHEST_STATUS
		lua_pushstring(L, "CHEST_STATUS_INIT");
		lua_pushnumber(L, CHEST_STATUS_INIT);
		lua_settable(L, -3);

		lua_pushstring(L, "CHEST_STATUS_TIME");
		lua_pushnumber(L, CHEST_STATUS_TIME);
		lua_settable(L, -3);

		lua_pushstring(L, "CHEST_STATUS_DONE");
		lua_pushnumber(L, CHEST_STATUS_DONE);
		lua_settable(L, -3);
	}

	{
		//CHEST_TREE_ACTION
		lua_pushstring(L, "CHEST_TREE_ACTION_WATER_SELF_ADD");
		lua_pushnumber(L, CHEST_TREE_ACTION_WATER_SELF_ADD);
		lua_settable(L, -3);

		lua_pushstring(L, "CHEST_TREE_ACTION_WATER_OTHER_ADD");
		lua_pushnumber(L, CHEST_TREE_ACTION_WATER_OTHER_ADD);
		lua_settable(L, -3);

		lua_pushstring(L, "CHEST_TREE_ACTION_WATER_FULL");
		lua_pushnumber(L, CHEST_TREE_ACTION_WATER_FULL);
		lua_settable(L, -3);

		lua_pushstring(L, "CHEST_TREE_ACTION_WATER_POOR");
		lua_pushnumber(L, CHEST_TREE_ACTION_WATER_POOR);
		lua_settable(L, -3);

		lua_pushstring(L, "CHEST_TREE_ACTION_BUG");
		lua_pushnumber(L, CHEST_TREE_ACTION_BUG);
		lua_settable(L, -3);

		lua_pushstring(L, "CHEST_TREE_ACTION_LEVEL_UP");
		lua_pushnumber(L, CHEST_TREE_ACTION_LEVEL_UP);
		lua_settable(L, -3);
	}

	{
		// CHEST_TYPE
		lua_pushstring(L, "CHEST_TYPE_GOLDEN");
		lua_pushnumber(L, CHEST_TYPE_GOLDEN);
		lua_settable(L, -3);

		lua_pushstring(L, "CHEST_TYPE_SMALL");
		lua_pushnumber(L, CHEST_TYPE_SMALL);
		lua_settable(L, -3);

		lua_pushstring(L, "CHEST_TYPE_BIG");
		lua_pushnumber(L, CHEST_TYPE_BIG);
		lua_settable(L, -3);

		lua_pushstring(L, "CHEST_TYPE_GIANT");
		lua_pushnumber(L, CHEST_TYPE_GIANT);
		lua_settable(L, -3);

		lua_pushstring(L, "CHEST_TYPE_GOLDEN_TEN");
		lua_pushnumber(L, CHEST_TYPE_GOLDEN_TEN);
		lua_settable(L, -3);
	}

	{
		// WATER_ADD_TYPE
		lua_pushstring(L, "WATER_ADD_TYPE_SELF");
		lua_pushnumber(L, WATER_ADD_TYPE_SELF);
		lua_settable(L, -3);

		lua_pushstring(L, "WATER_ADD_TYPE_OTHER");
		lua_pushnumber(L, WATER_ADD_TYPE_OTHER);
		lua_settable(L, -3);

		lua_pushstring(L, "WATER_ADD_TYPE_BUG");
		lua_pushnumber(L, WATER_ADD_TYPE_BUG);
		lua_settable(L, -3);
	}
	{
		lua_pushstring(L, "MAX_VISIT_INFO");
		lua_pushnumber(L, MAX_VISIT_INFO);
		lua_settable(L, -3);
	}

	{
		//CHEST_TREE_GIFT_TYPE
		lua_pushstring(L, "CHEST_TREE_GIFT_TYPE_BEAN");
		lua_pushnumber(L, CHEST_TREE_GIFT_TYPE_BEAN);
		lua_settable(L, -3);

		lua_pushstring(L, "CHEST_TREE_GIFT_TYPE_CHEST");
		lua_pushnumber(L, CHEST_TREE_GIFT_TYPE_CHEST);
		lua_settable(L, -3);
	}
	{
		//MAX_BUDDY
		lua_pushstring(L, "MAX_BUDDY");
		lua_pushnumber(L, MAX_BUDDY);
		lua_settable(L, -3);
	}
	{
		//UIN_FLAG_BIND
		lua_pushstring(L, "UIN_FLAG_BIND");
		lua_pushnumber(L, UIN_FLAG_BIND);
		lua_settable(L, -3);
	}

	{
		//NAME_LEN
		lua_pushstring(L, "NAME_LEN");
		lua_pushnumber(L, NAME_LEN);
		lua_settable(L, -3);
	}

	lua_settop(L, n);
	// }}}


	// 充值 md5 相关 {{{
	lua_getglobal(L, "g_recharge_md5_related");
	{
		if (lua_isnil(L, -1))
		{
			lua_newtable(L);
			lua_setglobal(L, "g_recharge_md5_related");
			lua_getglobal(L, "g_recharge_md5_related");
		}
	} // [g_recharge_md5_related]
	{
		lua_pushstring(L, "md5sum");
		lua_pushcfunction(L, lua_md5_sum);
		lua_settable(L, -3);

		lua_pushstring(L, "get_file_digest");
		lua_pushcfunction(L, lua_md5_get_file_digest);
		lua_settable(L, -3);

		lua_pushstring(L, "shop_checkdata");
		lua_pushcfunction(L, lua_md5_shop_checkdata);
		lua_settable(L, -3);
	}
	lua_settop(L, n);
	// }}}

	// init m_const, 程序启动的时候, lua代码里面会根据配置重新初始化ConstAtLua的值  {{{
	memset(&m_const, 0, (char*)&m_const.endOfPOD - (char*)&m_const);
	memset(&m_soc_const, 0, (char*)&m_soc_const.endOfPOD - (char*)&m_soc_const);

	m_soc_const.soctestvalue = 0;
	m_soc_const.vLinkBlockIdList.clear();
	//m_soc_const.vOneOnOneLinkList.clear();
	//m_soc_const.vOneToManyRangeList.clear();
	m_soc_const.vRandNpcList.clear();
	m_soc_const.vTargetIdList.clear();
	m_soc_const.vSourceList.clear();

	
	m_const.SocLargeMapSizeMin = 0;
	m_const.SocMediumMapSizeMin = 0;
	m_const.SocSmallMapSizeMin = 0;
	
	// 设置地形配置默认值
	m_const.SocLargeMapSmallTerrain_plains_forest = 0;
	m_const.SocLargeMapSmallTerrain_plains_foresthill = 0;
	m_const.SocLargeMapSmallTerrain_plains_lake = 0;
	m_const.SocLargeMapSmallTerrain_plains_canyon = 0;
	
	m_const.SocLargeMapSmallTerrain_deserts_deserthill = 0;
	m_const.SocLargeMapSmallTerrain_deserts_desertoasis = 0;
	
	m_const.SocLargeMapSmallTerrain_iceplains_icemountains = 0;
	m_const.SocLargeMapSmallTerrain_iceplains_frizzelake = 0;
	
	m_const.SocMediumMapSmallTerrain_plains_forest = 0;
	m_const.SocMediumMapSmallTerrain_plains_foresthill = 0;
	m_const.SocMediumMapSmallTerrain_plains_lake = 0;
	m_const.SocMediumMapSmallTerrain_plains_canyon = 0;
	
	m_const.SocMediumMapSmallTerrain_deserts_deserthill = 0;
	m_const.SocMediumMapSmallTerrain_deserts_desertoasis = 0;
	
	m_const.SocMediumMapSmallTerrain_iceplains_icemountains = 0;
	m_const.SocMediumMapSmallTerrain_iceplains_frizzelake = 0;
	
	m_const.SocSmallMapSmallTerrain_plains_forest = 0;
	m_const.SocSmallMapSmallTerrain_plains_foresthill = 0;
	m_const.SocSmallMapSmallTerrain_plains_lake = 0;
	m_const.SocSmallMapSmallTerrain_plains_canyon = 0;
	
	m_const.SocSmallMapSmallTerrain_deserts_deserthill = 0;
	m_const.SocSmallMapSmallTerrain_deserts_desertoasis = 0;
	
	m_const.SocSmallMapSmallTerrain_iceplains_icemountains = 0;
	m_const.SocSmallMapSmallTerrain_iceplains_frizzelake = 0;

	
	m_const.hpmax = 100.0f;
	m_const.hpoverflow = 100;

	m_const.strengthmax = 100;
	m_const.strengthoverflow = 100;

	m_const.max_strength_for_exhaustion = 15.f;
	m_const.max_percentage_of_strength_for_exhaustion = 0.15f;
	m_const.architectural_repaired_value = 5;
	m_const.waterblock_bad_effect_probability = 5;
	m_const.waterblock_bad_effect_value = 5;

	m_const.strength_consumption_of_attack = 2.f;
	m_const.strength_consumption_of_running_jump = 3.f;
	m_const.strength_consumption_of_double_jump = 4.f;
	m_const.strength_consumption_of_charging_per_second = 1.f;
	m_const.strength_consumption_of_swimming_per_second = 6.f;
	m_const.strength_consumption_of_digging_per_second = 7.f;
	m_const.strength_consumption_of_running_per_second = 8.f;

	m_const.strength_consumption_of_digging_block_min = 0.05f;
	m_const.strength_consume_fishing = 0.1f;
	m_const.reduce_strength_per_hp_recover = 1;
	m_const.actor_revive_in_place_strength_min = 20; 

	m_const.thirst_consumption_of_attack = 2.f;
	m_const.thirst_consumption_of_running_jump = 3.f;
	m_const.thirst_consumption_of_double_jump = 4.f;
	m_const.thirst_consumption_of_charging_per_second = 1.f;
	m_const.thirst_consumption_of_swimming_per_second = 6.f;
	m_const.thirst_consumption_of_digging_per_second = 7.f;
	m_const.thirst_consumption_of_running_per_second = 8.f;

	m_const.thirst_consumption_of_digging_block_min = 0.05f;
	m_const.thirst_consume_fishing = 0.1f;
	m_const.reduce_thirst_per_hp_recover = 1;
	m_const.actor_revive_in_place_thirst_min = 20;


	m_const.be_forbidden_to_run_with_exhaustion = true;

	m_const.check_strength_enough = false;

	m_const.foodlevel = 100.0f;
	m_const.kongshou_shanghai_beilv = 5;
	m_const.food_beilv = 5;
	m_const.cactus_shanghai_beilv = 5;
	m_const.yanjiang_shanghai_beilv = 5;
	m_const.zhixi_shanghai_beilv = 5;
	m_const.nishui_shanghai_beilv = 5;
	m_const.xukong_shanghai_beilv = 5;
	m_const.falling_shanghai_beilv = 5;
	m_const.default_shanghai_beilv = 5;

	m_const.default_xieliang_huifu_beilv_old = 5.f;
	m_const.default_xieliang_huifu_beilv = 1.f;
	m_const.default_tili_huifu_beilv = 1.f;

	m_const.kaijing_sensitivity_xishu = 0.3f;
	m_const.kaijing_yidong_xishu = 0.7f;
	m_const.planet_gravity_beilv = 0.4f;
	m_const.planet_oxygenuse_beilv = 0.0f;
	m_const.planet_oxygenhurt_beilv = 5.0f;
	m_const.planet_safearea_radius = 16;
	m_const.planet_tili_beilv = 1.5f;
	m_const.planet_daynight_beilv = 2.0;
	m_const.planet_lightatten_beilv = 2;
	m_const.planet_cloud_genperiod = 1;
	m_const.planet_cloud_delperiod = 1;
	m_const.planet_cloud_crackperiod = 4;
	m_const.planet_totem_activeage = 5;
	m_const.planet_totem_pollutionradius = 64;
	m_const.planet_totem_pollutionspeed = 4;
	m_const.fall_hurt_ext = 3.0f;
	m_const.fall_hurt_ext2 = 5.0f;
	m_const.effect_maxscale = 100.0f;
	m_const.swimming_yidong_beilv = 0.25f;
	m_const.swimming_honey_yidong_beilv = 0.1f;
	m_const.houtui_yidong_beilv = 0.8f;

	m_const.actor_max_extremisval = 100;
	m_const.actor_max_hugger = 3000;
	m_const.consume_food_by_hugger = 5;
	m_const.actor_init_favor = 50;
	m_const.actor_max_favor = 100;
	m_const.decay_extremis = 1;

	m_const.actor_bound_height = 200;
	m_const.actor_bound_width = 110;

	m_const.feedtrough_1_consume_count = 10;
	m_const.feedtrough_2_consume_count = 20;
	m_const.feedtrough_3_consume_count = 30;

	m_const.number_of_sprinklers = 20;
	m_const.check_new_hp_rule = 0;
	m_const.strength_min_overdraw = 0;
	m_const.strength_max_overdraw = 0;
	m_const.thirst_min_overdraw = 0;
	m_const.thirst_max_overdraw = 0;
	m_const.actor_perseverance_max = 100;
	m_const.actor_armor_max = 100;
	m_const.strength_consume_walk = 0;
	m_const.thirst_consume_walk = 0;
	m_const.actor_revive_hp = 100;
	m_const.actor_revive_strength = 100;
	m_const.HotSpringGenRate = 100;
	m_const.HotSpringGenDis = 3;

	m_const.GenMossRate = 50;

	m_const.weapon_skillpoint_killed = 2;
	m_const.weapon_skillpoint_cdskill = 2;
	m_const.weapon_skillpoint_cuttree = 1;
	m_const.weapon_skillpoint_digblock = 1;
	m_const.weapon_skillpoint_plantland = 1;


	/*
	m_const.sneaking_yidong_beilv = 0.5;
	m_const.chongci_yidong_beilv = 1.3;
	m_const.xuli_yidong_beilv = 0.5;
	m_const.chongci_phys_yidong_beilv = 2.3;

	m_const.tili_action_walk = 15;
	m_const.tili_action_sprint = 75;
	m_const.tili_action_swim = 25;
	m_const.tili_action_jump = 195;
	m_const.tili_action_sprintjump = 960;
	m_const.tili_action_destroyblock = 100;
	m_const.tili_action_attack = 360;
	m_const.tili_action_hurt = 360;
	m_const.tili_action_food2hp = 3600;
	// }}}
	//}}}
	*/

	m_const.sandman_core_hp = 80;
	m_const.sandman_absorbsand_max = 10;
	m_const.sandman_absorbsand_curehp = 30.0f;
	m_const.sandman_absorbsand_scale = 0.2f;
	m_const.sandman_fakedeath_tick = 1200;
	m_const.sandman_sonicboom_tick = 100;

	m_const.sand_duststorm_power = 2.0;
	m_const.sand_duststorm_power_up = 4.0;
	m_const.sand_duststorm_speed = 1.0;
	m_const.sand_duststorm_idle = 6000;
	m_const.sand_duststorm_up_start = 200;
	m_const.sand_duststorm_up_end = 1000;
	m_const.sand_duststorm_end = 1000;

	m_const.cameraTpsBackPlayerRotationLimitAngle = 180.f;

	m_const.tempest_power = 2.0;
	m_const.tempest_power_up = 4.0;
	m_const.tempest_idle = 6000;
	m_const.tempest_up_start = 200;
	m_const.tempest_up_end = 1000;
	m_const.tempest_end = 1000;
	m_const.rainbow_probability = 50;
	m_const.tempest_probability = 50;
	m_const.tempest_add_tick = 600;
	m_const.rainbow_tick = 600;

	m_const.water_pressure_coefficient = 10;
	m_const.oxygen_consume_coefficient = 1.0f;
	m_const.power_consume_coefficient = 1.0f;
	m_const.block_hardness_value = 7;
	m_const.block_pressure_value = 3;
	m_const.block_scan_liquid_range = 5;
	m_const.block_scan_all_range = 20;

	m_const.oxygen_pack_full_bullet_num = 16;
	m_const.air_ball_atk_value_param = 0;
	m_const.crab_create_random_num = 5;
	m_const.clamp_click_max = 5;
	m_const.bubble_move_max_distance = 12;
	m_const.air_ball_range_value_param = 50;
	m_const.air_ball_land_range_value_param = 200;
	m_const.air_ball_max_range_value_param = 300;
	m_const.air_ball_land_montion_param = 100;

	m_const.fishingVillageExploreRange = 28;

	m_const.scallops_in_water_tick = 6000;
	m_const.scallops_around_fire_tick = 1200;
	m_const.scallops_after_dispear_tick = 100;
	m_const.scallops_born_pearl = 1;
	m_const.scallops_born_crap = 3;
	m_const.livingWaterJumpBaseSpeed = 4.0f;
	m_const.livingWaterSneakAddSpeed = 2.0f;
	m_const.spawnPointRangeNum = 10;

	m_const.m_ConstMilaTempAtten = 20;
	m_const.m_ConstMilaTempAdd = 20;
	m_const.m_ConstMengyanTempAtten = 20;
	m_const.m_ConstMengyanTempAdd = 20;
	m_const.m_ConstPingtanTempAtten = 20;
	m_const.m_ConstPingtanTempAdd = 20;
	m_const.m_ConstPlantTempChangeVal = 1.0f;
	m_const.m_ConstTickTempChangeVal = 0.005f;
	m_const.m_ConstBaseTempChangeVal = 0.05f;
	m_const.m_ConstTickTempChangeRate = 10.0f;
	m_const.m_ConstTempBurn = 11.0f;
	m_const.m_ConstTempTopHeat = 7.0f;
	m_const.m_ConstTempHeat = 3.0f;
	m_const.m_ConstTempIce = -3.0f;
	m_const.m_ConstTempTopIce = -7.0f;
	m_const.m_ConstTempFreeze = -11.0f;
	m_const.m_ConstWeatherRain = -1.0f;
	m_const.m_ConstWeatherSnow = -1.0f;
	m_const.m_ConstWeatherTempest = -1.0f;
	m_const.m_ConstWeatherTempestUp = -1.0f;
	m_const.m_ConstWeatherBlizzard = -2.0f;
	m_const.m_ConstWeatherBlizzardUp = -3.0f;
	m_const.m_ConstWeatherThunder = -1.0f;

	m_const.blizzard_power = 0.f;
	m_const.blizzard_power_up = 0.f;
	m_const.blizzard_speed = 1.0f;
	m_const.blizzard_idle = 72000;
	m_const.blizzard_up_start = 600;
	m_const.blizzard_up_end = 6000;
	m_const.blizzard_end = 300;
	m_const.manualEmitterInterval = 200;
	m_const.hp_recover_per_unit_of_time = 5;
	m_const.hp_recover_unit_time = 5;
	m_const.min_strength_for_hp_recover = 85;
	m_const.vortex_bigger_strain = 70;
	m_const.vortex_smaller_strain = 20;
	m_const.player_actionattrtip = 60;
	m_const.magicResistCoefficient = 50;
	m_const.isOpenNewHpdecCalculate = true;
	m_const.dampingControlA = 3;
	m_const.dampingControlB = 2;
	m_const.doubleWeaponDurPer = 0.8f;
	m_const.fusionCageValPer = 1;
	m_const.fusionCageTime = 40;
	m_const.doubleWeaponNeedValPer = 0.8f;
	m_const.aiatk_frist_pre_tick = 0;
	m_const.aiatk_interval_tick = 30;
}


LuaInterfaceProxy::~LuaInterfaceProxy(void)
{
	m_events.Clear();
}




ConstAtLua* LuaInterfaceProxy::get_lua_const()
{
	return &m_const;
}



bool LuaInterfaceProxy::isdebug(void)
{
#ifdef IWORLD_SERVER_BUILD 
	return true;
#else
#if (defined(_DEBUG) || defined(BUILD_MINI_EDITOR_APP))
	return true;
#else
	static bool isdebug = gFunc_isStdioFileExist("data/debugminiworld.txt") || gFunc_isStdioFileExist("data/debuglog.txt");
	//if(isdebug) LOG_INFO("LuaInterfaceProxy debugminiworld.txt exists!");
	return isdebug;
#endif
#endif
}

// 检查字符串是否为有效的UTF-8编码
static bool is_valid_utf8(const std::string& str) {
	return utf8::is_valid(str.begin(), str.end());
}

// 计算UTF-8字符串的字符数量（不是字节数量）
static size_t utf8_character_count(const std::string& str) {
	size_t count = 0;
	auto it = str.begin();
	while (it != str.end()) {
		try {
			utf8::unchecked::next(it);
			++count;
		} catch (...) {
			// 跳过无效的UTF-8字符
			if (it != str.end()) {
				++it;
			}
		}
	}
	return count;
}



// 检查字符是否为应该过滤的危险或不适合的字符
static bool is_filtered_char(uint32_t codepoint) {
	// 控制字符 (0x00-0x1F, 0x7F-0x9F)
	if (codepoint <= 0x1F || (codepoint >= 0x7F && codepoint <= 0x9F)) {
		return true;
	}
	
	// 不可见和格式化字符
	if ((codepoint >= 0x200B && codepoint <= 0x200F) ||  // 零宽字符
		(codepoint >= 0x2028 && codepoint <= 0x202F) ||  // 行分隔符等
		(codepoint >= 0x2066 && codepoint <= 0x2069) ||  // 方向隔离
		(codepoint >= 0x202A && codepoint <= 0x202E) ||  // LTR/RTL标记
		codepoint == 0xFEFF ||  // BOM
		codepoint == 0x00AD ||  // 软连字符
		codepoint == 0x034F ||  // 组合字素连接符
		codepoint == 0x061C ||  // 阿拉伯字母标记
		codepoint == 0x180E) {  // 蒙文元音分隔符
		return true;
	}
	
	// 私用区字符 (可能包含恶意内容)
	if ((codepoint >= 0xE000 && codepoint <= 0xF8FF) ||  // 私用区
		(codepoint >= 0xF0000 && codepoint <= 0xFFFFD) || // 补充私用区A
		(codepoint >= 0x100000 && codepoint <= 0x10FFFD)) { // 补充私用区B
		return true;
	}
	
	// 特殊空格字符（保留普通空格0x20）
	if (codepoint == 0x00A0 ||  // 不换行空格
		codepoint == 0x1680 ||  // Ogham空格
		(codepoint >= 0x2000 && codepoint <= 0x200A) ||  // 各种空格
		codepoint == 0x202F ||  // 窄不换行空格
		codepoint == 0x205F ||  // 中等数学空格
		codepoint == 0x3000) {  // 全角空格
		return true;
	}
	
	// 安全威胁字符
	if (codepoint == 0x003C ||  // <
		codepoint == 0x003E ||  // >
		codepoint == 0x0026 ||  // &
		codepoint == 0x005C ||  // backslash
		codepoint == 0x0000) {  // NULL
		return true;
	}
	
	// 不适合昵称的符号
	if (codepoint == 0x007C ||  // |
		codepoint == 0x0060 ||  // `
		codepoint == 0x007E ||  // ~
		codepoint == 0x007B ||  // {
		codepoint == 0x007D ||  // }
		codepoint == 0x005B ||  // [
		codepoint == 0x005D) {  // ]
		return true;
	}
	
	return false;
}

// 清理并过滤掉昵称中的危险字符
static std::string clean_nickname(const std::string& nickname) {
	if (nickname.empty()) {
		return "";
	}
	
	std::string cleaned = nickname;

	// 第一步：使用正则表达式快速移除明显的问题字符
	// 去除前后空白字符
	cleaned = std::regex_replace(cleaned, std::regex("^\\s+|\\s+$"), "");
	
	// 移除连续的空白字符，替换为单个空格
	cleaned = std::regex_replace(cleaned, std::regex("\\s+"), " ");
	
	// 移除控制字符
	cleaned = std::regex_replace(cleaned, std::regex("[\\x00-\\x1F\\x7F]"), "");

	// 第二步：使用UTF-8字符级别的黑名单过滤
	std::string result;
	auto it = cleaned.begin();
	
	while (it != cleaned.end()) {
		try {
			auto start_it = it;
			uint32_t codepoint = utf8::unchecked::next(it);
			
			// 只排除危险和不适合的字符，其他都保留
			if (!is_filtered_char(codepoint)) {
				// 将原始字节序列添加到结果中
				result.append(start_it, it);
			}
			
		} catch (...) {
			// 跳过无效的UTF-8字符
			if (it != cleaned.end()) {
				++it;
			}
		}
	}
	
	// 第三步：最终清理
	// 再次去除前后空格
	if (!result.empty()) {
		size_t start = result.find_first_not_of(" ");
		if (start == std::string::npos) {
			result = "";
		} else {
			size_t end = result.find_last_not_of(" ");
			result = result.substr(start, end - start + 1);
		}
	}
	
	return result;
}

// 检查并验证昵称的合法性
static bool is_valid_nickname_impl(const std::string& nickname, int min_length, int max_length) {
	// 确保昵称UTF-8字符长度在合理范围内
	size_t utf8_length = utf8_character_count(nickname);
	if (utf8_length < min_length || utf8_length > max_length) {
		return false;
	}

	// 确保昵称是有效的UTF-8编码
	if (!is_valid_utf8(nickname)) {
		LOG_WARNING("is_valid_utf8");
		return false;
	}

	// 清理并过滤非法字符
	std::string cleaned_nickname = clean_nickname(nickname);
	LOG_WARNING("cleaned_nickname %s", cleaned_nickname.c_str());
	
	// 检查清理后的昵称UTF-8字符长度
	size_t cleaned_utf8_length = utf8_character_count(cleaned_nickname);
	if (cleaned_utf8_length == 0 || cleaned_utf8_length < min_length) {
		return false;
	}

	return true;
}

bool LuaInterfaceProxy::is_valid_nickname(const std::string& nickname, int min_length, int max_length)
{
	return is_valid_nickname_impl(nickname, min_length, max_length);
}

int LuaInterfaceProxy::os_mkdir(const char* path)
{
	LOG_INFO("mkdir %s ", path);
	Rainbow::GetFileManager().CreateWritePathDir(path);
	return 0;
}

void* LuaInterfaceProxy::io_open(const char* path, bool mode)
{
	// TODO 需要修复
	/*DataStream* stream = FileManager::getSingleton().openFile(path, mode);
	if (stream != NULL) {
		return (void*)stream;
	}*/
	return NULL;
}
void LuaInterfaceProxy::io_write(void* fp, const std::string str)
{
	// TODO 需要修复
	//DataStream* stream = (DataStream*)fp;
	//if (stream != NULL) {
	//	stream->write((void*)str.c_str(), str.length());
	//}
}
std::string LuaInterfaceProxy::io_read(void* fp)
{
	// TODO 需要修复
	/*DataStream* stream = (DataStream*)fp;
	m_filecontent = "";
	if (stream != NULL) {
		m_filecontent += stream->getAsString();
		return m_filecontent;
	}
	else {
		return "";
	}*/
	return "";
}
std::string LuaInterfaceProxy::io_line(void* fp)
{
	// TODO 需要修复
	/*DataStream* stream = (DataStream*)fp;
	m_filecontent = "";
	if (stream != NULL) {
		if (stream->eof()) {
			return "";
		}
		else {
			m_filecontent += stream->getLine(false);
			m_filecontent += "\n";
			return m_filecontent;
		}
	}
	else {
		return "";
	}*/
	return "";
}

void LuaInterfaceProxy::io_close(void* fp)
{
	// TODO 需要修复
	//DataStream* stream = (DataStream*)fp;
	//stream->close();

}

bool LuaInterfaceProxy::io_dir_exsit(const char* path)
{
	return Rainbow::GetFileManager().IsFileExistWritePath(path);
}

bool LuaInterfaceProxy::dofile(const char* path)
{
	return m_VM->callFile(path);
}
bool LuaInterfaceProxy::loadpackage(const char* path)
{
	return m_VM->loadPackage(path);
}

const char* LuaInterfaceProxy::getStdioRoot()
{
	return Rainbow::GetFileManager().GetWritePathRoot();
}




int LuaInterfaceProxy::bnot(int a) { return ~a; }
int LuaInterfaceProxy::band(int a, int b) { return a & b; }
int LuaInterfaceProxy::bor(int a, int b) { return a | b; }
int LuaInterfaceProxy::bxor(int a, int b) { return a ^ b; }
int LuaInterfaceProxy::lshift(int a, int left) { return a << left; }
int LuaInterfaceProxy::rshift(int a, int right) { return a >> right; }



unsigned int LuaInterfaceProxy::random(unsigned int min, unsigned int max) {
	if (max == 0) { max = 1; }
	if (min == 0) { min = 0; }
	m_seed = (m_seed * 9301 + 49297) % 233280;
	unsigned int rand = min + (m_seed * (max + 1 - min)) / 233280;
	if (rand > max) { rand = max; }
	return rand;
}

void LuaInterfaceProxy::randomseed(unsigned int seed) {
	m_seed = seed;
}

unsigned int LuaInterfaceProxy::string2Int(const char* k)
{
	unsigned int length = strlen(k);
	register unsigned int a, b, c, len;

	/* Set up the internal state */
	len = length;
	a = b = 0x9e3779b9;  /* the golden ratio; an arbitrary value */
	c = 0;           /* the previous hash value */

	/*---------------------------------------- handle most of the key */
	while (len >= 12)
	{
		a += (k[0] + ((unsigned int)k[1] << 8) + ((unsigned int)k[2] << 16) + ((unsigned int)k[3] << 24));
		b += (k[4] + ((unsigned int)k[5] << 8) + ((unsigned int)k[6] << 16) + ((unsigned int)k[7] << 24));
		c += (k[8] + ((unsigned int)k[9] << 8) + ((unsigned int)k[10] << 16) + ((unsigned int)k[11] << 24));
		mix(a, b, c);
		k += 12; len -= 12;
	}

	/*------------------------------------- handle the last 11 bytes */
	c += length;
	switch (len)              /* all the case statements fall through */
	{
	case 11: c += ((unsigned int)k[10] << 24);
	case 10: c += ((unsigned int)k[9] << 16);
	case 9: c += ((unsigned int)k[8] << 8);
		/* the first byte of c is reserved for the length */
	case 8: b += ((unsigned int)k[7] << 24);
	case 7: b += ((unsigned int)k[6] << 16);
	case 6: b += ((unsigned int)k[5] << 8);
	case 5: b += k[4];
	case 4: a += ((unsigned int)k[3] << 24);
	case 3: a += ((unsigned int)k[2] << 16);
	case 2: a += ((unsigned int)k[1] << 8);
	case 1: a += k[0];
		/* case 0: nothing left to add */
	}
	mix(a, b, c);
	/*-------------------------------------------- report the result */
	return c;
}



//长度必须是4的倍数, 输入和输出都是block
void LuaInterfaceProxy::decrypt(void* block, unsigned int len) {
	if (len % 4 == 0) { xxtea_decrypt((unsigned int*)block, len, getXXTeaKey()); }
}


void LuaInterfaceProxy::setRentLuaLogFlag( int flag )
{
	m_rent_lua_log_flag = flag;
}


void LuaInterfaceProxy::log(const char* msg)
{
    if (std::strstr(msg, "MessageBoxError"))
        Rainbow::GetIClientInfo().SendLuaErrMsg(msg);

#ifdef IWORLD_DEV_BUILD
    if(std::strstr(msg, "MessageBoxError"))
        MINIW::PopMessageBox(msg, "lua error");

    LOG_INFO("@lua: %s\n", msg);

#elif !defined(IWORLD_SERVER_BUILD)
    LOG_INFO("@lua: %s\n", msg);

# if PLATFORM_ANDROID
    if (isdebug() && msg != nullptr)
    {
        LOG_INFO("@lua: %s\n", msg);
        __android_log_print(ANDROID_LOG_INFO, "LuaInterfaceProxy", "@lua: %s\n", msg);
    }
# elif PLATFORM_OHOS
    if (isdebug() && msg != nullptr)
    {
        LOG_INFO("@lua: %s", msg);
        //OHOS_LOGI("@lua: %s", msg); // enum LogLevel 与 google::protobuf::LogLevel 冲突
    }
# endif

#else
    //租赁服是否打印日志
    if (m_rent_lua_log_flag == 1) {
        LOG_INFO("@lua: %s\n", msg);
    }
#endif
}

// just for debug }}}

void LuaInterfaceProxy::androidLogcat(const char* text)
{
	//必然会打印的
	 printf_console(text);
}

std::string LuaInterfaceProxy::hash_sha512(const char* str)
{
	if (!str) return "";
#if OGRE_PLATFORM == OGRE_PLATFORM_ANDROID || PLATFORM_OHOS
	SHA512_CTX context;
	if (!SHA512_Init(&context))
	{
		return "";
	}
	if (!SHA512_Update(&context, str, strlen(str)))
	{
		return "";
	}
	unsigned char result[SHA512_DIGEST_LENGTH] = { 0 };
	if (!SHA512_Final(result, &context))
	{
		return "";
	}
	int iI;
	char* pcHexInternal = (char*)malloc(sizeof(char) * (SHA512_DIGEST_LENGTH + 1));
	pcHexInternal[0] = '\0';
	unsigned char* pucDigest = result;

	const char gcMapHex[] = "0123456789abcdefABCDEF";

	for (iI = 0; iI < SHA512_DIGEST_LENGTH; iI++)
	{
		*pcHexInternal++ = gcMapHex[(*pucDigest >> 4) & 0x0f];
		*pcHexInternal++ = gcMapHex[(*pucDigest++) & 0x0f];
	}

	*pcHexInternal = '\0';

	std::string strret = std::string(pcHexInternal);
	std::transform(strret.begin(), strret.end(), strret.begin(),
		[](unsigned char c) { return std::tolower(c); });

	free(pcHexInternal);

	return  strret;
#endif
	return str;

}

void LuaInterfaceProxy::callLuaString(const char* luaString)
{
	if (luaString == NULL)
	{
		return;
	}
	// TODO 需要修复
	std::string callstr = luaString;
	Rainbow::GetMainThreadJob().ScheduleMainThreadJobFunc<std::function<void()>>([callstr]() {
        OPTICK_CATEGORY("LuaInterfaceProxy::callLuaString.ScheduleMainThreadJobFunc", Optick::Category::Script);
		ScriptVM::game()->callString(callstr.c_str());
	});
}
//统一调用lua
void LuaInterfaceProxy::callLuaStringWithCallback(const char* funcName, const char* sessionId, const char* params)
{
    if (funcName == NULL || sessionId == NULL || params == NULL)
    {
        return;
    }
    std::string callFunc = funcName;
	std::string callSessionId = sessionId;
	std::string callParams = params;
    
    Rainbow::GetMainThreadJob().ScheduleMainThreadJobFunc<std::function<void()>>([callFunc, callSessionId, callParams]() {
        ScriptVM::game()->callStringWithCallback(callFunc.c_str(), callSessionId.c_str(), callParams.c_str(), 0);
    });
}
//回调callLuaStringWithCallback的接口
void LuaInterfaceProxy::responseLuaWithCallback(const char *funcName, const char *sessionId, const char *responseJson)
{
	MINIW::responseLuaWithCallback(funcName, sessionId, responseJson);
}


void LuaInterfaceProxy::showGameTips(const int& stringId) {
	if (this->m_VM) {
		char luaString[32];
		sprintf(luaString, "ShowGameTips(GetS(%d))", stringId);
		this->m_VM->callString(luaString);
	}
}

void LuaInterfaceProxy::showGameTips(const char* sz) {
	if (this->m_VM) {
		char luaString[128];
		sprintf(luaString, "ShowGameTips(\"%s\")", sz);
		this->m_VM->callString(luaString);
	}
}

void LuaInterfaceProxy::showGameTips(std::string str) {
	if (this->m_VM) {
		showGameTips(str.c_str());
	}
}

void LuaInterfaceProxy::showGameTips(int stringId, int type, bool withoutFilter/* = false*/)
{
	//LOG_INFO("showGameTips(): stringId = %d", stringId);
	std::stringstream luaStringStream;
	std::string luaString;
	if (withoutFilter)
	{
		luaStringStream << "ShowGameTipsWithoutFilter(GetS(" << stringId << ")," << type << ");";
	}
	else
	{
		luaStringStream << "ShowGameTips(GetS(" << stringId << ")," << type << ");";
	}
	luaStringStream >> luaString;
	callLuaString(luaString.c_str());
}

inline char Bin2Char(unsigned char c)
{
	return c < 10 ? '0' + c : c - 10 + 'a';
}

void LuaInterfaceProxy::toHex(char* hexbuf, const unsigned char* content, int len)
{
	for (int i = 0; i < len; i++)
	{
		unsigned char c = content[i];
		hexbuf[i * 2 + 0] = Bin2Char(c >> 4);
		hexbuf[i * 2 + 1] = Bin2Char(c & 15);
	}
	hexbuf[2 * len] = 0;
}

std::string LuaInterfaceProxy::aes_encryption(std::string key, std::string tmpIV, std::string strSrc)
{
	if (key.empty() || tmpIV.empty() || strSrc.empty())
	{
		return "";
	}
	size_t length = strSrc.length();
	int block_num = length / AES_BLOCK_SIZE + 1;
	int bufferLen = block_num * AES_BLOCK_SIZE + 1;
	//明文
	char* szDataIn = new char[bufferLen];
	memset(szDataIn, 0, bufferLen);
	strcpy(szDataIn, strSrc.c_str());

	//进行PKCS7Padding填充。
	int k = length % AES_BLOCK_SIZE;
	int j = length / AES_BLOCK_SIZE;
	int padding = AES_BLOCK_SIZE - k;
	for (int i = 0; i < padding; i++)
	{
		szDataIn[j * AES_BLOCK_SIZE + k + i] = padding;
	}
	szDataIn[block_num * AES_BLOCK_SIZE] = '\0';

	//加密后的密文
	char* szDataOut = new char[bufferLen];
	memset(szDataOut, 0, bufferLen);
	AES aes;
	aes.MakeKey(key.c_str(), tmpIV.c_str(), AES_BLOCK_SIZE, AES_BLOCK_SIZE);
	aes.Encrypt(szDataIn, szDataOut, block_num * AES_BLOCK_SIZE, AES::CBC);
	char* rOut = new char[2 * (block_num * AES_BLOCK_SIZE) + 1];
	memset(rOut, 0, 2 * (block_num * AES_BLOCK_SIZE) + 1);
	toHex(rOut, (const unsigned char*)szDataOut, block_num * AES_BLOCK_SIZE);
	std::string retValue(rOut);
	OGRE_DELETE_ARRAY(szDataIn)
		OGRE_DELETE_ARRAY(szDataOut)
		OGRE_DELETE_ARRAY(rOut)
		return retValue;
}

std::string LuaInterfaceProxy::aes_decryption(std::string key, std::string iv, std::string pInBuf)
{
	if (key.empty() || iv.empty() || pInBuf.empty())
	{
		return "";
	}
	size_t length = pInBuf.length();
	//密文
	char* szDataIn = new char[length + 1];
	memcpy(szDataIn, pInBuf.c_str(), length + 1);
	//明文
	char* szDataOut = new char[length + 1];
	memset(szDataOut, 0, length + 1);

	//进行AES的CBC模式解密
	AES aes;
	aes.MakeKey(key.c_str(), iv.c_str(), AES_BLOCK_SIZE, AES_BLOCK_SIZE);
	aes.Decrypt(szDataIn, szDataOut, length, AES::CBC);

	//去PKCS7Padding填充
	if (0x00 < szDataOut[length - 1] && szDataOut[length - 1] <= 0x16)
	{
		int tmp = szDataOut[length - 1];
		for (size_t i = length - 1; i >= length - tmp; i--)
		{
			if (szDataOut[i] != tmp)
			{
				memset(szDataOut, 0, length);
				cout << "去填充失败！解密出错！！" << endl;
				break;
			}
			else
				szDataOut[i] = 0;
		}
	}
	string strDest(szDataOut);
	OGRE_DELETE_ARRAY(szDataIn)
		OGRE_DELETE_ARRAY(szDataOut)
		return strDest;
}

// 仅支持简单数据类型
int LuaInterfaceProxy::event_trigger(const char* funcname, const char* msgid, const ObserverEvent* obevent)
{


	//{{{
	int ret = 0;
	if (!MINIW::ScriptVM::IsVaild())
	{
		return ret;
	}

	lua_State* L = this->m_VM->getLuaState();
	if (!ScriptVM::isCurrentThreadIsMainThread(L))
	{
		return ret;
	}
	int n = lua_gettop(L);

	lua_getglobal(L, funcname);
	if (!lua_isfunction(L, -1))
	{
		lua_settop(L, n);
		return 0;
	}

	lua_pushstring(L, msgid); // 参数1
	lua_pushlightuserdata(L, (void*)obevent); // 参数2

    if (lua_pcall(L, 2, 1, 0) == 0)
    {
        // if(lua_isnumber(L, -1))
		// 	n = (int)lua_tonumber(L, -1);
    }
	else
	{
		LOG_SEVERE("lua_pcall error: %s", lua_tostring(L, -1));
	}

	lua_settop(L, n);
	return ret;
	//}}}
}

void LuaInterfaceProxy::event_trigger_begin()
{
	m_events.Clear();
}

void LuaInterfaceProxy::event_trigger_add(const char* msgid, ObserverEvent* obevent)
{
	m_events.PushEvent(obevent, msgid);
}

void LuaInterfaceProxy::event_trigger_end(const char* funcname)
{
	lua_State* L = this->m_VM->getLuaState();
	if (!ScriptVM::isCurrentThreadIsMainThread(L))
	{
		return;
	}
	int n = lua_gettop(L);

	lua_getglobal(L, funcname);
	if (!lua_isfunction(L, -1))
	{
		lua_settop(L, n);
		return;
	}

	lua_pushstring(L, m_events.GetEventsNames()); // 参数1
	lua_pushlightuserdata(L, (void*)(&m_events)); // 参数2
	lua_pcall(L, 2, 1, 0);
	lua_settop(L, n);
	m_events.Clear();
}

bool LuaInterfaceProxy::shouldUseNewHpRule()
{
	if (!g_WorldMgr)
	{
		return false;
	}
	ConstAtLua* v = get_lua_const();
	if (v->check_new_hp_rule == 0)
	{
		return false;
	}
	else if (v->check_new_hp_rule == 1)
	{
		return true;
	}
	else if (v->check_new_hp_rule == 2)
	{
		if (g_WorldMgr->isCreativeMode() || g_WorldMgr->isExtremityMode() || g_WorldMgr->isFreeMode()
			|| g_WorldMgr->isSurviveMode() || g_WorldMgr->isCreateRunMode())
		{
			return true;
		}
	}
	else if (v->check_new_hp_rule == 3)
	{
		if (g_WorldMgr->isGameMakerMode() || g_WorldMgr->isGameMakerRunMode())
		{
			return true;
		}
	}
	else
	{
		return false;
	}
	return false;
}

void LuaInterfaceProxy::SaveToFile(std::string filePath, std::string content)
{
#if PLATFORM_WIN
	std::wstring wideFilePath = Utf8ToWide(filePath);
	FILE* srcfile = _wfopen(wideFilePath.c_str(), L"w+");
#else
	FILE* srcfile = fopen(filePath.c_str(), "w+");
#endif

	fwrite(content.c_str(), content.length(), 1, srcfile);
	fclose(srcfile);
}

std::string LuaInterfaceProxy::ReadFromFile(std::string filePath)
{
	std::string res;
#if PLATFORM_WIN
	std::wstring wideFilePath = Utf8ToWide(filePath);
	FILE* srcfile = _wfopen(wideFilePath.c_str(), L"rb");
#else
	FILE* srcfile = fopen(filePath.c_str(), "rb");
#endif

	if (srcfile == NULL)
	{
		return "";
	}

	fseek(srcfile, 0, SEEK_END);
	long filesize = ftell(srcfile);

	char* buffer = new char[filesize];
	fseek(srcfile, 0, SEEK_SET);
	if (fread(buffer, 1, filesize, srcfile) != filesize)
	{
		delete[]buffer;
		fclose(srcfile);
		return "";
	}

	fclose(srcfile);

	res.assign(buffer, filesize);
	delete[]buffer;

	return res;
}

//IMPLEMENT_GETMETHOD_MANUAL_INIT(LuaInterfaceProxy)
LuaInterfaceProxy& GetLuaInterfaceProxy()
{
	return *s_instance;
}

LuaInterfaceProxy* GetLuaInterfaceProxyPtr()
{
	return s_instance;
}

SocConstAtLua* LuaInterfaceProxy::get_soc_lua_const()
{
	return &m_soc_const;
}
