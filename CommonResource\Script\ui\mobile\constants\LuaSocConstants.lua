--一些常量
local LuaSocConstants = {

}
_G.LuaSocConstants = LuaSocConstants;
function LuaSocConstants:get()
    return {
        soctestvalue = 100001,                  --模板

        vLinkBlockIdList = {
            10001001,--1对1的连接方块id
            10001002,--1个源头对多个目标的连接方块id
            10001003,--多个源头对1个目标的连接方块id
        },
        vTargetIdList = {
            10001042,
            10001043,
            10001044,
        },
        vSourceList = {
            {
                source_id = 10001041,
                rangex = 100,
                rangey = 100,
                rangez = 100,
            },
            {
                source_id = 10001051,
                rangex = 100,
                rangey = 100,
                rangez = 100,
            },
        },
        vRandNpcList = {
           [10001061] = {
                [10001061] = {
                    Probability = 10,
                },
                [10001062] = {
                    Probability = 10,
                },
            }, 
            [10001062] = {
                [10001062] = {
                    Probability = 10,
                },
                [10001063] = {
                    Probability = 10,
                },
            },
        },
--[[ 留下作为数据传导格式参考
        vOneOnOneLinkList = {
            {
                source_id = 10001021,
                target_id = 10001022,
            },
            {
                source_id = 10001031,
                target_id = 10001032,
            },
        },
        vOneSourceToManyTargetLinkList = {
            {
                source_id = 10001041,
                target_id = {10001042,10001043,10001044},
            },
        },
        vOneTargetToManySourceLinkList = {
            {
                source_id = {10001052,10001053,10001054},
                target_id = 10001051,
            },
        },
        vOneToManyRangeList = {
            {
                source_id = 10001041,
                target_id = {10001042,10001043,10001044},
                rangex = 100,
                rangey = 100,
                rangez = 100,
            },
            {
                source_id = 10001051,
                target_id = {10001052,10001053,10001054},
                rangex = 100,
                rangey = 100,
                rangez = 100,
            },
        },
]]
    }
end



function LuaSocConstants:load()
    if type(LuaInterface.get_soc_lua_const) ~= 'function' then 
        return
    end 
    local t = self:get();
    local c = LuaInterface:get_soc_lua_const()
    if not t or not c then 
        return
    end 
    for k, v in pairs(t) do
        if type(v) ~= 'table' then
            if c[k] ~= nil then 
                c[k] = v 
            end 
        else 
            -- 处理vector类型的数据，将Lua table导入到C++的vector中
            if k == "vLinkBlockIdList" or k == "vTargetIdList" then
                if c[k] ~= nil and type(v) == 'table' then
                    -- 清空原有数据
                    c[k]:clear()
                    -- 遍历Lua table，将每个元素添加到C++的vector中
                    for _, blockId in ipairs(v) do
                        if type(blockId) == 'number' then
                            c[k]:push_back(blockId)
                        end
                    end
                end
            end
            -- 处理vSourceList：源列表
            if k == "vSourceList" then
                if type(v) == 'table' then
                    -- 遍历Lua table，通过函数添加数据到C++的vector中
                    for _, sourceData in ipairs(v) do
                        if type(sourceData) == 'table' then
                            local source_id = sourceData.source_id or 0
                            local rangex = sourceData.rangex or 0
                            local rangey = sourceData.rangey or 0
                            local rangez = sourceData.rangez or 0
                            c:addSourceData(source_id, rangex, rangey, rangez)
                        end
                    end
                end
            end

            -- 处理vRandNpcList：随机NPC列表（嵌套map结构）
            if k == "vRandNpcList" then
                if type(v) == 'table' then
                    -- 遍历外层table (outer_key -> inner_table)
                    for outer_key, inner_table in pairs(v) do
                        if type(outer_key) == 'number' and type(inner_table) == 'table' then
                            -- 遍历内层table (inner_key -> data)
                            for inner_key, data in pairs(inner_table) do
                                if type(inner_key) == 'number' and type(data) == 'table' then
                                    local probability = data.Probability or 0
                                    -- 调用C++函数添加随机NPC数据
                                    c:addRandNpc(outer_key, inner_key, probability)
                                end
                            end
                        end
                    end
                end
            end
--[[ 留下作为数据传导格式参考
             -- 处理vOneOnOneLinkList：一对一关联列表
            if k == "vOneOnOneLinkList" then
                if type(v) == 'table' then
                    -- 遍历Lua table，通过函数添加数据到C++的vector中
                    for _, linkData in ipairs(v) do
                        if type(linkData) == 'table' then
                            local source_id = linkData.source_id or 0
                            local target_id = linkData.target_id or 0
                            -- 调用C++函数添加一对一关联数据
                            c:addOneOnOneLink(source_id, target_id)
                        end
                    end
                end
            end
            if k == "vOneSourceToManyTargetLinkList" then
                if type(v) == 'table' then
                    for _, linkData in ipairs(v) do
                        if type(linkData) == 'table' then
                            local source_id = linkData.source_id or 0
                            c:addOneSourceToManyTarget(source_id)
                            if linkData.target_id and type(linkData.target_id) == 'table' then
                                for _, targetId in ipairs(linkData.target_id) do
                                    if type(targetId) == 'number' then
                                        c:addTargetIdToLastOneSourceToMany(targetId)
                                    end
                                end
                            end
                        end
                    end
                end
            end
            if k == "vOneTargetToManySourceLinkList" then
                if type(v) == 'table' then
                    for _, linkData in ipairs(v) do
                        if type(linkData) == 'table' then
                            local target_id = linkData.target_id or 0
                            c:addOneTargetToManySource(target_id)
                            if linkData.source_id and type(linkData.source_id) == 'table' then
                                for _, sourceId in ipairs(linkData.source_id) do
                                    if type(sourceId) == 'number' then
                                        c:addSourceIdToLastOneTargetToMany(sourceId)
                                    end
                                end
                            end
                        end
                    end
                end
            end
            -- 处理vOneToManyRangeList：一对多范围列表
             if k == "vOneToManyRangeList" then
                 if type(v) == 'table' then
                     -- 遍历Lua table，通过函数添加数据到C++的vector中
                     for _, rangeData in ipairs(v) do
                         if type(rangeData) == 'table' then
                             local source_id = rangeData.source_id or 0
                             local rangex = rangeData.rangex or 0
                             local rangey = rangeData.rangey or 0
                             local rangez = rangeData.rangez or 0
                             -- 调用C++函数添加一对多范围数据
                             c:addOneToManyRange(source_id, rangex, rangey, rangez)
                             -- 处理target_id数组
                             if rangeData.target_id and type(rangeData.target_id) == 'table' then
                                 for _, targetId in ipairs(rangeData.target_id) do
                                     if type(targetId) == 'number' then
                                         c:addTargetIdToLastOneToMany(targetId)
                                     end
                                 end
                             end
                         end
                     end
                 end
             end
]]
        end
    end
end

