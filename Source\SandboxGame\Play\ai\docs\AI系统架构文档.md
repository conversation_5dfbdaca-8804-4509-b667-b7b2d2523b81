# NPC AI 系统架构文档

## 目录

1. [系统概述](#系统概述)
2. [整体架构](#整体架构)
3. [传统 AI 系统](#传统ai系统)
4. [现代行为树系统](#现代行为树系统)
5. [服务器同步机制](#服务器同步机制)
6. [AI 行为示例](#ai行为示例)
7. [开发指南](#开发指南)

---

## 系统概述

### 核心特征

- **服务器端驱动**: AI 逻辑 100%在服务器端执行
- **双重架构**: 传统 AITask 系统 + 现代 BehaviorTree 系统并存
- **权威同步**: 服务器权威，客户端表现
- **性能优化**: 距离优化、分帧执行、按需激活

### 设计目标

- 防作弊：AI 逻辑服务器权威
- 一致性：所有玩家看到相同行为
- 扩展性：支持复杂 AI 逻辑配置
- 性能：优化网络同步和计算负载

---

## 整体架构

### 架构层次

```
ClientMob (怪物实体)
    ├── 传统AI系统
    │   ├── AITask (任务管理器)
    │   └── AIBase子类 (具体AI行为)
    └── 现代行为树系统
        ├── BTreeIns (行为树实例)
        ├── BTBlackboard (黑板数据)
        └── BTNodeBase (行为树节点)
```

### 核心组件关系

```cpp
class ClientMob : public ActorLiving
{
private:
    // 传统AI任务系统
    AITask* m_AITask;           // 主要AI任务
    AITask* m_AITaskTarget;     // 目标AI任务

    // 现代行为树系统
    BTreeIns* m_btree;          // 行为树实例
    BTBlackboard* m_bb;         // 黑板数据
    bool m_isUseAILua;          // 是否使用Lua AI
};
```

### AI 更新流程

```cpp
void ClientMob::MobTick()
{
    if (m_pWorld->isRemoteMode()) return;  // 🔑 只在服务器端执行

    if(m_NeedUpdateAI)
        AITick();
}

void ClientMob::AITick()
{
    if (!m_isUseAILua)
    {
        // 传统AI系统更新
        if (m_AITask) m_AITask->onUpdateTasks();
        if (m_AITaskTarget) m_AITaskTarget->onUpdateTasks();
    }

    // 行为树系统更新
    aiTick();
}
```

---

## 传统 AI 系统

### 核心组件

#### AIBase (AI 行为基类)

```cpp
class AIBase
{
public:
    virtual bool willRun() = 0;        // 是否应该运行
    virtual bool continueRun();        // 是否继续运行
    virtual bool isInterruptible();    // 是否可被中断
    virtual void start() {}            // 开始执行
    virtual void reset() {}            // 重置状态
    virtual void update() {}           // 每帧更新
    virtual AI_MOTION_TYPE getMotionType();

    void setMutexBits(int iMutex);     // 设置互斥位
    int getMutexBits();
};
```

#### AITask (任务管理器)

```cpp
class AITask
{
public:
    void addTask(int iPriority, AIBase *pAIBase);    // 添加任务
    void removeTask(AIBase *pAIBase);                // 移除任务
    void onUpdateTasks();                            // 更新所有任务

private:
    std::vector<AITaskEntry> m_TaskEntries;          // 所有任务
    AITaskEntryTable m_RunningTaskEntries;           // 正在运行的任务
    ClientMob *m_pMobActor;                          // 关联的Mob
};
```

### 任务调度机制

1. **优先级调度**: 数字越小优先级越高
2. **互斥机制**: 通过 MutexBits 防止冲突行为
3. **生命周期管理**: willRun() → start() → update() → reset()
4. **时间片调度**: 每 3 帧检查一次任务状态

### 常用 AI 行为类型

- **AIWander**: 随机漫游
- **AIAtk**: 攻击行为
- **AIFierce**: 威胁恐吓
- **AIWatchClosest**: 观察最近目标
- **AITargetNearest**: 寻找最近目标
- **AIFollowOwner**: 跟随主人
- **AIPanic**: 恐慌逃跑

---

## 现代行为树系统

### 核心组件

#### BTreeIns (行为树实例)

```cpp
class BTreeIns
{
public:
    void Tick();                    // 执行行为树
    void SetActive(bool active);    // 设置激活状态
    void AttachObj(ClientActor* target);  // 绑定目标对象
    void SetBlackboard(BTBlackboard* bb); // 绑定黑板

private:
    BTNodeBase* m_btroot;          // 根节点
    ClientActor* m_target;         // 目标对象
    BTBlackboard* m_blackboard;    // 黑板数据
    bool m_bActive;                // 是否激活
};
```

#### BTNodeBase (节点基类)

```cpp
class BTNodeBase : public BTObj
{
public:
    virtual BTNODERESULT Run(const BTNodeRunData& rundata);
    virtual void Reset();

    virtual bool RunBefore(const BTNodeRunData& rundata);
    virtual BTNODERESULT RunActive(const BTNodeRunData& rundata) = 0;
    virtual void RunBack(const BTNodeRunData& rundata);

    virtual bool SetParam(const std::string& key, const BTLuaData& data);
};
```

### 节点类型体系

#### 逻辑节点 (BTNodeLogic)

- **Selector**: 选择器，依次执行直到成功
- **Sequence**: 序列器，依次执行直到失败
- **Parallel**: 并行器，同时执行多个子节点
- **Random**: 随机器，随机选择子节点

#### 任务节点 (BTNodeTask)

- **BTNTaskAttack**: 攻击任务
- **BTNTaskMoveTo**: 移动任务
- **BTNTaskFollow**: 跟随任务
- **BTNTaskWander**: 漫游任务

#### 装饰节点 (BTNodeSingle)

- **BTNodeLoop**: 循环执行
- **BTNodeInversion**: 结果反转
- **BTDecorator**: 条件装饰器

### 黑板系统 (BTBlackboard)

```cpp
class BTBlackboard : public BTObj
{
public:
    bool SetDataLua(const char* key, const BTLuaData* pluadata);
    bool GetDataLua(const char* key, BTLuaData*& pluadata);

    bool GetData_Number(const char* key, int& out);
    bool GetData_Vector3(const char* key, Rainbow::Vector3f& out);
    bool GetData_Object(const char* key, long long& out);

    bool SetByString(const std::string& data);
    std::string ToString();
};
```

### 性能优化策略

#### 距离优化

```cpp
void BTreeIns::Tick()
{
    // 根据与玩家距离调整更新频率
    if (iDistanceMin > 40960000)      // 6400*6400
    {
        if (++m_iDisTickStep < 6) return;  // 每6帧更新一次
    }
    else if (iDistanceMin > 10240000) // 3200*3200
    {
        if (++m_iDisTickStep < 2) return;  // 每2帧更新一次
    }

    BTNodeRunData rundata(GetInstanceID(), m_target, m_blackboard, 0.05f);
    m_btroot->Run(rundata);
}
```

---

## 服务器同步机制

### 架构原理

- **服务器权威**: AI 决策完全在服务器端
- **客户端表现**: 仅负责接收同步和渲染
- **状态同步**: 通过网络协议同步 AI 结果

### 关键判断机制

```cpp
void ClientMob::MobTick()
{
    if (m_pWorld->isRemoteMode()) return;  // 远程模式(客户端)不执行AI
    // AI逻辑只在服务器端执行
}
```

### 同步消息示例

```cpp
void ClientMob::notifyBodyChange()
{
    if (m_pWorld && !m_pWorld->isRemoteMode())  // 只在服务器端发送
    {
        PB_MobBodyChangeHC mobBodyChangeHC;
        mobBodyChangeHC.set_objid(getObjId());
        GetGameNetManagerPtr()->sendBroadCast(PB_MOB_BODY_CHANGE_HC, mobBodyChangeHC);
    }
}
```

### 同步流程

```
服务器端AI决策:
AIFierce::willRun() → 检测到玩家 → 返回true
    ↓
AIFierce::start() → 播放威胁动画 → 面向玩家
    ↓
发送同步消息: PB_MOB_ANIM_HC { objid, anim_id, target_pos }
    ↓
客户端接收: 播放威胁动画 + 转向目标位置
```

### 网络优化

- **按需同步**: 只同步状态变化
- **批量发送**: 合并多个状态更新
- **插值平滑**: 客户端位置插值
- **距离裁剪**: 远距离玩家不接收详细 AI 状态

---

## AI 行为示例

### AIFierce (威胁行为) 详解

```cpp
class AIFierce : public AIBase
{
public:
    AIFierce(ClientMob *pActor, int prob, float aniTime = 3.0f);

    virtual bool willRun();      // 触发条件检查
    virtual bool continueRun();  // 持续条件检查
    virtual void start();        // 开始执行
    virtual void reset();        // 重置状态

private:
    WORLD_ID m_Target;    // 目标玩家ID
    int m_Prob;           // 触发概率
    float m_aniTime;      // 动画持续时间
    int m_nAiEndTime;     // 结束倒计时
};
```

#### 行为特征

- **不移动**: 原地威胁
- **不攻击**: 纯威胁展示
- **面向目标**: 转身面对玩家
- **播放动画**: SEQ_FOX_FIERCE

#### 触发条件

1. 视野内有活着的玩家
2. 随机概率检查通过 (1/m_Prob)
3. 没有其他互斥行为执行

#### 执行流程

```cpp
bool AIFierce::willRun()
{
    // 1. 计算视野范围
    float vr = m_pMobActor->getDef()->ViewDistance * BLOCK_SIZE;

    // 2. 寻找最近玩家
    ClientPlayer* player = m_pMobActor->getActorMgr()->selectNearPlayer(
        m_pMobActor->getPosition(), vr);
    if (!player || player->isDead()) return false;

    // 3. 概率检查
    if(GenRandomInt(m_Prob) != 0) return false;

    // 4. 记录目标
    m_Target = player->getObjId();
    return true;
}

void AIFierce::start()
{
    // 设置持续时间
    m_nAiEndTime = (int)floor(m_aniTime * 20);

    // 停止移动
    m_pMobActor->getNavigator()->clearPathEntity();

    // 播放威胁动画
    m_pMobActor->playAnim(SEQ_FOX_FIERCE);

    // 面向目标玩家
    ClientPlayer* targetPlayer = m_pMobActor->getActorMgr()->findPlayerByUin(m_Target);
    if (targetPlayer && !targetPlayer->isDead())
    {
        // 计算朝向并转身
        Rainbow::Vector3f tempDir = (targetPlayer->getPosition() -
                                   m_pMobActor->getPosition()).toVector3();
        float botYaw = 0.f, botPitch = 0.f;
        Direction2PitchYaw(&botYaw, &botPitch, tempDir);
        m_pMobActor->getBody()->rotateTo(botYaw);
    }
}
```

---

## 开发指南

### 创建最简单的 AI 怪物

#### 方法 1: 传统 AITask 方式

```cpp
void ClientMob::initSimpleMonsterAI()
{
    if (nullptr == m_AITask)
    {
        m_AITask = ENG_NEW(AITask)(this);
    }

    // 添加AI行为 (优先级从高到低)
    addAiTask<AIAtk>(1, 0, true, 1.2f);           // 攻击行为
    addAiTask<AIWatchClosest>(2, 8, 50);          // 观察行为
    addAiTask<AIWander>(3, 1.0f, 120);            // 漫游行为

    // 添加目标选择
    if (nullptr == m_AITaskTarget)
    {
        m_AITaskTarget = ENG_NEW(AITask)(this);
    }
    addAiTaskTarget<AITargetNearest>(1, 100, true, 0.0f);
}
```

#### 方法 2: BehaviorTree 方式

```lua
-- MOB_9999.lua
local BTree = {}

function BTree:CreateTree()
    local root = self:CreateNode("BTNodeSelector", nil, {
        {val = 2030001, paramtype = 4}
    })

    -- 攻击序列
    local attackSequence = self:CreateNode("BTNodeSequence", root, {})
    local hasTarget = self:CreateNode("BTNTaskConditions", attackSequence, {
        {val = "HasAttackTarget", paramtype = 1}
    })
    local attack = self:CreateNode("BTNTaskAttack", attackSequence, {
        {val = "AttackTarget", paramtype = 1},
        {val = 60, paramtype = 4}
    })

    -- 默认漫游
    local wander = self:CreateNode("BTNTaskWander", root, {
        {val = 1.0, paramtype = 2},
        {val = 10, paramtype = 4},
        {val = 120, paramtype = 4}
    })

    return root
end

return BTree
```

### 调试建议

```cpp
void DebugMonsterAI(ClientMob* pMob)
{
    printf("=== 怪物AI调试信息 ===\n");
    printf("怪物ID: %d\n", pMob->getDefID());
    printf("血量: %.1f/%.1f\n", pMob->getHealth(), pMob->getMaxHealth());

    if (pMob->m_AITask)
    {
        printf("AI任务数量: %d\n", pMob->m_AITask->getTaskCount());
        printf("正在运行的任务: %d\n", pMob->m_AITask->getRunningTaskCount());
    }

    ClientActor* target = pMob->getAttackTarget();
    if (target)
    {
        printf("攻击目标: %s\n", target->getName().c_str());
    }
}
```

### 最佳实践

1. **优先级设计**: 攻击(1) > 观察(2) > 漫游(3)
2. **互斥设计**: 相冲突的行为设置相同 MutexBits
3. **性能考虑**: 合理设置触发概率和更新频率
4. **调试支持**: 添加详细的调试输出
5. **配置驱动**: 使用 JSON/Lua 配置而非硬编码

---

## 总结

这个 AI 系统采用了现代游戏开发的最佳实践：

- **服务器权威架构**确保公平性和一致性
- **双重 AI 系统**兼顾兼容性和扩展性
- **完善的同步机制**保证网络性能
- **丰富的行为类型**支持复杂 AI 逻辑
- **优秀的性能优化**适应大规模场景

通过这套系统，开发者可以快速创建从简单到复杂的各种 AI 行为，为游戏提供丰富的 NPC 交互体验。
