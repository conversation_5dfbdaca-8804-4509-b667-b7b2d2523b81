--[[
    云服主机初始化ConstAtLua中定义数据，非云服主机不要加载此文件
]]
local CJson = require("cjson")

-- 初始化ConstAtLua参数函数
local initConstData = function ()
    if type(LuaInterface.get_lua_const) == 'function' then
        if LuaConstants and LuaConstants.load then
            LuaConstants:load();
            return
        end

        local t = loadwwwcache('res.const')
        local c = LuaInterface:get_lua_const()
        if t and c then
            for k, v in pairs(t) do
                if type(v) ~= 'table' then if c[k] ~= nil then c[k] = v end end 
            end
            gFunc_SLOG('cloudServerHost initConstData success ')
        else
            gFunc_SLOG('cloudServerHost initConstData failed, no t or c')
        end
    else
        gFunc_SLOG('cloudServerHost initConstData failed, not get_lua_const')
    end
end
-- 初始化SocConstAtLua参数函数
local initSocConstData = function ()
    if type(LuaInterface.get_lua_soc_const) == 'function' then
        if LuaSocConstants and LuaSocConstants.load then
            LuaSocConstants:load();
            return
        end
        gFunc_SLOG('cloudServerHost initSocConstData failed, not get_lua_soc_const')
        -- local t = loadwwwcache('res.soc_const')
        -- local c = LuaInterface:get_lua_soc_const()
        -- if t and c then
        --     for k, v in pairs(t) do
        --         if type(v) ~= 'table' then if c[k] ~= nil then c[k] = v end end 
        --     end
        --     gFunc_SLOG('cloudServerHost initSocConstData success ')
        -- else
        --     gFunc_SLOG('cloudServerHost initSocConstData failed, no t or c')
        -- end
    else
        gFunc_SLOG('cloudServerHost initSocConstData failed, not get_lua_soc_const')
    end
end


-- 确定需要的配置，需要在加载之前
local NeedWWWConfigBeforeLoad = nil

-- 一些服务器的全局数据可以放这里
tbServerGlobalData = {
    mapserver = nil,  -- mapserver地址
}


function IsServerConfigOk()
    if PlatformUtility:isStuioServer() then --本地云服不需要获取服务器配置
        return true
    end

    -- 确定需要的配置，需要在加载之前
    if not NeedWWWConfigBeforeLoad then
        NeedWWWConfigBeforeLoad = {
            -- trigger_config = 1,
        }
        -- 海外需要额外等待trigger_config_ex
        if get_game_env() >= 10 then
            -- NeedWWWConfigBeforeLoad.trigger_config_ex = 1
        end
    end

    for k,v in pairs(NeedWWWConfigBeforeLoad) do
        if not WWWConfigLoaded[k] then
            return false
        end
    end
    return true
end

-- 检测特定的uin配置 server_config.lua配置
function CheckCanServerCmd(uin)
    for i,value in ipairs(ServerGMUin) do
        if value == uin then
            return true
        end
    end
    return false
end

-- 初始化advert_config数据
local GetConfigInfoData = function()
    threadpool:work(function()
        -- 云服config数据拉取
        WWW_get_cf_info_first();

        -- 多次步长检测，不要傻等5秒
        local count = 0
        while (true) do
            threadpool:wait(0.5)
            count = count + 0.5
            if count > 5.0 or ns_data.info_update_finished then
                break
            end
        end

        WWW_miniui_trigger_config()

        -- 拉取公告类型活动的数据
        WWW_file_rent_advert_config()
        WWW_file_business_weapon_config()
        -- 获取ns_version内容 审核人员配置在这里
        WWW_file_version_server()

        WWW_file_cloud_server()

--        WWW_file_role_skill_config() -- 特长配置
    end)
end

-- 服务器对ns_version的处理不一样，这里使用新的处理函数
function WWW_file_version_callback_server(ret)
    WWWConfigLoaded['version'] = true
    if type(ret) == 'table' then
        ns_version = ret or {}
        if zmqMgr_ and zmqMgr_.SetPersonalRoomTimeoutParam and ns_version
            and ns_version.personal_rent_timeout then
            local t_ = ns_version.personal_rent_timeout
            zmqMgr_:SetPersonalRoomTimeoutParam(t_.freeSec1 or 0, t_.freeSec2 or 0,
                                                t_.maxSec or 0, t_.keepSec or 0)
        end

    end
end

function WWW_file_version_server()
    local file_name_, download_  = getLuaConfigFileInfo( "version" );
    ns_http.func.downloadLuaConfig( file_name_, download_, ns_data.cf_md5s['version'],   WWW_file_version_callback_server,  "cdn" );
end

-- 房间通行证
local GetPassportInformation = function()
    local worldDesc = AccountManager:getCurWorldDesc();
    Log("GetPassportInformation")
    if worldDesc then
        Log("GetPassportInformation worldname, owneruin, realowneruin，passportflag"..worldDesc.worldname..worldDesc.owneruin..worldDesc.realowneruin..worldDesc.passportflag)
        CheckPassPortInfo(worldDesc)
    end
end
local cloudServerHostLoad = function()
    -- 初始化ConstAtLua参数
    initConstData()
    -- 初始化SocConstAtLua参数
    initSocConstData()
    -- 云服server数据初始化
    InitServerUrls();
    GetConfigInfoData()

    GetPassportInformation()
end

function getServerToken( cmd_ )
    local  time_  = os.time()
    local  token_ = gFunc_getmd5("1000#LY1006#" .. time_ .. '#' .. cmd_ .. '#')
    local  full_auth = "&uin=1000" .. "&time=" .. time_ .. "&token=" .. token_
    return full_auth
end

local account_def = loadwwwcache('account_def')
function cloudServerReloadAccountDef()
    account_def = loadwwwcache('account_def', true)    
end

function urlEncode(s)
    s = string.gsub(s, "([^%w%.%- _])", function(c) return string.format("%%%02X", string.byte(c)) end)
   return string.gsub(s, " ", "+")
end

function getCloudMapServerAddr()
    if tbServerGlobalData.mapserver then
        return tbServerGlobalData.mapserver
    end
    if zmqMgr_ and zmqMgr_.GetBackendServiceInfo then
        local ip1_ = ""
        local port1_ = 100
        local ret_, ip_, port_ = zmqMgr_:GetBackendServiceInfo("mapserver", ip1_, port1_)
        print("mapserver.tcp.miniw.env", ret_, ip_, port_)
        if ret_ then
            tbServerGlobalData.mapserver = 'http://' .. ip_ .. ':' .. port_
        end
    end
    return tbServerGlobalData.mapserver
end

-- 服务器上报mod的配置信息 用于运营系统
function uploadModInfos(mapid, mod_info_str)
    --print("uploadModInfos", mapid)
    if mapid and mod_info_str then
        local mapserverUrl = getCloudMapServerAddr()
        print("mapserver addr", tostring(mapserverUrl))
        if mapserverUrl then
            local url = mapserverUrl .. '/v1/map/set_mod_info'
            local bodydata = 'aid=' .. mapid .. '&mod_info=' .. urlEncode(mod_info_str)
            --print(bodydata)

            ns_http.func.rpc_do_http_post(url, nil, nil, bodydata)
            --ScriptSupportSetting:writeFile("test_set_mod_info.txt", bodydata, true)
        end
    end
end

function cloudServerGetSkincostdef()
    return account_def.SkinCostDef    
end

-- 空实现，避免服务器函数找不到报错
function OnChangeNumOfPlayers(playerNum)
end
function ReqCurUseAchieveByUin(uin)
end
function BattleCountDown(code, isrocket)
end

return cloudServerHostLoad
