
#ifndef __AITASK_H__
#define __AITASK_H__

#include <vector>
#include "OgreHashTable.h"
#include <map>
#include "AIBase.h"
#include <math.h>
#include "SandboxGame.h"

class AIBase;
struct AITaskEntry //tolua_exports
{ //tolua_exports
	//tolua_begin
	AITaskEntry():m_pAIBase(nullptr),m_bPriority(0) {};
	AITaskEntry(int iPriority, AIBase *pAIBase):m_bPriority(iPriority),m_pAIBase(pAIBase) {};
	//tolua_end
	bool operator==(const AITaskEntry &other) const
	{
		if ((other.m_pAIBase == m_pAIBase) && (other.m_bPriority == m_bPriority))
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	//tolua_begin
	AIBase *m_pAIBase;
	unsigned char m_bPriority;
	//tolua_end
}; //tolua_exports

struct AITaskEntryHashCoder
{
	uintptr_t operator()(const AITaskEntry &entry) const
	{
		return (uintptr_t)entry.m_pAIBase << 2;
	}

	bool operator()(const AITaskEntry &entry1, const AITaskEntry &entry2) const
	{
		return entry1 == entry2;
	}
};

typedef Rainbow::HashTable<AITaskEntry, bool, AITaskEntryHashCoder> AITaskEntryTable;
class EXPORT_SANDBOXGAME AITask;
class AITask //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	AITask(ClientMob *mob);
	~AITask();
	void addTask(int iPriority, AIBase *pAIBase);
	void removeTask(AIBase *pAIBase);
	void onUpdateTasks();
	void clearAllRunningTasks();
	bool canInterruptedByInteract();
	void clearAllTasks();
	//tolua_end
	void OnCollideWithPlayer(ClientPlayer* player);
	template<typename T>
	AIBase* getTaskAI()
	{
		auto iter = m_TaskEntries.begin();
		while (iter != m_TaskEntries.end())
		{
			if (dynamic_cast<T*>(iter->m_pAIBase))
			{
				return iter->m_pAIBase;
			}
			else
			{
				iter++;
			}
		}

		return nullptr;
	}
private:
	bool canContinue(AITaskEntry &stAITaskEntry);
	bool canUse(AITaskEntry &stAITaskEntry);
	bool areTasksCompatible(AIBase *pAIBase1, AIBase *pAIBase2);
	bool isEntryRunning(const AITaskEntry &entry);
	void biotaActionTrigger(ClientMob *mob, AIBase *aiBase, bool isRun);	//������Ϊ������

private:
	std::vector<AITaskEntry> m_TaskEntries;
	AITaskEntryTable m_RunningTaskEntries;
	std::vector<AITaskEntry> m_NewRun;
	int m_TickCount;
	int m_TickRate;
	
	ClientMob *m_pMobActor;
}; //tolua_exports

#endif
