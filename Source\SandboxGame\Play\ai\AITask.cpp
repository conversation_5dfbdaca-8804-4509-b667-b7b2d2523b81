#include "AITask.h"
#include "ClientMob.h"
#include "ObserverEventManager.h"
#include "LivingAttrib.h"

AITask::AITask(ClientMob* mob) :m_TickCount(0), m_TickRate(3), m_pMobActor(mob), m_RunningTaskEntries(100) {}

AITask::~AITask() 
{
	std::vector<AITaskEntry>::iterator iter = m_TaskEntries.begin();
	while(iter != m_TaskEntries.end())
	{
		ENG_DELETE(iter->m_pAIBase);
		iter++;
	}
}

void AITask::addTask(int iPriority, AIBase *pAIBase)
{
	AITaskEntry stEntry(iPriority, pAIBase);
	m_TaskEntries.push_back(stEntry);

	return;
}

void AITask::removeTask(AIBase *pAIBase)
{
	std::vector<AITaskEntry>::iterator iter = m_TaskEntries.begin();
	while(iter != m_TaskEntries.end())
	{
		if (iter->m_pAIBase == pAIBase)
		{
			AITaskEntry& entry = *iter;
			if (NULL != m_RunningTaskEntries.find(entry))
			{
				iter->m_pAIBase->reset();
				m_RunningTaskEntries.erase(entry);
			}
			ENG_DELETE(iter->m_pAIBase);
			iter = m_TaskEntries.erase(iter);
		}
		else
			iter++;
	}

	return;
}

void AITask::onUpdateTasks()
{
	OPTICK_EVENT();
	if (!m_pMobActor)
		return;

	LivingAttrib* pAttrib = m_pMobActor->getLivingAttrib();
	if (pAttrib)
	{
		if (pAttrib->hasBuff(SWIMMY_BUFF) ||
			pAttrib->hasBuff(BUBBLE_SLEEP_BUFF) ||
			pAttrib->hasStatusEffect(STATUS_EFFECT_DROP) ||
			pAttrib->hasStatusEffect(STATUS_EFFECT_PERCIPIENCE))
		{
			return;
		}
	}

	m_TickCount++;
	m_NewRun.clear();	

	if(0 == (m_TickCount % m_TickRate))
	{
		AITaskEntry* pEntry = nullptr;
		for (size_t i = 0; i < m_TaskEntries.size();)
		{
			AITaskEntry& entry = m_TaskEntries[i];
			if (NULL != m_RunningTaskEntries.find(entry))
			{
				if (canUse(entry) && canContinue(entry))
				{
					++i;
					continue;
				}

				if (entry.m_pAIBase) entry.m_pAIBase->reset();
				m_RunningTaskEntries.erase(entry);
			}

			if(canUse(entry) && ( (entry.m_pAIBase && entry.m_pAIBase->canInterruptInteract()) || /*!m_pMobActor->isInteracting()*/ !m_pMobActor->EXEC_USEMODULE(isInteracting)))
			{
				bool isRun = entry.m_pAIBase->willRun();
				biotaActionTrigger(m_pMobActor, entry.m_pAIBase, isRun);
				if(isRun)
				{
					m_RunningTaskEntries.insert(entry, true);
					m_NewRun.push_back(entry);
					if (entry.m_pAIBase->canInterruptInteract() && /*m_pMobActor->isInteracting()*/m_pMobActor->EXEC_USEMODULE(isInteracting))
					{
						//m_pMobActor->closeDialogue();
						m_pMobActor->EXEC_USEMODULE(closeDialogue);
					}
				}
			}

			++i;
		}
	}
	else
	{
		AITaskEntryTable::Element* iter = m_RunningTaskEntries.iterate(NULL);
		for (; iter != NULL; )
		{
			AITaskEntry& entry = iter->key;
			if (entry.m_pAIBase == nullptr)
			{
				iter = m_RunningTaskEntries.erase(entry);
				continue;
			}

			if (entry.m_pAIBase->continueRun())
			{
				iter = m_RunningTaskEntries.iterate(iter);
			}
			else
			{
				entry.m_pAIBase->reset();
				iter = m_RunningTaskEntries.erase(entry);
			}
		}
	}

	for (size_t i = 0; i != m_NewRun.size(); ++i)
	{
		AITaskEntry& entry = m_NewRun[i];
		if (entry.m_pAIBase)
		{
			entry.m_pAIBase->start();
		}
	}

	if (m_pMobActor->isDead())
	{
		return;
	}

	AITaskEntryTable::Element* iterRun = m_RunningTaskEntries.iterate(NULL);
	for (; iterRun != NULL; iterRun = m_RunningTaskEntries.iterate(iterRun))
	{
		AITaskEntry& entry = iterRun->key;
		if (entry.m_pAIBase)
			entry.m_pAIBase->update();
	}
}

bool AITask::canContinue(AITaskEntry &stAITaskEntry)
{
	if (!m_pMobActor)
		return false;

	if ((!stAITaskEntry.m_pAIBase->canInterruptedByInteract() || /*!m_pMobActor->isInteracting()*/ !m_pMobActor->EXEC_USEMODULE(isInteracting)) && stAITaskEntry.m_pAIBase->continueRun())
	{
		return true;
	}

	return false;	
}

bool AITask::isEntryRunning(const AITaskEntry &entry)
{
	return m_RunningTaskEntries.find(entry) != nullptr;
}

bool AITask::canUse(AITaskEntry &input_entry)
{
	AITaskEntryTable::Element* iterRun = m_RunningTaskEntries.iterate(NULL);
	for (; iterRun != NULL; iterRun = m_RunningTaskEntries.iterate(iterRun))
	{
		AITaskEntry& entry = iterRun->key;
		if (entry == input_entry) continue;

		if (input_entry.m_bPriority >= entry.m_bPriority)
		{
			if (!areTasksCompatible(input_entry.m_pAIBase, entry.m_pAIBase))
			{
				return false;
			}
		}
		else
		{
			if(!entry.m_pAIBase->isInterruptible())
			{
				return false;
			}
		}
	}


	return true;
}

bool AITask::areTasksCompatible(AIBase *pAIBase1, AIBase *pAIBase2)
{
	return (pAIBase1->getMutexBits() & pAIBase2->getMutexBits()) == 0;
}

void AITask::clearAllRunningTasks()
{
	AITaskEntryTable::Element* iterRun = m_RunningTaskEntries.iterate(NULL);
	for (; iterRun != NULL; iterRun = m_RunningTaskEntries.iterate(iterRun))
	{
		AITaskEntry& entry = iterRun->key;
		if (entry.m_pAIBase) {
			entry.m_pAIBase->reset();
		}
	}

	m_RunningTaskEntries.clear();
}

bool AITask::canInterruptedByInteract()
{
	AITaskEntryTable::Element* iterRun = m_RunningTaskEntries.iterate(NULL);
	for (; iterRun != NULL; iterRun = m_RunningTaskEntries.iterate(iterRun))
	{
		AITaskEntry& entry = iterRun->key;
		if (entry.m_pAIBase && !entry.m_pAIBase->canInterruptedByInteract())
			return false;
	}

	return true;
}

void AITask::clearAllTasks()
{
	while (!m_TaskEntries.empty())
	{
		removeTask(m_TaskEntries.begin()->m_pAIBase);
	}
}

void AITask::biotaActionTrigger(ClientMob *mob, AIBase *aiBase, bool isRun)
{
	if(!mob)
		return;

	AI_MOTION_TYPE motion = aiBase->getMotionType();
	AI_MOTION_TYPE mobMT = mob->GetMotionType();
	if(!isRun) 
	{
		if( mobMT == motion && mobMT != IDLE)
		{
			mob->SetMotionType(IDLE);

			// �۲����¼��ӿ�
			ObserverEvent obevent;
			obevent.SetData_EventObj(mob->getObjId());
			obevent.SetData_ActorMotion(OBAMIDLE);
			obevent.SetData_Actor(mob->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeMotion", &obevent);
		}
		return;
	}

	if(mobMT == motion)
		return;
	else
	{
		OBACTORMOTION obam = OBAMIDLE;

		mob->SetMotionType(motion);
		switch(motion)
		{
		case IDLE:
			obam = OBAMIDLE;
			break;
		case LOOKIDLE:
			obam = OBAMSTANDBY;
			break;
		case WANDER:
			obam = OBAMSTROLL;
			break;
		case ATK_MELEE:
			obam = OBAMATK_MELEE;
			break;
		case ATK_REMOTE:
			obam = OBAMATK_REMOTE;
			break;
		case FOLLOW_OWNER:
		case FOLLOW_PARENT:
			obam = OBAMFOLLOW;
			break;
		case SWIMMING:
			obam = OBAMSWIM;
			break;
		case INJURED_PANIC:
		case BUFF_PANIC:
		case PLAYER_PANIC:
		case FISH_PANIC:
		case SEPARATE_PANIC:
			obam = OBAMRUN_AWAY;
			break;
		case SELF_BOMB:
			obam = OBAMSELF_BOMB;
			break;
		case FKY_ITEM_ATTRACTED:
		case FKY_BLOCK_ATTRACTED:
		case BE_ATTRACTED:
			obam = OBAMBEATTRACTED;
			break;
		case COPULATION:
			obam = OBAMCOPULATION;
			break;
		default:
			return;	
		}

		// ������Ϊ�¼��ӿ�
		ObserverEvent obevent;
		obevent.SetData_EventObj(mob->getObjId());
		obevent.SetData_ActorMotion(obam);
		obevent.SetData_Actor(mob->getDefID());
		GetObserverEventManager().OnTriggerEvent("Actor.ChangeMotion", &obevent);
	}

	
}

void AITask::OnCollideWithPlayer(ClientPlayer* player)
{
	for (const auto& it : m_TaskEntries)
	{
		if (it.m_pAIBase)
			it.m_pAIBase->OnCollideWithPlayer(player);
	}
}