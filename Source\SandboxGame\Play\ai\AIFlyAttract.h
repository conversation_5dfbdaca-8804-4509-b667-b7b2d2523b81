#pragma once
#include "AIBase.h"
#include "world_types.h"


class LivingLocoMotion;
class ActorLiving;
//飞行 吸引 

class AIFlyAttract :public AIBase //tolua_exports
{ //tolua_exports
public:
	const static int LanternRiddleBirdId = 3897;
	//tolua_begin
	AIFlyAttract(ClientMob *pActor, int iDist, int iFollowDist, int iTimeOut, int followCnt = 3, int height = 1);
	~AIFlyAttract();
	virtual bool willRun();
	void start();
	bool continueRun();
	void reset();
	virtual void update();

	bool canAttractMe(ActorLiving *living);
	virtual AI_MOTION_TYPE getMotionType() { return FKY_ITEM_ATTRACTED; }
	//tolua_end
private:
	LivingLocoMotion * m_pLivingLocomotion;
	float m_FindDist;
	int m_FollowDist;
	int m_timeOut;
	WORLD_ID m_TargetID;
	int m_StopDist;
	bool m_HasArrive;
	int m_FollowCnt;
	int m_TickCnt;
	int m_height;
};//tolua_exports

