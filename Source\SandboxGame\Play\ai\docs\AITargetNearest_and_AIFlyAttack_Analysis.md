# AITargetNearest 和 AIFlyAttack AI 行为分析文档

## 📋 概述

本文档详细分析了 AITargetNearest 和 AIFlyAttack 两个 AI 组件的工作机制、参数配置和协作关系。

## 🎯 AITargetNearest - 目标搜索与锁定 AI

### 基本功能

- **职责**: 搜索、锁定和监控攻击目标
- **继承**: 继承自 AITarget 基类
- **互斥位**: 设置为 1，与其他目标 AI 互斥

### 构造函数参数

```cpp
AITargetNearest(ClientMob *pActor, int iChance, bool iCheckSight, float brightNess, float minhp, int bossEffect);
```

| 参数        | 类型        | 说明             |
| ----------- | ----------- | ---------------- |
| pActor      | ClientMob\* | 生物指针         |
| iChance     | int         | 触发概率(0-100)  |
| iCheckSight | bool        | 是否需要视线检查 |
| brightNess  | float       | 亮度要求         |
| minhp       | float       | 目标最小血量     |
| bossEffect  | int         | Boss 效果 ID     |

### Lua 调用方式

```lua
actor:addAiTaskTargetNearest(priority, chance, checkSight, minHP)
-- 示例：
actor:addAiTaskTargetNearest(1, 0, true, 0.0)
```

### 核心工作流程

#### 1. 目标搜索 (willRun)

```cpp
ClientActor *target = m_pMobActor->getActorMgr()->selectNearPlayer(
    m_pMobActor->getLocoMotion()->getPosition(),
    m_pMobActor->getViewDist()
);
```

**搜索逻辑**:

- 🔍 在视野范围内搜索所有玩家
- 📏 选择距离最近的合适目标
- ✅ 通过 isSuitableTarget 验证目标有效性
- 🔒 锁定目标 ID 到 m_Target

#### 2. 目标锁定 (start)

```cpp
targetComponent->setTargetByID(m_Target);
m_findNextTargetTicks = 3000; // 3000tick后重新寻找
```

#### 3. 持续监控 (continueRun)

**监控条件**:

- 💀 目标是否死亡
- 🛡️ 目标是否无敌
- 🏃 目标是否超出追踪距离
- 👁️ 视线检查(如果启用)

**视线检查机制**:

```cpp
if (m_CheckSight) {
    if (!canSee(target)) {
        m_CheckSightCount++;
        if (m_CheckSightCount > 60) { // 3秒看不见
            return false; // 放弃目标
        }
    }
}
```

### 目标选择算法

```cpp
// selectNearPlayer的核心算法
float fDist = 99999999.0f;
for (auto player : players) {
    float dist = (player->getPosition() - pos).length();
    if (dist < range && dist < fDist && isSuitable(player)) {
        fDist = dist;
        pTarget = player; // 更新最近目标
    }
}
```

**特点**:

- 🎯 **距离优先**: 总是选择最近的目标
- 🔒 **专一追踪**: 锁定后不会随意切换
- ⏰ **定时重选**: 只在特定条件下重新搜索

## ✈️ AIFlyAttack - 飞行攻击 AI

### 基本功能

- **职责**: 执行飞行追踪和攻击
- **依赖**: 需要其他 AI(如 TargetNearest)提供目标
- **移动类型**: 仅适用于飞行生物(FlyLoc)

### 构造函数参数

```cpp
AIFlyAttack(ClientMob *pActor, bool trace, float speedMulty, int buffid, int bufflevel);
```

| 参数       | 类型        | 说明               |
| ---------- | ----------- | ------------------ |
| pActor     | ClientMob\* | 生物指针           |
| trace      | bool        | 是否实时追踪目标   |
| speedMulty | float       | 速度倍数           |
| buffid     | int         | 攻击附带的 Buff ID |
| bufflevel  | int         | Buff 等级          |

### Lua 调用方式

```lua
actor:addAiTaskFlyAttack(priority, trace, speedMulty, buffid, bufflevel)
-- 示例：
actor:addAiTaskFlyAttack(2, true, 1.5, 0, 0)
```

### 核心工作流程

#### 1. 触发条件检查 (willRun)

```cpp
ClientActor *target = targetComponent->getTarget(); // 获取目标
if (!target || target->isDead() || target->isInWater()) return false;
if (!m_pMobActor->getVision()->canSee(target)) return false;
```

**检查项目**:

- 🎯 是否有有效目标
- 💀 目标是否存活
- 🌊 目标是否在水中
- 👁️ 是否能看见目标

#### 2. 飞行启动 (start)

```cpp
if (m_Trace) {
    // 实时追踪模式
    m_pLivingLocomotion->m_MoveTarget = target->getPosition() + 100 * Vector3f::yAxis;
} else {
    // 固定位置模式
    m_pLivingLocomotion->m_MoveTarget = m_InitialPos + 100 * Vector3f::yAxis;
}
m_pLivingLocomotion->setBehaviorOn(BehaviorType::Pursuit);
```

#### 3. 持续更新 (update)

```cpp
if (m_Trace) {
    // 实时更新飞行目标
    WCoord currentPos = target->getPosition();
    m_pLivingLocomotion->m_MoveTarget = currentPos + 100 * Vector3f::yAxis;
}

// 攻击逻辑
if (m_AttackTick <= 0 && atkDist(target) && canSee(target)) {
    m_AttackTick = 30; // 1.5秒冷却
    m_pMobActor->attackActor(target);
}
```

### trace 参数的影响

#### trace=true (实时追踪)

- ✅ **动态调整**: 实时跟随目标移动
- ✅ **高精度**: 很难被躲避
- ✅ **自然行为**: 像真实的追踪攻击
- ❌ **计算开销**: 每帧更新位置

#### trace=false (固定追踪)

- ✅ **低开销**: 不需要持续更新
- ✅ **可预测**: 玩家可以通过移动躲避
- ❌ **可能失误**: 攻击过时的位置
- ❌ **不够智能**: 行为较为机械

### 攻击机制

#### 距离检查

```cpp
bool atkDist(ClientActor *target) {
    double dist = getDef()->AttackDistance * BLOCK_SIZE;
    return getSquareDistToPos(target->getPosition()) <= dist*dist;
}
```

#### 攻击执行

- **攻击类型**: 必须是 ATTACK_PUNCH(近战)
- **攻击冷却**: 30tick (1.5 秒)
- **Buff 效果**: 可选的状态效果
- **飞行高度**: 目标上方 100 单位(5 格)

## 🤝 AI 协作机制

### 工作流程

```mermaid
sequenceDiagram
    participant TN as TargetNearest
    participant TC as ToAttackTargetComponent
    participant FA as FlyAttack
    participant P as Player

    TN->>TN: 搜索最近玩家
    TN->>TC: setTarget(player)
    TN->>TN: 持续监控目标

    FA->>TC: getTarget()
    TC->>FA: 返回player对象

    alt trace=true
        FA->>P: 获取实时位置
        FA->>FA: 更新飞行目标
    else trace=false
        FA->>FA: 飞向固定位置
    end

    FA->>P: 执行攻击
```

### 职责分工

| AI 组件       | 主要职责          | 关键参数             |
| ------------- | ----------------- | -------------------- |
| TargetNearest | 🎯 目标搜索与锁定 | CheckSight, 触发概率 |
| FlyAttack     | ✈️ 飞行追踪与攻击 | trace, 速度倍数      |

### 参数独立性

- ✅ **TargetNearest 的 CheckSight**: 控制目标锁定的视线要求
- ✅ **FlyAttack 的 trace**: 控制飞行追踪的实时性
- 🔄 **互不影响**: 两个参数分别控制各自的行为逻辑

## 📊 配置示例

### 标准攻击型配置

```lua
function SetAttackAI(actor)
    actor:addAiTaskTargetNearest(1, 0, true, 0.0)  -- 需要视线的目标锁定
    actor:addAiTaskFlyAttack(2, true, 1.5, 0, 0)   -- 实时追踪攻击
end
```

### 巡逻型配置

```lua
function SetPatrolAI(actor)
    actor:addAiTaskTargetNearest(2, 50, true, 0.0) -- 50%概率触发
    actor:addAiTaskFlyAttack(3, false, 1.2, 0, 0)  -- 固定位置攻击
    actor:addAiTaskRandFly(1, 200, 5, 1000)        -- 主要进行随机飞行
end
```

### 精英怪配置

```lua
function SetEliteAI(actor)
    actor:addAiTaskTargetNearest(1, 0, false, 0.0) -- 无视障碍物锁定
    actor:addAiTaskFlyAttack(2, true, 2.0, 44, 1)  -- 高速追踪+毒性攻击
end
```

## 🎮 性能考虑

### 优化建议

1. **合理设置触发概率**: 避免过于频繁的目标搜索
2. **权衡 trace 参数**: 根据游戏需求选择追踪模式
3. **控制视野范围**: 避免过大的搜索范围
4. **适当的攻击冷却**: 平衡攻击频率和性能

### 常见问题

- **只追踪不攻击**: 检查 AttackDistance 配置
- **攻击过于频繁**: 调整攻击冷却时间
- **追踪过于智能**: 考虑关闭 trace 或降低速度倍数
- **目标切换频繁**: 检查视线检查和追踪距离设置

## 🔧 调试和故障排除

### 常见问题诊断

#### 问题 1: 飞鸡只追踪不攻击

**可能原因**:

- AttackDistance 设置过小
- 攻击类型不是 ATTACK_PUNCH
- 目标在攻击范围外

**解决方案**:

```csv
// monster.csv中调整攻击距离
AttackDistance: 1.6 → 2.5  // 增加攻击范围
```

#### 问题 2: 目标切换过于频繁

**可能原因**:

- 视线检查过于严格
- 追踪距离设置过小
- AI 优先级冲突

**解决方案**:

```lua
-- 降低TargetNearest的触发频率
actor:addAiTaskTargetNearest(1, 30, true, 0.0) -- 30%概率
```

#### 问题 3: 追踪过于智能

**可能原因**:

- trace=true 导致实时追踪
- 速度倍数过高

**解决方案**:

```lua
-- 关闭实时追踪或降低速度
actor:addAiTaskFlyAttack(2, false, 1.2, 0, 0)
```

### 调试工具和方法

#### 1. 日志输出

```cpp
// 在AITargetNearest::willRun中添加
LOG_INFO("TargetNearest: Found target %lld at distance %.2f",
         target->getObjId(), distance);

// 在AIFlyAttack::update中添加
LOG_INFO("FlyAttack: Attacking target at pos(%.1f,%.1f,%.1f)",
         target->getPosition().x, target->getPosition().y, target->getPosition().z);
```

#### 2. 可视化调试

```cpp
// 显示攻击范围
if (DEBUG_AI) {
    float attackRange = getDef()->AttackDistance * BLOCK_SIZE;
    DrawDebugSphere(getPosition(), attackRange, Color::Red);
}
```

## 📈 性能分析

### 计算复杂度

#### TargetNearest

- **搜索复杂度**: O(n) - n 为玩家数量
- **触发频率**: 根据概率参数和 AI 优先级
- **内存占用**: 低 - 只存储目标 ID

#### FlyAttack

- **trace=true**: O(1) - 每帧更新位置
- **trace=false**: O(1) - 固定计算
- **攻击检查**: O(1) - 距离和视线检查

### 优化策略

#### 1. 批量处理

```cpp
// 建议：将多个AI的目标搜索合并
class AITargetManager {
    void updateAllTargets(std::vector<ClientMob*>& mobs);
};
```

#### 2. 缓存机制

```cpp
// 缓存视线检查结果
class VisionCache {
    bool canSeeInAICache(ClientActor* target);
    void updateCache(); // 每几帧更新一次
};
```

#### 3. 距离预筛选

```cpp
// 先进行粗略距离检查，再进行精确计算
if (roughDistance > maxRange * 1.5f) continue;
```

## 🎯 最佳实践

### 1. AI 优先级设计

```lua
-- 推荐的优先级结构
actor:addAiTaskSwimming(0)           -- 基础移动
actor:addAiTaskTargetHurtee(1, true) -- 反击优先
actor:addAiTaskTargetNearest(2, 0, true, 0.0) -- 主动攻击
actor:addAiTaskFlyAttack(3, true, 1.5, 0, 0)  -- 飞行攻击
actor:addAiTaskRandFly(4, 200, 5, 1000)       -- 巡逻飞行
actor:addAiTaskWander(5, 1.0)                 -- 地面游荡
actor:addAiTaskLookIdle(6)                    -- 待机动作
```

### 2. 参数调优指南

#### 新手友好配置

```lua
-- 给新手玩家更多逃脱机会
actor:addAiTaskTargetNearest(2, 40, true, 0.0)  -- 40%触发概率
actor:addAiTaskFlyAttack(3, false, 1.2, 0, 0)   -- 固定位置攻击
```

#### 挑战性配置

```lua
-- 为高级玩家提供挑战
actor:addAiTaskTargetNearest(1, 0, false, 0.0)  -- 无视障碍
actor:addAiTaskFlyAttack(2, true, 2.0, 44, 1)   -- 高速+毒性
```

#### 平衡性配置

```lua
-- 平衡的游戏体验
actor:addAiTaskTargetNearest(1, 0, true, 0.0)   -- 需要视线
actor:addAiTaskFlyAttack(2, true, 1.5, 0, 0)    -- 适中追踪
```

### 3. 生物类型适配

#### 小型飞行生物(蜜蜂、萤火虫)

```lua
-- 快速但脆弱
actor:addAiTaskTargetNearest(1, 20, true, 0.0)  -- 低触发概率
actor:addAiTaskFlyAttack(2, true, 2.5, 44, 1)   -- 高速+毒性
```

#### 中型飞行生物(飞鸡)

```lua
-- 平衡型
actor:addAiTaskTargetNearest(1, 0, true, 0.0)   -- 标准配置
actor:addAiTaskFlyAttack(2, true, 1.5, 0, 0)    -- 适中速度
```

#### 大型飞行生物(龙类)

```lua
-- 强力但缓慢
actor:addAiTaskTargetNearest(1, 0, false, 0.0)  -- 强力锁定
actor:addAiTaskFlyAttack(2, true, 1.0, 0, 0)    -- 缓慢但稳定
```

## 📚 相关文档

### 相关 AI 组件

- `AITarget.cpp` - 目标 AI 基类
- `AIRandomFly.cpp` - 随机飞行 AI
- `AIWatchClosest.cpp` - 观察最近目标 AI
- `AITargetHurtee.cpp` - 反击目标 AI

### 相关系统

- `ToAttackTargetComponent` - 攻击目标组件
- `ActorVision` - 视觉系统
- `LivingLocoMotion` - 生物移动系统
- `ClientActorManager` - 角色管理器

### 配置文件

- `monster.csv` - 生物属性配置
- `mobs.lua` - AI 行为配置

---

_文档版本: 1.0_
_最后更新: 2025-01-02_
_作者: AI Assistant_
