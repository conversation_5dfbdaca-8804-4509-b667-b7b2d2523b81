#include "AIFlyPointToPoint.h"
#include "ClientMob.h"
#include "LivingLocoMotion.h"
#include "world.h"
#include "ActorBody.h"
#include "coreMisc.h"

using namespace MNSandbox;

AIFlyPointToPoint::AIFlyPointToPoint(ClientMob *pActor, const WCoord& startPos, const WCoord& endPos, 
									 float speed, int avoidanceRange, float avoidanceHeight)
	: AIBase(pActor)
	, m_StartPos(startPos)
	, m_EndPos(endPos)
	, m_CurrentTarget(endPos)
	, m_Speed(speed)
	, m_AvoidanceRange(avoidanceRange)
	, m_AvoidanceHeight(avoidanceHeight)
	, m_HasReachedTarget(false)
	, m_StuckCounter(0)
	, m_UpdateCounter(0)
	, m_IsAvoiding(false)
	, m_AvoidanceTicks(0)
{
	setMutexBits(3);
	m_pLivingLocomotion = dynamic_cast<LivingLocoMotion*>(m_pMobActor->getLocoMotion());
	
	if (m_pLivingLocomotion && m_pLivingLocomotion->getLocoMotionType() != MoveAbilityType::FlyLoc)
	{
		LOG_INFO("AIFlyPointToPoint can only be applied on Fly mob");
	}
	
	m_LastPosition = m_pMobActor->getPosition();
}

AIFlyPointToPoint::~AIFlyPointToPoint()
{
}

bool AIFlyPointToPoint::willRun()
{
	if (!m_pLivingLocomotion)
		return false;
		
	// 检查是否已到达目标
	if (m_HasReachedTarget)
		return false;
		
	// 检查目标位置是否有效
	WCoord currentPos = m_pMobActor->getPosition();
	float distToTarget = (m_EndPos - currentPos).length();
	
	// 如果距离很近，认为已到达
	if (distToTarget < 100.0f) // 1个方块的距离
	{
		m_HasReachedTarget = true;
		return false;
	}
	
	// 检查是否被攻击，如果被攻击则暂停导航
	if (m_pMobActor->getBeHurtTarget())
		return false;
		
	return true;
}

bool AIFlyPointToPoint::continueRun()
{
	if (!willRun())
		return false;
		
	// 检查是否卡住太久
	if (m_StuckCounter > 200) // 大约10秒
	{
		return false;
	}
	
	return true;
}

void AIFlyPointToPoint::start()
{
	if (!m_pLivingLocomotion)
		return;
		
	m_HasReachedTarget = false;
	m_StuckCounter = 0;
	m_UpdateCounter = 0;
	m_IsAvoiding = false;
	m_AvoidanceTicks = 0;
	
	// 计算智能目标高度
	m_CurrentTarget = calculateSmartHeight(m_EndPos);
	
	m_pLivingLocomotion->m_HasTarget = true;
	m_pLivingLocomotion->m_MoveTarget = m_CurrentTarget;
	m_pLivingLocomotion->setBehaviorOn(BehaviorType::Pursuit);
	m_pLivingLocomotion->m_SpeedMultiple = m_Speed;
	
	// 播放飞行动画
	ActorBody *pActorBody = m_pMobActor->getBody();
	if (pActorBody && !pActorBody->hasAnimPlaying(SEQ_WALK))
	{
		pActorBody->setCurAnim(SEQ_WALK, 0);
	}
	
	m_LastPosition = m_pMobActor->getPosition();
}

void AIFlyPointToPoint::reset()
{
	if (m_pLivingLocomotion)
	{
		m_pLivingLocomotion->setBehaviorOff(BehaviorType::Pursuit);
		m_pLivingLocomotion->m_HasTarget = false;
		m_pLivingLocomotion->m_SpeedMultiple = 1.0f;
	}
	
	m_HasReachedTarget = false;
	m_StuckCounter = 0;
	m_IsAvoiding = false;
	m_AvoidanceTicks = 0;
}

void AIFlyPointToPoint::update()
{
	if (!m_pLivingLocomotion)
		return;
		
	m_UpdateCounter++;
	
	// 每20帧检查一次状态
	if (m_UpdateCounter % 20 == 0)
	{
		WCoord currentPos = m_pMobActor->getPosition();
		
		// 检查是否卡住
		float moveDistance = (currentPos - m_LastPosition).length();
		if (moveDistance < 10.0f) // 移动距离太小
		{
			m_StuckCounter++;
		}
		else
		{
			m_StuckCounter = 0;
		}
		
		m_LastPosition = currentPos;
		
		// 如果卡住，尝试重新规划路径
		if (m_StuckCounter > 5)
		{
			m_CurrentTarget = calculateAvoidancePath();
			m_pLivingLocomotion->m_MoveTarget = m_CurrentTarget;
		}
	}
	
	// 避障逻辑
	if (m_AvoidanceTicks > 0)
	{
		m_AvoidanceTicks--;
		// 继续避障移动
		m_pLivingLocomotion->m_MoveTarget = m_AvoidanceTarget;
	}
	else
	{
		// 检查前方是否有障碍物
		WCoord currentPos = m_pMobActor->getPosition();
		WCoord directionCoord = (m_CurrentTarget - currentPos);
		directionCoord.y = 0; // 只检查水平方向
		
		if (directionCoord.length() > 1.0f)
		{
			Rainbow::Vector3f direction(directionCoord.x, 0, directionCoord.z);
			direction = MINIW::Normalize(direction);
			
			if (detectObstacle(WCoord(direction.x, direction.y, direction.z), m_AvoidanceRange))
			{
				// 发现障碍物，开始避障
				m_IsAvoiding = true;
				m_AvoidanceTarget = calculateAvoidancePath();
				m_AvoidanceTicks = 60; // 避障3秒
				m_pLivingLocomotion->m_MoveTarget = m_AvoidanceTarget;
			}
			else
			{
				// 没有障碍物，直接飞向目标
				m_IsAvoiding = false;
				m_CurrentTarget = calculateSmartHeight(m_EndPos);
				m_pLivingLocomotion->m_MoveTarget = m_CurrentTarget;
			}
		}
	}
	
	// 确保始终启用追击行为
	if (!m_pLivingLocomotion->isBehaviorOn(BehaviorType::Pursuit))
	{
		m_pLivingLocomotion->setBehaviorOn(BehaviorType::Pursuit);
	}
}

void AIFlyPointToPoint::setTargetPos(const WCoord& newTarget)
{
	m_EndPos = newTarget;
	m_HasReachedTarget = false;
	m_StuckCounter = 0;
	m_IsAvoiding = false;
	m_AvoidanceTicks = 0;
	
	if (m_pLivingLocomotion)
	{
		m_CurrentTarget = calculateSmartHeight(m_EndPos);
		m_pLivingLocomotion->m_MoveTarget = m_CurrentTarget;
	}
}

bool AIFlyPointToPoint::hasReachedTarget() const
{
	return m_HasReachedTarget;
}

bool AIFlyPointToPoint::detectObstacle(const WCoord& direction, int range)
{
	if (!m_pMobActor->getWorld())
		return false;
		
	WCoord currentPos = m_pMobActor->getPosition();
	WCoord checkPos = currentPos;
	
	// 向前检查多个方块
	for (int i = 1; i <= range; i++)
	{
		checkPos = currentPos + direction * (BLOCK_SIZE * i);
		WCoord blockCoord = CoordDivBlock(checkPos);
		
		// 检查该位置及其上方的方块
		for (int y = 0; y < 3; y++)
		{
			WCoord testCoord = blockCoord + WCoord(0, y, 0);
			int blockId = m_pMobActor->getWorld()->getBlockID(testCoord);
			
			// 如果是实体方块，则认为有障碍
			if (blockId != 0 && blockId != 8 && blockId != 9) // 排除空气、水、岩浆
			{
				return true;
			}
		}
	}
	
	return false;
}

WCoord AIFlyPointToPoint::calculateAvoidancePath()
{
	WCoord currentPos = m_pMobActor->getPosition();
	WCoord targetDirectionCoord = (m_EndPos - currentPos);
	Rainbow::Vector3f targetDirection(targetDirectionCoord.x, targetDirectionCoord.y, targetDirectionCoord.z);
	targetDirection = MINIW::Normalize(targetDirection);
	
	// 尝试向上绕行
	WCoord avoidancePos = currentPos + WCoord(targetDirection.x, targetDirection.y, targetDirection.z) * (BLOCK_SIZE * 2) + WCoord(0.0, m_AvoidanceHeight, 0.0);
	
	// 检查上方路径是否清晰
	if (isPathClear(currentPos, avoidancePos))
	{
		return avoidancePos;
	}
	
	// 如果上方不行，尝试左右绕行
	WCoord rightDir = WCoord(-targetDirection.z, 0, targetDirection.x); // 垂直向量
	WCoord leftAvoidance = currentPos + rightDir * (BLOCK_SIZE * 2) + WCoord(0.0, m_AvoidanceHeight * 0.5f, 0.0);
	WCoord rightAvoidance = currentPos - rightDir * (BLOCK_SIZE * 2) + WCoord(0.0, m_AvoidanceHeight * 0.5f, 0.0);
	
	if (isPathClear(currentPos, leftAvoidance))
	{
		return leftAvoidance;
	}
	else if (isPathClear(currentPos, rightAvoidance))
	{
		return rightAvoidance;
	}
	
	// 如果都不行，就向上飞
	return currentPos + WCoord(0.0, m_AvoidanceHeight * 2, 0.0);
}

WCoord AIFlyPointToPoint::calculateSmartHeight(const WCoord& targetPos)
{
	if (!m_pMobActor->getWorld())
		return targetPos;
		
	WCoord adjustedTarget = targetPos;
	WCoord blockCoord = CoordDivBlock(targetPos);
	
	// 从目标位置向下寻找地面
	int maxDepth = 10;
	for (int y = 0; y < maxDepth; y++)
	{
		WCoord checkCoord = blockCoord - WCoord(0, y, 0);
		int blockId = m_pMobActor->getWorld()->getBlockID(checkCoord);
		
		if (blockId != 0 && blockId != 8 && blockId != 9) // 找到实体方块
		{
			// 在实体方块上方3个方块高度飞行
			adjustedTarget.y = (checkCoord.y + 1) * BLOCK_SIZE + 150.0f;
			break;
		}
	}
	
	// 确保最小飞行高度
	float minHeight = m_StartPos.y;
	if (adjustedTarget.y < minHeight)
	{
		adjustedTarget.y = minHeight;
	}
	
	return adjustedTarget;
}

bool AIFlyPointToPoint::isPathClear(const WCoord& from, const WCoord& to)
{
	if (!m_pMobActor->getWorld())
		return true;
		
	WCoord directionCoord = (to - from);
	Rainbow::Vector3f direction(directionCoord.x, directionCoord.y, directionCoord.z);
	direction = MINIW::Normalize(direction);
	float distance = directionCoord.length();
	int steps = (int)(distance / (BLOCK_SIZE * 0.5f)); // 每半个方块检查一次
	
	for (int i = 0; i <= steps; i++)
	{
		float stepDistance = distance * i / steps;
		WCoord checkPos = from + WCoord(direction.x * stepDistance, direction.y * stepDistance, direction.z * stepDistance);
		WCoord blockCoord = CoordDivBlock(checkPos);
		
		int blockId = m_pMobActor->getWorld()->getBlockID(blockCoord);
		if (blockId != 0 && blockId != 8 && blockId != 9) // 有实体方块阻挡
		{
			return false;
		}
	}
	
	return true;
}